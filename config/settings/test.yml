domain:
  admin: admin.lawson
  corporation: corporation.lawson
  landing_page: "landing-workz.domain"
  staff: "workz"
  api: api.lawson
test:
  mailer:
    host: "localhost"
session:
  key: "_session_id"
  expire_time: <%= 1.days %>
  step_key: "_wz_session_id"
  step_expire_time: <%= 1.day %>
config:
  mailer:
    default_from: "<EMAIL>"
    support_email:
      default: "<EMAIL>"
is_off_my_page: false
active_my_page_departments: [5, 6, 7, 8, 10, 11, 12, 13, 15, 18, 19, 20, 3, 4, 9, 16, 17]
my_number_api:
  host: "my_number_api.com"
  domain: "http://my_number_api.com"
  password: "Ponos@1234"
  version: "/v1"
  user:
    login: "/user/login"
    refresh_token: "/user/refresh"
  partner:
    register: "/partner/register"
    list: "/partner/list"
    delete: "/partner/delete"
  default_params:
    corporation_id: "6ba7b810-9dad-11d1-80b4-00c04fd430c8"
    user_id: "6ba7b810-9dad-11d1-80b4-00c04fd430c8"
  dummies:
    partner_id: "026430e0-f092-4a47-9af3-4756bc4f6d2e"
    idv_1: "123456789018"
    idv_2: "914466486940"
    token: "eyJhbGciOiJIUzI1NiIsInR"
    refresh_token: "ciOiJIUzI1NiIsInR"
rejected_corporation_ids: []
opened_on_09_16_departments: [8, 10, 11, 18]
opened_on_09_16_start_date: "2019/09/16"
opened_on_10_28_departments: [3, 4, 9, 16, 17]
opened_on_10_28_start_date: "2019/10/28"
owner:
  default_usage_achievement_start_date: "2019/06/01"
locations_allow_store_computer: [1,2,3,4,5]
ie:
  accessible: false
fcm:
  api_key:
    ios: ""
    android: ""
app:
  under_maintenance: false
  maintenance_time: "2023年10月31日(水) 09:00~12:00"
  version:
    ios: "1.0.0"
    android: "1.0.0"
staff_web:
  under_maintenance: false
  maintenance_time: "2023年10月31日(水) 09:00~12:00"
op_center_emails: ["<EMAIL>"]
is_enable_job_category_feature: true
payment_request:
  start_use_date: "2019/08/05"
combini_haken_excluded_ids: []
insurance_receiver_ids: []
