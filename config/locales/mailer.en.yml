en:
  mailer:
    admin:
      reset_pw:
        subject: "【Lawson Staff】 Please reset password"
      block_account:
        admin:
          subject: "【Lawson Staff】 Account lock notification(OP center)"
        user:
          subject: "【Lawson Staff】 Account lock notification(corporation)"
        staff:
          subject: "【Lawson Staff】 Account lock notification(staff)"
      reset_pw_user:
        subject: "【Lawson Staff】 Please reset password"
      staff_reject:
        subject: "【Lawson Staff】 Rejected"
      send_mail_edit_entry:
        subject: "【Lawson Staff】 Edit entry information"
      send_mail_app_reject_entry:
        subject: "【Lawson Staff】Notification of selection results <rejected>"
      finish_staff_entry:
        subject: "【Lawson Staff】 Entry completed mail"
      request_change_profile:
        subject:
          approve: "【Lawson Staff】Approved change profile request."
          reject: "【Lawson Staff】Rejected change profile request."
      expiration_contract:
        subject: "【Lawson Staff】 Expiration contract"
      alert_update_contract:
        subject: "【Lawson Staff】 Alert update contract"
      interview_update_contract:
        subject: "【Lawson Staff】 Interview update contract"
      confirm_job_mail_for_staff:
        subject: "【Lawson Staff】 Confirm %{earliest_date}～%{latest_date}"
      confirm_job_mail_for_owner:
        subject: "【Lawson Staff】 %{office} dear matching result notification"
      news_for_owner:
        subject: "【Information】 News from Lawson Staff Co., Ltd." 
      staff_offer:
        subject: "【Lawson Staff】 %{status} Matching result notification"
      confirm_start_go_to_work:
        subject: "【Lawson Staff】%{date} Confirm advance preparation of work"
      reject_staff_apply_mail:
        subject: "【Lawson Staff】 I was not placed in work"
        detail_info:
          location_name: "Location name"
          station: "Station"
          started_at: "Started at"
          start_time: "Start time"
          end_time: "End time"
          break_time: "Break time"
      remind_staff_update_contract:
        subject: "【Lawson Staff】 Remind update contract"
      remind_staff_login:
        subject: "【Lawson Staff】Enrollment Confirmation intention"
      remind_staffs_login_to_admin:
        subject: "【Lawson Staff】Enrollment confirmation notification"
      remind_staff_login_retire:
        subject: "【Lawson Staff】Notification of retirement processing"
      remind_staffs_login_retire_to_admin:
        subject: "【Lawson Staff】Retirement processing notification"
      contract_update_request:
        subject: "【Lawson Staff】Announcement of renewal of contract"
      op_alert_staff_end_contract:
        subject: "【Lawson Staff】 Notify that will expiring employment contract"
      op_confirm_staff_profile:
        subject: "【Lawson Staff】 Profile registration completed"
      request_input_working_time:
        subject: "【Lawson Staff】 Please enter duty / break time"
      working_schedule_remind:
        subject: "【Lawson Staff】%{date} scheduled to work"
      alert_staff_end_contract:
        subject: "【Lawson Staff】Alert staff expiration contract"
      alert_staff_end_contract_by_company:
        subject: "【Lawson Staff】Alert staff expiration contract"
      op_alert_update_staff_contract:
        subject: "【Lawson Staff】 Employment contract renewal will inform nearby staff"
      op_alert_residence_end:
        subject: "【Lawson Staff】 We will inform staff closing expiration date of stay."
      staff_alert_residence_end:
        subject: "【Lawson Staff】 The expiration date of the period of stay is approaching."
      batch_request_profile:
        subject: "【Lawson Staff】 Notification of registration dossiers"
      cancel_arrangement_owner:
        subject: "【LawsonStaff】%{date} Cancel job"
      cancel_arrangement_staff:
        subject: "【LawsonStaff】%{date} Cancel work"
      owner_daily_job:
        subject: "【Lawson Staff】Daily Mail"
      daily_offer_mail:
        subject: "【Lawson Staff】Recommended job guide!"
      notify_new_jobs_from_liked_location:
        subject: "【Lawson Staff】New jobs from your favorite stores"
      notification_salary_detail:
        subject: "【Lawson Staff】 Contact of salary details %{time}"
      cancel_notification_salary_detail:
        subject: "【Lawson Staff】 Contact of cancel salary details %{time}"
      cancel_order_less_than_minimum_wage:
        subject: "【Lawson Staff】 There are matters that are lower than the minimum hourly wage"
      notification_new_rank_or_level:
        subject: "【Lawson Staff】 Next Month Rank Confirmation"
      update_insurance_by_retirement:
        subject: "About loss of insurance procedure for retired employees"
      create_new_staff_contract:
        subject: "【Lawson Staff】 Notice of contract renew"
      maintain_system:
        subject: "Notice of system maintenance"
      alert_staff_update_bank_account:
        subject: "[Lawson staff] Request for bank account correction"
      staff_update_bank_account_confirmed:
        subject: "【Lawson staff】Notification of completion of bank account change"
      end_contract_with_future_job:
        subject: "【Lawson Staff】List of staffs applying for contract out of contract period"
      reset_registration_pw:
        subject: "[work'z] Registration Completed! Please set a password"
      update_registered_profile:
        subject: "【Lawson Staff】Request for correction of registered membership detail"
      notify_join_and_reject_insurances:
        subject: "【Lawson Staff】 Notice of loss of membership"
      alert_admin_new_individual_number:
        subject: "【Lawson Staff】New My Number Registrations"
      notify_staff_returned_individual_number:
        subject: "【Lawson Staff】My Number resubmission request"
      confirm_training_job_mail_for_staff:
        subject: "【Lawson Staff】Training information!"
      alert_admin_corporation_violation_date_change:
        subject: "【Lawson Staff】Initial setting completed site list"
      notify_daily_to_admin_payment_request_histories:
        subject: "【Lawson Staff】Instant payment data list Month: %{month} Day: %{day}"
      notify_staff_payment_request_success:
        subject: "【Lawson Staff】Notification of completion of immediate payment transfer processing"
      notify_admin_staff_not_update_contract:
        subject: "【Lawson staff】Staff who selected No to renew the contract"
      notify_staff_booked_training_schedule:
        subject: "【Lawson Staff】%{location_prefecture}%{training_date_weekday} Notification of Staff training reservation"
      notify_staff_cancel_schedule:
        subject: "【Lawson Staff】%{location_prefecture} %{training_date_weekday} Notice of Absence from Training"
      notify_delete_schedule:
        subject: "【Lawson Staff】%{location_prefecture} %{training_date_weekday} Notice of training deletion"
      remind_login_mail:
        subject: "【Lawson staff】This is the work'z login URL."
      remind_staff_violation_date:
        subject: "【Violation】 We will inform you of the case where the conflict day is approaching."
      remind_staffs_over_sixty_hours_ot:
        subject: "【Work'z Over Time Limit】Notify list Staff have over 60 hours over time a month"
      remind_staffs_work_less_than_standard_hours:
        subject: "[Lawson Staff] Staff who do not work more than 18 hours a week will be notified."
      auto_restore_mail:
        subject: "[Lawson Staff] %{training_date_weekday} Notice of automatic restore training schedules"
      interview_remind_mail:
        subject: "[Lawson Staff] %{interview_date} | %{interview_time}WEB面接"
      remind_arranged_job_during_long_vacation:
        subject: "[Lawson Staff] A staff member's long-term vacation overlaps with ot arranged."
    corporation:
      order:
        subject:
          request_approve: "【Lawson Staff】 Request approve create order"
          approve: "【Lawson Staff】 Approve create order"
          cancel: "【Lawson Staff】 Cancel create order"
      active_account:
        subject: "【Lawson Staff】Please confirm account"
      request_violation_day:
        subject: "【Lawson Staff】 Please extend the violation day"
      cancel_order_less_than_minimum_wage:
        subject: "【Lawson Staff】 hourly wage change by minimum hourly revision"
      violation_day_notification:
        subject: "【Lawson Staff】 Notify the conflict date"
      cancel_order_case_for_staff:
        subject: "【LawsonStaff】%{date} Cancel work"
      staff_input_work_achievement_to_owner:
        subject: "【LawsonStaff】 Attendance correction notice"
      owner_input_work_achievement_to_staff:
        subject: "【LawsonStaff】 Attendance correction notice"
    staff:
      reset_pw:
        subject: "【Lawson Staff】Please reset password"
      cancel_order_less_than_minimum_wage:
        subject: "【Lawson Staff】 hourly wage change by the minimum hourly revision"
      cancel_job_an_hour_ago:
        subject: "【Lawson Staff】 Cancellation of work"
      join_insurance_subsection:
        subject: "[Lawson Staff] Notification of company insurance enrollment procedures"
      join_employment_insurance:
        subject: "[Lawson Staff] Notification of enrollment procedure"
      reject_employment_insurance:
        subject: "[Lawson Staff] Notification of lost employment insurance"
      reject_subsection_insurance:
        subject: "[Lawson Staff] Notification of loss of company insurance"
      reject_subsection_and_employment_insurance:
        subject: "[Lawson Staff] Notification of Employment/Company Loss Procedures"
      apply_interview_success_to_staff:
        subject: "[work'z] Interview reservation completed! Request for advance preparation"
      notify_acquired_insurance_to_admin:
        subject: "[Lawson Staff] Employment Insurance: Notification of New Acquirers"
      apply_interview_success_to_staff_has_experience:
        subject: "[Lawson Staff] Information on WEB interview and training schedule decision * Response required *"
      send_verification_otp:
        subject: "[Lawson Staff] %{otp_code} is your verification OTP!"
      send_reschedule_interview_reminder:
        subject: "【Lawson Staff】%{date_format}WEB interview non-participation"
      send_mail_entry:
        subject: "【Lawson Staff】Notification of selection results <confirmation>"
      send_mail_entry_has_exp:
        subject: "【Lawson Staff】Request for completion of user information"
      send_mail_entry_no_exp:
        subject: "【Lawson Staff】About future guidance"
      reject_training_job_mail:
        subject: "【Lawson Staff】was not placed in training"
      notify_interview_deleted:
        subject: "【Lawson Staff】%{date_format}WEB interview cancellation"
      notify_interview_applied:
        subject: "【Lawson Staff】Notification of completion of interview reservation"
      remind_staff_interview:
        subject: "【Lawson Staff】Information on Online Interview (Tomorrow %{date_format})"
      notify_staff_new_training_schedule:
        subject: "[Lawson Staff] Notice of training reservation completion"
      notify_training_schedule_booked:
        subject: "[Lawson Staff] Notice of training reservation completion"
      notify_training_schedule_deleted:
        subject: "[Lawson Staff]%{training_date_weekday} training canceled"
      notify_training_schedule_absent:
        subject: "[Lawson Staff]%{training_date_weekday}not participating in training"
      remind_staff_training:
        subject: "[Lawson Staff] Training information!"
      remind_two_hours_before_training:
        subject: "[Lawson Staff] %{training_date_weekday} Preparation confirmation for training"

  admin:
    sms:
      notify_training_schedule_applied: "[Lawson Staff] Notification of training reservation\n\n[Interview/training location]\nLocation name: %{location_name}\nLocation address: %{location_address}\n[Training Date]: %{datetime}\n[Staff Tel]: %{staff_tel}"