require "faker"

FactoryBot.define do
  factory :training_schedule_applicant do
    staff{ build_stubbed(:staff) }
    schedule_status_code{TrainingScheduleApplicant.schedule_status_codes.keys.sample}
    association :training_schedule
  end

  factory :booked_schedule_applicant, parent: :training_schedule_applicant do
    schedule_status_code{"booked"}
  end

  factory :absent_schedule_applicant, parent: :training_schedule_applicant do
    schedule_status_code{"absent_with_notice"}
  end
end
