FactoryBot.define do
  factory :training_schedule do
    location { build_stubbed(:location) }
    training_session_code{[1, 2, 3].sample}
    total_portion{rand(1..10)}
    sequence(:start_time) {|n| ServerTime.now.beginning_of_day + n.days}
    end_time{start_time + 7.hours}
    creator_id{1}
    is_full{false}
    person_in_charge_id{ Faker::Number.number(digits: 2) }

    trait :single_session do
      training_session_code{"single_session"}
    end

    trait :training_first_round do
      training_session_code{"training_first_round"}
    end

    trait :training_second_round do
      training_session_code{"training_second_round"}
    end

    trait :skip_callback do
      to_create do |instance|
        instance.save(validate: false)
      end
    end
  end

  factory :full_training_schedule, parent: :training_schedule do
    training_session_code{"single_session"}
    is_full{true}
  end

  factory :passed_absent_time_limit_schedule, parent: :training_schedule do
    training_session_code{"single_session"}
    start_time{ServerTime.now - 1.day}
  end

  factory :training_schedule_has_applicants, parent: :training_schedule do
    is_full{true}
    total_portion{1}

    after(:create) do |schedule|
      FactoryBot.create(
        :training_schedule_applicant,
        training_schedule_id: schedule.id,
        schedule_status_code: TrainingScheduleApplicant.schedule_status_codes[:booked]
      )
    end
  end

  factory :training_schedule_trigger_auto_delete, parent: :training_single_session do
    after(:create) do |schedule|
      FactoryBot.create(
        :training_schedule,
        deleted_by: schedule.id,
        deleted_at: ServerTime.now,
        is_full: false,
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        person_in_charge: schedule.person_in_charge
      )

      FactoryBot.create(
        :training_schedule,
        deleted_by: schedule.id,
        deleted_at: ServerTime.now,
        is_full: false,
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        training_session_code: schedule.training_session_code,
        location: schedule.location
      )
    end
  end
end
