FactoryBot.define do
  factory :location_survey do
    user { build_stubbed(:user) }
    answer_24{"Answer Example"}
    answer_25{"Answer Example"}
    answer_26{"Answer Example"}
    answer_28{"Answer Example"}
    answer_29{"Answer Example"}
    answer_30{"Answer Example"}
    answer_32{"Answer Example"}
    answer_33{"Answer Example"}
    answer_34{"Answer Example"}
    answer_36{"Answer Example"}
    answer_37{"Answer Example"}
    answer_38{"Answer Example"}
    answer_40{"Answer Example"}
    answer_41{"Answer Example"}
    answer_42{"Answer Example"}
    answer_44{"Answer Example"}
    answer_45{"Answer Example"}
    answer_46{"Answer Example"}
    answer_48{"Answer Example"}
    answer_49{"Answer Example"}
    answer_50{"Answer Example"}
    answer_52{"Answer Example"}
    answer_53{"Answer Example"}
    answer_54{"Answer Example"}
    answer_56{"Answer Example"}
    answer_57{"Answer Example"}
    answer_58{"Answer Example"}
    answer_60{"Answer Example"}
    answer_61{"Answer Example"}
    answer_62{"Answer Example"}
    answer_64{"Answer Example"}
    answer_65{"Answer Example"}
    answer_66{"Answer Example"}
    answer_68{"Answer Example"}
    answer_69{"Answer Example"}
    answer_70{"Answer Example"}
    answer_72{"Answer Example"}
    answer_73{"Answer Example"}
    answer_74{"Answer Example"}
    answer_76{"Answer Example"}
    answer_77{"Answer Example"}
    answer_78{"Answer Example"}
    answer_80{"Answer Example"}
    answer_81{"Answer Example"}
    answer_82{"Answer Example"}
    answer_84{"Answer Example"}
    answer_85{"Answer Example"}
    answer_86{"Answer Example"}
    answer_88{"Answer Example"}
    answer_89{"Answer Example"}
    answer_90{"Answer Example"}
    answer_92{"Answer Example"}
    answer_93{"Answer Example"}
    answer_94{"Answer Example"}
  end
end
