require "faker"

FactoryBot.define do
  factory :admin do
    role{build_stubbed(:role)}
    started_at{ServerTime.now}
    stopped_at{ServerTime.now + 1.year}
    association(
      :account,
      factory: :account,
      name: Faker::Name.name,
      name_kana: "ｱﾄﾞﾐﾝ",
      password: "Aa@123456"
    )

    trait :full_permissions do
      is_super_admin{true}
      is_insurance_mail_receiver{true}
      can_view_my_number{true}
      can_view_user_details{true}
      can_reschedule_payment_request{true}
      can_create_billing_payment_template{true}
      can_update_billing_payment{true}
    end

    trait :skip_callback do
      account { build_stubbed(:account) }
      to_create do |instance|
        instance.save(validate: false)
      end
    end

    before(:create) do |admin|
      admin.admin_departments = [build(:admin_department, admin: admin)]
    end
  end
end
