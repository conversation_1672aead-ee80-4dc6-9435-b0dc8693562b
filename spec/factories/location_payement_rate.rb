FactoryBot.define do
  factory :location_payment_rate do
    location { build_stubbed(:location) }
    now_base_rate{1000}
    now_night_rate{1000}
    now_overtime_rate{1000}
    now_short_allowance{1000}
    now_area_allowance{1000}
    chain_id{1}
    start_date { Faker::Date.forward }

    trait :skip_callback do
      to_create do |instance|
        instance.save(validate: false)
      end
    end
  end
end
