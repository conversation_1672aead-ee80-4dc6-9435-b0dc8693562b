require "faker"

FactoryBot.define do
  factory :user do
    corporation{build_stubbed(:corporation)}
    role_id{1}
    started_at{ServerTime.now - 3.months}
    is_need_confirm_order{false}
    account { build_stubbed(:account) }
    corporation_id { corporation.id }
    account_id { account.id }

    trait :skip_callback do
      to_create do |instance|
        instance.save(validate: false)
      end
    end
  end
end
