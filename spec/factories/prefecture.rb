require "faker"

FactoryBot.define do
  factory :prefecture do
    name{Faker::Address.state}
    region{FactoryBot.build_stubbed(:region)}
  end

  factory :prefecture_with_stations, parent: :prefecture do
    after(:create) do |p|
      (0..10).each do |_|
        FactoryBot.create(:station, prefecture_id: p.id)
      end
    end
  end

  factory :prefecture_with_postal_codes, parent: :prefecture do
    after(:create) do |p|
      (0..10).each do |_|
        FactoryBot.create(:postal_code, postal_code: "0600000", prefecture_id: p.id)
      end
    end
  end
end
