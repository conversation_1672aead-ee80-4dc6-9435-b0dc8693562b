require "faker"

FactoryBot.define do
  factory :location do
    corporation_group{FactoryBot.build_stubbed(:corporation_group)}
    organization{FactoryBot.build_stubbed(:organization)}
    location_type{Settings.location_type.first}
    job_category{FactoryBot.build_stubbed(:job_category)}
    name{Faker::Company.name}
    short_name{Faker::Company.name}
    creator_id{1}
    updater_id{1}
    prefecture{FactoryBot.build_stubbed(:prefecture)}
    city{Faker::Address.city}
    street_number{Faker::Address.street_address}
    building{Faker::Address.building_number}
    tel{"************"}
    fax{"************"}
    email{"<EMAIL>"}
    postal_code{"060-0810"}
    is_store_parking_area_usable{true}

    trait :skip_callback do
      to_create do |instance|
        instance.save(validate: false)
      end
    end

    trait :skip_callback do
      to_create do |instance|

      end
    end
  end

  factory :location_with_location_pics, parent: :location do
    after(:create) do |location|
      FactoryBot.create(:location_pic, location_id: location.id, pic_type_id: 1)
      FactoryBot.create(:location_pic, location_id: location.id, pic_type_id: 2)
      FactoryBot.create(:location_pic, location_id: location.id, pic_type_id: 3)
    end
  end

  factory :location_outside_lawson, parent: :location do
    thumbnail do
      Rack::Test::UploadedFile.new(
        File.open(File.join(Rails.root, "/spec/fixtures/files/sample.png"))
      )
    end
    thumbnail_background do
      Rack::Test::UploadedFile.new(
        File.open(File.join(Rails.root, "/spec/fixtures/files/sample.png"))
      )
    end
  end

  factory :location_with_payement_rate, parent: :location do
    after(:create) do |location|
      FactoryBot.create(:location_payment_rate, :with_new_base_price,
        location_id: location.id)
    end
  end

  factory :location_with_full_data, parent: :location do
    before(:create) do |location|
      department = create(:department)
      corporation = create(:corporation, :with_users, pic_department: department)
      location.corporation_group = create(:corporation_group,
        corporation_id: corporation.id,
        pic_department: department
      )
    end
  end
end
