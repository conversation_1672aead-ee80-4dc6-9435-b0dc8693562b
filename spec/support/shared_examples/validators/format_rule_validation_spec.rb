RSpec.shared_examples 'FormatRuleValidation' do |attribute, valid:, invalid:, type: nil|
  subject { build(described_class.name.underscore.to_sym) }

  valid.each do |value|
    it "is valid when #{attribute} is #{value}" do
      subject.assign_attributes(attribute => value)

      expect(subject).to be_valid
    end
  end

  invalid.each do |value|
    it "is invalid when #{attribute} is #{value}" do
      subject.assign_attributes(attribute => value)

      expect(subject).not_to be_valid
      expect(subject.errors.details[attribute]).to include(a_hash_including(error: type&.to_sym || :wrong_format))
    end
  end
end
