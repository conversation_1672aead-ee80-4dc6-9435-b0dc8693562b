RSpec.shared_examples 'TrainingScheduleApplicantDecorator' do
  describe 'Instance methods' do
    subject { build(described_class.name.underscore.to_sym) }

    describe '#staff_number_and_name' do
      it 'returns staff number and name' do
        expect(subject.staff_number_and_name).to eq("#{subject.staff.staff_number} #{subject.staff.name}")
      end
    end

    describe '#schedule_status_text' do
      it 'returns translated schedule status text' do
        expect(subject.schedule_status_text)
          .to eq(I18n.t("admin.training_schedule.schedule_status_codes.#{subject.schedule_status_code}"))
      end
    end

    describe '#created_full_date' do
      it 'returns formatted created at date' do
        subject.created_at = Time.zone.parse('2025-01-01 10:00:00')

        expect(subject.created_full_date)
          .to eq(I18n.l(Time.zone.parse('2025-01-01 10:00:00'), format: Settings.date.day_and_date))
      end
    end

    describe '#as_json' do
      it 'returns behavior of super when option rename is not present' do
        options = { only: [:id] }

        expect(subject.as_json(options)).to eq({ 'id' => subject.id })
      end

      it 'renames keys in the resulting JSON object when option rename is present' do
        options = { only: [:id], rename: { id: :applicant_id } }

        expect(subject.as_json(options)).to eq({ 'applicant_id' => subject.id })
      end
    end

    describe '#deleted_at_formatted' do
      context 'when deleted_at is present' do
        it 'returns formatted deleted at date' do
          subject.deleted_at = Time.zone.parse('2025-01-01 10:00:00')

          expect(subject.deleted_at_formatted)
            .to eq(I18n.l(Time.zone.parse('2025-01-01 10:00:00'), format: Settings.date.formats))
        end
      end

      context 'when deleted_at is blank' do
        it 'returns an empty string' do
          expect(subject.deleted_at_formatted).to eq("")
        end
      end
    end

    describe '#detail_single_session_info' do
      it 'returns detail single session info' do
        info = subject.detail_single_session_info

        expect(info).to be_a(Hash)
        expect(info).to match(a_hash_including(
          training_center_id: subject.location&.id,
          prefecture_name: subject.location&.prefecture_name,
          location_name: subject.location&.name,
          location_full_address: subject.location&.formatted_full_address,
          single_session: {
            id: subject.id,
            training_schedule_id: subject.training_schedule_id,
            schedule_status_code: subject.schedule_status_code,
            datetime: subject.formatted_datetime
          },
          absent_deadline_note: I18n.t("staff.training_schedule.details.schedule_absent_deadline_single_session",
            absent_deadline: subject.formatted_absent_deadline)
        ))
      end

      it 'does not raise error when location is nil' do
        allow(subject).to receive(:location).and_return(nil)

        expect { subject.detail_single_session_info }.not_to raise_error
      end
    end

    describe '#detail_applicant_info' do
      it 'returns detail applicant info' do
        expect(subject.detail_applicant_info).to match(
          a_hash_including(
            id: subject.id,
            training_schedule_id: subject.training_schedule_id,
            schedule_status_code: subject.schedule_status_code,
            datetime: subject.formatted_datetime
          )
        )
      end
    end
  end
end
