RSpec.shared_examples 'TrainingScheduleDecorator' do
  describe 'Constants' do
    it 'defines TRAINING_SESSION_MAPPING' do
      expect(described_class::TRAINING_SESSION_MAPPING).to be_a(Hash)
      expect(described_class::TRAINING_SESSION_MAPPING.keys)
        .to include(:training_first_round, :training_second_round, :single_session)
    end
  end

  describe 'Instance methods' do
    subject { build(described_class.name.underscore.to_sym) }

    describe '#applicant_staff_ids' do
      let(:training_schedule_applicants) { build_list(:training_schedule_applicant, 2) }

      before do
        allow(subject).to receive(:training_schedule_applicants).and_return(training_schedule_applicants)
      end

      it 'returns staff ids through training schedule applicants' do
        expect(subject.applicant_staff_ids).to eq(training_schedule_applicants.map(&:staff_id))
      end
    end

    describe '#booked_staff_names' do
      let(:staffs) { build_list(:staff, 2) }
      let(:booked_applicants) do
        staffs.map{|staff| double('booked_applicants', staff: staff) }
      end

      it 'returns staff names through booked applicants' do
        expect(subject.booked_staff_names(booked_applicants)).to eq(staffs.map(&:account_name))
      end

      it 'returns empty array when booked applicants is blank' do
        expect(subject.booked_staff_names(nil)).to eq([])
      end

      it 'does not include nil' do
        booked_applicants << double('booked_applicants', staff: nil)

        expect(subject.booked_staff_names(booked_applicants)).to eq(staffs.map(&:account_name))
      end

      it 'does not raise error when booked applicants has nil value' do
        booked_applicants << nil

        expect { subject.booked_staff_names(booked_applicants) }.not_to raise_error
      end
    end

    describe '#list_format' do
      let(:booked_applicants) { build_list(:training_schedule_applicant, 2) }

      it 'returns a hash with keys and slots is zero when booked applicants is empty' do
        results = subject.list_format([])

        expect(results).to be_a(Hash)
        expect(results.keys).to match_array([:schedule_id, :location_name, :training_date,
          :training_datetime, :start_time, :end_time, :training_session, :slots,
          :person_in_charge, :deleted_by, :deleted_at])

        expect(results[:slots]).to match_array({filled: 0, total: subject.total_portion})
      end

      it 'returns a hash with filled slots is size of booked applicants' do
        results = subject.list_format(booked_applicants)

        expect(results[:slots]).to match_array({filled: 2, total: subject.total_portion})
      end

      it 'returns deleted date and time when deleted_at is present' do
        subject.deleted_at = Time.zone.parse('2025-01-01 10:00:00')
        results = subject.list_format(booked_applicants)

        expect(results[:deleted_at]).to match_array({date: '2025-01-01', time: '10:00'})
      end
    end

    describe '#deleted_format' do
      it 'returns a hash with the correct structure' do
        result = subject.deleted_format

        expect(result).to be_a(Hash)
        expect(result.keys).to match_array([:id, :location_id, :location_name, :start_time, :end_time,
          :training_session, :training_session_code, :total_portion, :person_in_charge,
          :person_in_charge_id, :training_datetime])
      end
    end

    describe '#training_session_text' do
      %w(training_first_round training_second_round single_session).each do |training_session_code|
        it "returns the correct training session text for #{training_session_code}" do
          subject.training_session_code = training_session_code

          expect(subject.training_session_text)
            .to eq(I18n.t("admin.training_schedule.training_session_codes.#{training_session_code}"))
        end
      end
    end

    describe '#formatted_calendar_data' do
      let(:booked_applicants) { build_list(:training_schedule_applicant, 2) }

      it 'returns a hash with the correct structure and total_apply is zero when booked applicants is empty' do
        result = subject.formatted_calendar_data([])

        expect(result).to be_a(Hash)
        expect(result.keys).to match_array([:id, :height, :training_date, :start_time, :end_time, :total_apply,
          :total_portion, :location_name, :applieds_names, :person_in_charge,
          :person_in_charge_id, :training_session_code])

        expect(result[:total_apply]).to eq(0)
      end

      it 'returns a hash with total_apply is size of booked applicants' do
        result = subject.formatted_calendar_data(booked_applicants)

        expect(result[:total_apply]).to eq(2)
      end
    end

    describe '#view_time' do
      it 'returns the correct view time' do
        expect(subject.view_time).to eq(Settings.training_schedules.view.time * 60)
      end
    end

    describe '#location_json' do
      it 'returns a hash with the correct structure' do
        result = subject.location_json

        expect(result).to be_a(Hash)
        expect(result.keys).to match_array([:id, :name])
      end

      it 'returns nil if location_id is nil' do
        subject.location_id = nil

        expect(subject.location_json).to be_nil
      end
    end

    describe '#person_in_charge_json' do
      let(:person_in_charge) { build_stubbed(:admin) }
      let(:current_admin) { build_stubbed(:admin) }

      it 'returns a hash with the correct structure and data when person_in_charge_id is present' do
        subject.person_in_charge_id = person_in_charge.id

        result = subject.person_in_charge_json(current_admin)

        expect(result).to eq({id: person_in_charge.id, name: person_in_charge.account.name})
      end

      it 'returns a hash with the correct structure and current admin data when person_in_charge_id is nil' do
        subject.person_in_charge_id = nil

        result = subject.person_in_charge_json(current_admin)

        expect(result).to eq({id: current_admin.id, name: current_admin.account.name})
      end
    end

    describe '#show_format' do
      let(:current_admin) { build_stubbed(:admin) }

      it 'returns a hash with the correct structure' do
        result = subject.show_format(current_admin)

        expect(result).to be_a(Hash)
        expect(result.keys).to match_array([:id, :training_date, :start_time, :end_time, :training_session_code,
          :location, :has_person_in_charge, :person_in_charge, :can_change_person_in_charge,
          :applicants, :total_applicants])
      end
    end

    describe '#as_json' do
      it 'returns behavior of super if when option rename is not present' do
        expect(subject.as_json(only: [:id])).to eq({"id" => subject.id})
      end

      it 'renames keys in the resulting JSON object when option rename is present' do
        options = { only: [:id], rename: { id: :schedule_id } }

        result = subject.as_json(options)
        expect(result).to eq({"schedule_id" => subject.id})
      end
    end

    describe '#format_date' do
      it 'formats the date correctly' do
        date = Date.today
        format = Settings.date.dash_separator
        expect(subject.format_date(date, format)).to eq(I18n.l(date, format: format))
      end

      it 'returns an empty string if date is nil' do
        expect(subject.format_date(nil, Settings.date.dash_separator)).to eq("")
      end
    end

    describe '#training_date' do
      it 'returns the training date in the correct format' do
        expect(subject.training_date).to eq(I18n.l(subject.start_time, format: Settings.date.day_and_month))
      end
    end

    describe '#training_date_weekday' do
      it 'returns the training date with weekday in the correct format' do
        expect(subject.training_date_weekday).to eq(I18n.l(subject.start_time, format: Settings.date.month_date))
      end
    end

    describe '#training_date_text' do
      it 'returns the training date text in the correct format' do
        expect(subject.training_date_text).to eq(I18n.l(subject.start_time, format: Settings.date.formats))
      end
    end

    describe '#working_date' do
      it 'returns the working date in the correct format' do
        expect(subject.working_date).to eq(I18n.l(subject.start_time, format: Settings.date.day_and_date))
      end
    end

    describe '#format_time' do
      it 'formats the time correctly' do
        time = Time.now
        format = Settings.time.formats
        expect(subject.format_time(time, format)).to eq(time.strftime(format))
      end

      it 'returns an empty string if time is nil' do
        expect(subject.format_time(nil, Settings.time.formats)).to eq("")
      end
    end

    describe '#formatted_datetime' do
      it 'returns the formatted datetime' do
        expect(subject.formatted_datetime)
          .to eq("#{I18n.l(subject.start_time, format: Settings.date.year_month_and_date)} #{subject.training_time}")
      end
    end

    describe '#start_time_text' do
      it 'returns the start time text' do
        expect(subject.start_time_text).to eq(subject.start_time.strftime(Settings.time.formats))
      end
    end

    describe '#end_time_text' do
      it 'returns the end time text' do
        expect(subject.end_time_text).to eq(subject.end_time.strftime(Settings.time.formats))
      end
    end

    describe '#training_time' do
      it 'returns the training time' do
        expect(subject.training_time).to eq("#{subject.start_time_text} ~ #{subject.end_time_text}")
      end
    end

    describe '#training_time_with_day' do
      it 'returns the training time with day' do
        expect(subject.training_time_with_day).to eq("#{subject.working_date} | #{subject.training_time}")
      end
    end

    describe '#formatted_absent_deadline' do
      it 'returns the formatted absent deadline' do
        expect(subject.formatted_absent_deadline)
        .to eq("#{I18n.l(subject.absent_deadline, format: Settings.date.year_month_and_date)} #{subject.format_time(subject.absent_deadline)}")
      end
    end

    describe '#default_mailer_data' do
      it 'returns the default mailer data' do
        result = subject.default_mailer_data

        expect(result).to be_a(Hash)
        expect(result.keys).to match_array([:trainer_name, :location_name, :location_prefecture,
          :training_date_weekday, :training_datetime])
      end
    end

    describe '#booked_mailer_data' do
      let(:deleted_schedules) { build_list(:training_schedule, 2) }

      it 'returns the default mail data with schedule id and deleted schedules' do
        result = subject.booked_mailer_data(deleted_schedules)

        expect(result).to be_a(Hash)
        expect(result).to match(a_hash_including(subject.default_mailer_data))

        expect(result[:schedule_id]).to eq(subject.id)
        expect(result[:deleted_schedules]).to eq(deleted_schedules)
      end
    end

    describe '#absent_mailer_data' do
      let(:deleted_schedules) { build_list(:training_schedule, 2) }

      it 'returns the default mail data with deleted schedules' do
        result = subject.absent_mailer_data(deleted_schedules)

        expect(result).to be_a(Hash)
        expect(result).to match(a_hash_including(subject.default_mailer_data))

        expect(result[:deleted_schedules]).to eq(deleted_schedules)
      end
    end

    describe '#delete_mailer_data' do
      it 'returns the delete mailer data same as default mailer data' do
        expect(subject.delete_mailer_data).to eq(subject.default_mailer_data)
      end
    end

    describe '#auto_restore_mail_data' do
      let(:restored_schedules) { build_list(:training_schedule, 2) }

      it 'returns the auto restore mail data with restored schedules if restored schedules is not empty' do
        result = subject.auto_restore_mail_data(restored_schedules)

        expect(result).to be_a(Hash)
        expect(result.keys).to match_array([:schedule_id, :training_date_weekday, :person_in_charge_id,
          :location_name, :person_in_charge, :training_datetime, :restored_schedules])

        expect(result[:restored_schedules]).to eq(restored_schedules)
      end
    end
  end

  describe 'Class methods' do
    describe '.label_i18n' do
      it 'returns the label i18n' do
        expect(described_class.label_i18n).to eq(I18n.t("admin.training_schedule.export.list.columns").values)
      end
    end
  end
end
