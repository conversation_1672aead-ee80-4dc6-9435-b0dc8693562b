RSpec.shared_examples 'TrainingScheduleApplicantConcern' do
  describe 'Constants' do
    it 'defines NO_TRAINING_SCHEDULE_SCENARIOS' do
      expect(described_class::NO_TRAINING_SCHEDULE_SCENARIOS).to eq([
        [nil, nil],
        [:booked, nil], [nil, :booked],
        [:joined, nil], [nil, :joined],
        [:absent_with_notice, nil], [nil, :absent_with_notice],
        [:absent_without_notice, nil], [nil, :absent_without_notice]
      ])
    end

    it 'defines ARRANGED_TRAINING_SCENARIOS' do
      expect(described_class::ARRANGED_TRAINING_SCENARIOS).to eq([
        [:booked, :booked],
        [:joined, :booked], [:booked, :joined],
        [:joined, :joined]
      ])
    end

    it 'defines ABSENT_FROM_TRAINING_SCENARIOS' do
      expect(described_class::ABSENT_FROM_TRAINING_SCENARIOS).to eq([
        [:absent_with_notice, :absent_with_notice],
        [:absent_without_notice, :absent_without_notice],
        [:absent_with_notice, :absent_without_notice], [:absent_without_notice, :absent_with_notice],
        [:absent_with_notice, :booked], [:booked, :absent_with_notice],
        [:absent_without_notice, :booked], [:booked, :absent_without_notice],
        [:absent_with_notice, :joined], [:joined, :absent_with_notice],
        [:absent_without_notice, :joined], [:joined, :absent_without_notice]
      ])
    end

    it 'defines SCHEDULE_STATUS_ORDER' do
      expect(described_class::SCHEDULE_STATUS_ORDER).to eq([:joined, :booked, :absent_with_notice, :absent_without_notice])
    end
  end

  describe 'Instance methods' do
    subject { create(described_class.name.underscore.to_sym) }

    let(:first_session) { create(:training_schedule, :training_first_round) }
    let(:second_session) { create(:training_schedule, :training_second_round) }
    let(:single_session) { create(:training_schedule, :single_session) }

    %w(first second single).each do |val|
      describe "##{val}_session" do
        context 'when there is no training schedule' do
          before do
            allow(subject.training_schedule_applicants).to receive("#{val}_sessions")
              .and_return(TrainingScheduleApplicant.none)
          end

          it 'returns nil' do
            expect(subject.send("#{val}_session")).to be_nil
          end
        end

        context 'when there are training schedule applicants' do
          before do
            subject.training_schedule_applicants = [
              create(:training_schedule_applicant,
                training_schedule: self.send("#{val}_session"),
                schedule_status_code: :absent_with_notice
              ),

              create(:training_schedule_applicant,
                training_schedule: self.send("#{val}_session"),
                schedule_status_code: :booked
              )
            ]
          end

          it 'returns the correct training schedule applicant' do
            expect(subject.send("#{val}_session")).to be_a(TrainingScheduleApplicant)
            expect(subject.send("#{val}_session").training_schedule.training_session_code)
              .to eq(self.send("#{val}_session").training_session_code)
          end

          it 'sorts training schedule applicants by schedule status and updated_at' do
            expect(subject.send("#{val}_session").schedule_status_code).to eq("booked")
          end
        end
      end

      describe "##{val}_session_json" do
        context 'when there is no training schedule' do
          before do
            allow(subject.training_schedule_applicants).to receive("#{val}_sessions")
              .and_return(TrainingScheduleApplicant.none)
          end

          it 'returns nil' do
            expect(subject.send("#{val}_session")).to be_nil
          end
        end

        context 'when there are training schedule applicants' do
          let(:applicant) { create(:training_schedule_applicant, training_schedule: self.send("#{val}_session")) }

          it 'returns json object including training schedule applicant info' do
            allow(subject).to receive("#{val}_session").and_return(applicant)

            expect(subject.send("#{val}_session_json")).to eq(
              {
                id: applicant.id,
                schedule_status_code: applicant.schedule_status_code,
                training_schedule_id: applicant.training_schedule_id,
                datetime: applicant.formatted_datetime,
                absent_deadline: applicant.formatted_absent_deadline,
                able_to_absent: applicant.able_to_absent?,
                unavailable: applicant.is_unavailable?
              }.as_json
            )
          end
        end
      end
    end

    describe '#handle_training_process_code' do
      context 'when single session is present' do
        let(:training_schedule_applicant) { build(:training_schedule_applicant) }

        before do
          allow(subject).to receive(:single_session).and_return(training_schedule_applicant)
        end

        it 'returns :arranged_training when schedule status code is booked' do
          allow(training_schedule_applicant).to receive(:schedule_status_code).and_return('booked')

          expect(subject.handle_training_process_code).to eq(:arranged_training)
        end

        it 'returns :arranged_training when schedule status code is joined' do
          allow(training_schedule_applicant).to receive(:schedule_status_code).and_return('joined')

          expect(subject.handle_training_process_code).to eq(:arranged_training)
        end

        it 'returns :absent_from_training when schedule status code is absent_with_notice' do
          allow(training_schedule_applicant).to receive(:schedule_status_code).and_return('absent_with_notice')

          expect(subject.handle_training_process_code).to eq(:absent_from_training)
        end

        it 'returns :absent_from_training when schedule status code is absent_without_notice' do
          allow(training_schedule_applicant).to receive(:schedule_status_code).and_return('absent_without_notice')

          expect(subject.handle_training_process_code).to eq(:absent_from_training)
        end

        it 'returns :no_training_schedule when schedule status code is different' do
          allow(training_schedule_applicant).to receive(:schedule_status_code).and_return('other')

          expect(subject.handle_training_process_code).to eq(:no_training_schedule)
        end
      end

      context 'when single session is not present' do
        let(:first_applicant) { build(:training_schedule_applicant) }
        let(:second_applicant) { build(:training_schedule_applicant) }

        before do
          allow(subject).to receive(:first_session).and_return(first_applicant)
          allow(subject).to receive(:second_session).and_return(second_applicant)
          allow(subject).to receive(:single_session).and_return(nil) # single session is not present
        end

        it 'returns :no_training_schedule if first session is nil' do
          allow(subject).to receive(:first_session).and_return(nil)

          expect(subject.handle_training_process_code).to eq(:no_training_schedule)
        end

        it 'returns :no_training_schedule when second session is nil' do
          allow(subject).to receive(:second_session).and_return(nil)

          expect(subject.handle_training_process_code).to eq(:no_training_schedule)
        end

        context 'and all sessions are booked or joined' do
          %w(booked joined).each do |status_code|
            %w(booked joined).each do |other_status_code|
              before do
                allow(first_applicant).to receive(:schedule_status_code).and_return(status_code)
                allow(second_applicant).to receive(:schedule_status_code).and_return(other_status_code)
              end

              it 'returns :arranged_training' do
                expect(subject.handle_training_process_code).to eq(:arranged_training)
              end
            end
          end
        end

        context 'and any session is absent_with_notice or absent_without_notice' do
          %w(absent_with_notice absent_without_notice).each do |status_code|
            TrainingScheduleApplicant.schedule_status_codes.keys.each do |other_status_code|
              before do
                allow(first_applicant).to receive(:schedule_status_code).and_return(status_code)
                allow(second_applicant).to receive(:schedule_status_code).and_return(other_status_code)
              end

              it 'returns :absent_from_training' do
                expect(subject.handle_training_process_code).to eq(:absent_from_training)
              end
            end
          end
        end

        context 'and any session is invalid status' do
          TrainingScheduleApplicant.schedule_status_codes.keys.each do |status_code|
            before do
              allow(first_applicant).to receive(:schedule_status_code).and_return(status_code)
              allow(second_applicant).to receive(:schedule_status_code).and_return('other')
            end

            it 'returns nil' do
              expect(subject.handle_training_process_code).to eq(nil)
            end
          end
        end
      end
    end

    describe '#update_recruitment_process!' do
      before do
        subject.staff_recruitment_process = create(:staff_recruitment_process,
          registration_process_code: 'training_schedule_step')
      end

      it 'updates recruitment process with params' do
        expect { subject.update_recruitment_process!(registration_process_code: 'completed_registration') }
          .to change(subject.staff_recruitment_process, :registration_process_code).from('training_schedule_step')
            .to('completed_registration')
      end
    end
  end
end
