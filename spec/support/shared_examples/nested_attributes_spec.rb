RSpec.shared_examples 'Nested attributes with allow_destroy' do |nested_attributes|
  it 'creates nested attributes' do
    parent.assign_attributes("#{nested_attributes}_attributes" => nested_attribute_params)

    expect { parent.save! }.to change { parent.send(nested_attributes).model.count }.by(nested_attribute_params.size)
  end

  it 'destroys nested attributes' do
    parent.update("#{nested_attributes}_attributes" => nested_attribute_params) #Initial save

    destroy_params = parent.send(nested_attributes).map do |attributes|
      {id: attributes.id, _destroy: '1'}
    end

    parent.update("#{nested_attributes}_attributes" => destroy_params)

    expect(parent.send(nested_attributes)).to be_empty
  end
end
