require "rails_helper"

RSpec.describe Staff::TrainingSchedulesService, type: :service do
  let!(:staff){create(:staff)}
  FactoryBot.create(:staff)
  let(:training_center){create(:training_center)}
  let(:first_session){create(:training_session, location: training_center)}
  let(:second_session){create(:training_session, location: training_center)}
  let(:applicant){create(:training_schedule_applicant, staff: staff, first_session: first_session, second_session: second_session)}
  let(:params){ActionController::Parameters.new(applicant_ids: [applicant.id], consider_dropping_out: true)}

  describe "#cancel" do
    context "when all applicants are able to absent" do
      it "updates the schedule status of each applicant to absent_with_notice" do
        expect{subject.cancel(params)}.to change{applicant.reload.schedule_status_code}.to("absent_with_notice")
      end

      it "saves a staff action log for each applicant" do
        expect{subject.cancel(params)}.to change{StaffActionLog.count}.by(1)
      end

      it "notifies the trainer if the applicant has one" do
        expect(subject).to receive(:notify_trainer).with(applicant)
        subject.cancel(params)
      end

      it "updates the recruitment process of the current staff" do
        expect{subject.cancel(params)}.to change{staff.reload.training_process_code}.to("absent_from_training")
      end

      it "returns a default JSON response" do
        expect(subject.cancel(params)).to eq(success: true, message: "")
      end
    end

    context "when an applicant is not able to absent" do
      before{allow(applicant).to receive(:able_to_absent?).and_return(false)}

      it "does not update the schedule status of any applicant" do
        expect{subject.cancel(params)}.not_to change{applicant.reload.schedule_status_code}
      end

      it "does not save any staff action log" do
        expect{subject.cancel(params)}.not_to change{StaffActionLog.count}
      end

      it "does not notify the trainer" do
        expect(subject).not_to receive(:notify_trainer)
        subject.cancel(params)
      end

      it "does not update the recruitment process of the current staff" do
        expect{subject.cancel(params)}.not_to change{staff.reload.training_process_code}
      end

      it "returns a JSON response with an error message" do
        expect(subject.cancel(params)).to eq(success: false, message: I18n.t("staff.training_schedule.errors.absent_failed"))
      end
    end

    context "when an error occurs during the transaction" do
      before{allow_any_instance_of(TrainingScheduleApplicant).to receive(:save!).and_raise(ActiveRecord::RecordInvalid)}

      it "does not update the schedule status of any applicant" do
        expect{subject.cancel(params)}.not_to change{applicant.reload.schedule_status_code}
      end

      it "does not save any staff action log" do
        expect{subject.cancel(params)}.not_to change{StaffActionLog.count}
      end

      it "does not notify the trainer" do
        expect(subject).not_to receive(:notify_trainer)
        subject.cancel(params)
      end

      it "does not update the recruitment process of the current staff" do
        expect{subject.cancel(params)}.not_to change{staff.reload.training_process_code}
      end

      it "returns a JSON response with an error message" do
        expect(subject.cancel(params)).to eq(success: false, message: I18n.t("staff.training_schedule.errors.absent_failed"))
      end
    end
  end

  describe "#get_applicants" do
    it "returns the training schedule applicants with the given IDs" do
      expect(subject.get_applicants(params)).to eq([applicant])
    end
  end

  describe "#log_data" do
    it "returns a hash with the correct keys and values" do
      expect(subject.log_data("staff_cancel_training_schedule", first_session.training_schedule.id, training_applicant_id: applicant.id)).to eq(
        target_type: "TrainingSchedule",
        target_id: first_session.training_schedule.id,
        action_type: "staff_cancel_training_schedule",
        log_params: {
          special_info: {
            training_applicant_id: applicant.id
          }
        }
      )
    end
  end

  describe "#applied_schedules" do
    before{staff.update(first_session: first_session, second_session: second_session)}

    it "returns a hash with the correct keys and values" do
      expect(subject.applied_schedules).to eq(
        training_center_id: training_center.id,
        prefecture_name: training_center.prefecture_name,
        location_name: training_center.name,
        location_full_address: training_center.formatted_full_address,
        first_session: first_session.as_json,
        second_session: second_session.as_json
      )
    end
  end

  describe "#schedule_params" do
    let(:params){ActionController::Parameters.new(training_center_id: training_center.id, first_session_id: first_session.id, second_session_id: second_session.id, training_schedule: {foo: "bar"})}

    it "returns a hash with the correct keys and values" do
      expect(subject.schedule_params(params)).to eq(
        training_center_id: training_center.id,
        first_session_id: first_session.id,
        second_session_id: second_session.id
      )
    end
  end

  describe "#notify_to_staff" do
    before{staff.update(first_session: first_session, second_session: second_session, tel: "1234567890")}

    it "sends an SMS to the current staff" do
      expect(MessageSenderService).to receive(:notify_training_schedule_booked).with(
        staff.tel,
        location_name: training_center.name,
        location_full_address: training_center.formatted_full_address,
        first_session_datetime: first_session.datetime,
        second_session_datetime: second_session.datetime
      )
      subject.notify_to_staff
    end
  end

  describe "#notify_trainer" do
    let(:trainer_email){"<EMAIL>"}
    let(:applicant){create(:training_schedule_applicant, staff: staff, first_session: first_session, second_session: second_session, person_in_charge_email: trainer_email, person_in_charge_name: "Trainer")}

    it "sends an email to the trainer" do
      expect(AdminMailer).to receive(:notify_staff_cancel_schedule).with(
        trainer_email,
        training_schedule_id: first_session.training_schedule.id,
        trainer_name: "Trainer",
        staff_name: staff.name,
        location_name: training_center.name,
        location_prefecture: training_center.prefecture_name,
        datetime: applicant.formatted_datetime,
        datetime_short: applicant.training_date_weekday
      ).and_return(double(deliver_now: true))
      subject.notify_trainer(applicant)
    end

    context "when the applicant does not have a trainer" do
      let(:applicant){create(:training_schedule_applicant, staff: staff, first_session: first_session, second_session: second_session, person_in_charge_email: nil, person_in_charge_name: nil)}

      it "does not send any email" do
        expect(AdminMailer).not_to receive(:notify_staff_cancel_schedule)
        subject.notify_trainer(applicant)
      end
    end
  end

  describe "#send_sms_to_staff" do
    before{staff.update(first_session: first_session, second_session: second_session, tel: "1234567890")}

    it "sends an SMS to the current staff" do
      expect(MessageSenderService).to receive(:notify_training_schedule_booked).with(
        staff.tel,
        location_name: training_center.name,
        location_full_address: training_center.formatted_full_address,
        first_session_datetime: first_session.datetime,
        second_session_datetime: second_session.datetime
      )
      subject.send_sms_to_staff
    end

    context "when the current staff does not have a phone number" do
      before{staff.update(tel: nil)}

      it "does not send any SMS" do
        expect(MessageSenderService).not_to receive(:notify_training_schedule_booked)
        subject.send_sms_to_staff
      end
    end
  end

  describe "#send_fcm_to_staff" do
    it "enqueues a job to send an FCM notification to the current staff" do
      expect(AppSendNotificationWorker).to receive(:perform_async).with([{
                                                                          staff_id: staff.id,
        creator_type: :staff,
        notification_type: :staff_booked_training_schedule,
        params: {
          location_name: training_center.name,
          first_job_date_time: first_session.formatted_datetime,
          second_job_date_time: second_session.formatted_datetime
        }
                                                                        }])
      subject.send_fcm_to_staff
    end
  end

  describe "#diff_reschedule" do
    let(:booked_ids){[first_session.training_schedule.id, second_session.training_schedule.id]}

    context "when the schedule params are valid to reschedule" do
      let(:params){ActionController::Parameters.new(first_session_id: first_session.training_schedule.id, second_session_id: -1)}

      it "returns a hash with the added and removed IDs" do
        expect(subject.diff_reschedule).to eq([
                                                {added_ids: [], removed_ids: [second_session.training_schedule.id]},
          true,
          ""
                                              ])
      end
    end

    context "when any schedule is unchanged" do
      let(:params){ActionController::Parameters.new(first_session_id: -1, second_session_id: second_session.training_schedule.id)}

      it "returns a hash with the added and removed IDs" do
        expect(subject.diff_reschedule).to eq([
                                                {added_ids: [first_session.training_schedule.id], removed_ids: []},
          true,
          ""
                                              ])
      end
    end

    context "when any schedule is changed" do
      let(:new_training_schedule){create(:training_schedule)}
      let(:params){ActionController::Parameters.new(first_session_id: new_training_schedule.id, second_session_id: second_session.training_schedule.id)}

      it "returns a hash with the added and removed IDs" do
        expect(subject.diff_reschedule).to eq([
                                                {added_ids: [new_training_schedule.id], removed_ids: [first_session.training_schedule.id]},
          true,
          ""
                                              ])
      end
    end

    context "when any schedule ID is blank" do
      let(:params){ActionController::Parameters.new(first_session_id: "", second_session_id: second_session.training_schedule.id)}

      it "returns a hash with an error message" do
        expect(subject.diff_reschedule).to eq([
                                                {},
          false,
          I18n.t("staff.training_schedule.errors.must_select_training_session")
                                              ])
      end
    end

    context "when any schedule ID is invalid" do
      let(:params){ActionController::Parameters.new(first_session_id: -1, second_session_id: 999)}

      it "returns a hash with an error message" do
        expect(subject.diff_reschedule).to eq([
                                                {},
          false,
          I18n.t("staff.training_schedule.errors.invalid_training_session")
                                              ])
      end
    end
  end

  describe "#diff_add_and_remove" do
    it "returns a hash with the added and removed IDs" do
      expect(subject.diff_add_and_remove([1, 2, 3], [2, 3, 4])).to eq(
        added_ids: [4],
        removed_ids: [1]
      )
    end
  end
end
