require 'rails_helper'

RSpec.describe StaffSalary, type: :model do
  describe 'Constants' do
    it 'defines SALARY_ATTRS' do
      expect(described_class::SALARY_ATTRS).to eq([:id, :staff_id, :income_tax_type, :tax_type, :is_spouse,
        :insurance_subsection_type, :insurance_number, :basic_pension_number,
        :insurance_certificate_acquisition_date, :insurance_lost_qualification_date,
        :employment_insurance_type, :employment_insurance_number, :copied_employment_insurance_number,
        :employment_certificate_acquisition_date, :employment_lost_qualification_date,
        :insurance_subsection_deadline, :employment_insurance_deadline])
    end

    it 'defines SALARY_DATETIME_ATTRS' do
      expect(described_class::SALARY_DATETIME_ATTRS).to eq([:insurance_certificate_acquisition_date,
        :insurance_lost_qualification_date,
        :employment_certificate_acquisition_date, :employment_lost_qualification_date,
        :insurance_subsection_deadline, :employment_insurance_deadline])
    end
  end

  describe 'Associations' do
    it { should belong_to(:staff) }
  end

  describe 'Validations' do
    subject(:staff_salary) { build(:staff_salary) }

    it { should validate_presence_of(:income_tax_type) }
    it { should validate_presence_of(:employment_insurance_type) }
    it { should validate_presence_of(:insurance_subsection_type) }

    context 'when i_t_target? is true' do
      subject(:staff_salary) { build(:staff_salary, income_tax_type: :i_t_target) }

      it { should validate_presence_of(:tax_type) }
      it { should validate_presence_of(:is_spouse) }
    end

    it 'should validate date for insurance_certificate_acquisition_date' do
      staff_salary.insurance_certificate_acquisition_date = 'invalid_date'

      expect(staff_salary).not_to be_valid
      expect(staff_salary.errors.details[:insurance_certificate_acquisition_date])
        .to include(error: :invalid_date_format)
    end

    it 'should validate date for insurance_lost_qualification_date' do
      staff_salary.insurance_lost_qualification_date = 'invalid_date'

      expect(staff_salary).not_to be_valid
      expect(staff_salary.errors.details[:insurance_lost_qualification_date])
        .to include(error: :invalid_date_format)
    end

    it 'should validate date for employment_certificate_acquisition_date' do
      staff_salary.employment_certificate_acquisition_date = 'invalid_date'

      expect(staff_salary).not_to be_valid
      expect(staff_salary.errors.details[:employment_certificate_acquisition_date])
        .to include(error: :invalid_date_format)
    end

    it 'should validate date for employment_lost_qualification_date' do
      staff_salary.employment_lost_qualification_date = 'invalid_date'

      expect(staff_salary).not_to be_valid
      expect(staff_salary.errors.details[:employment_lost_qualification_date])
        .to include(error: :invalid_date_format)
    end

    it 'should validate ensurance number for basic_pension_number' do
      staff_salary.basic_pension_number = 'invalid_number'

      expect(staff_salary).not_to be_valid
      expect(staff_salary.errors.details[:basic_pension_number])
        .to include(error: :wrong_format)
    end

    it 'should validate ensurance number for insurance_number' do
      staff_salary.insurance_number = 'invalid_number'

      expect(staff_salary).not_to be_valid
      expect(staff_salary.errors.details[:insurance_number])
        .to include(error: :wrong_format)
    end

    it 'should validate ensurance number for employment_insurance_number' do
      staff_salary.employment_insurance_number = 'invalid_number'

      expect(staff_salary).not_to be_valid
      expect(staff_salary.errors.details[:employment_insurance_number])
        .to include(error: :wrong_format)
    end

    it { should validate_length_of(:basic_pension_number).is_at_most(11) }
    it { should validate_length_of(:insurance_number).is_at_most(11) }
    it { should validate_length_of(:employment_insurance_number).is_at_most(14) }
  end

  describe 'Enums' do
    it { should define_enum_for(:is_spouse).with_values(not_spouse: 0, spouse: 1) }
    it { should define_enum_for(:insurance_subsection_type).with_values(not_joining: 0, subscription: 1) }
    it { should define_enum_for(:income_tax_type).with_values(i_t_target: 1, i_t_not_target: 2) }
    it { should define_enum_for(:tax_type).with_values(institution: 1, ownership: 2, category: 3, compensation: 4, nonresident: 5) }
    it { should define_enum_for(:employment_insurance_type).with_values(e_i_not_target: 0, e_i_target: 1, e_i_eligible_elderly: 2) }
  end

  describe 'Instance methods' do
    describe '#set_insurance_subsection_type_lost_date_retirement' do
      let(:staff) { build(:staff, retirement_date: Date.today) }
      let(:staff_salary) { build(:staff_salary, staff: staff, insurance_subsection_type: :subscription) }

      it 'sets insurance_subsection_type to not_joining and sets insurance_lost_qualification_date' do
        staff_salary.set_insurance_subsection_type_lost_date_retirement

        expect(staff_salary.insurance_subsection_type).to eq('not_joining')
        expect(staff_salary.insurance_lost_qualification_date).to eq(Date.today.beginning_of_day + 1.day)
      end

      it 'does not set when insurance_subsection_type is not subscription' do
        staff_salary.insurance_subsection_type = :not_joining
        staff_salary.set_insurance_subsection_type_lost_date_retirement

        expect(staff_salary.insurance_subsection_type).to eq('not_joining')
        expect(staff_salary.insurance_lost_qualification_date).to be_nil
      end
    end

    describe '#set_employment_insurance_type_lost_date_retirement' do
      let(:staff) { build(:staff, retirement_date: Date.today) }
      let(:staff_salary) { build(:staff_salary, staff: staff, employment_insurance_type: :e_i_target) }

      it 'sets employment_insurance_type to e_i_not_target and sets employment_lost_qualification_date' do
        staff_salary.set_employment_insurance_type_lost_date_retirement

        expect(staff_salary.employment_insurance_type).to eq('e_i_not_target')
        expect(staff_salary.employment_lost_qualification_date).to eq(Date.today.beginning_of_day + 1.day)
      end

      it 'does not set when employment_insurance_type is not e_i_target' do
        staff_salary.employment_insurance_type = :e_i_eligible_elderly
        staff_salary.set_employment_insurance_type_lost_date_retirement

        expect(staff_salary.employment_insurance_type).to eq('e_i_eligible_elderly')
        expect(staff_salary.employment_lost_qualification_date).to be_nil
      end
    end
  end

  describe 'Acts as paranoid' do
    let(:staff_salary) { create(:staff_salary) }

    it 'soft deletes the record' do
      staff_salary.destroy
      expect(described_class.find_by(id: staff_salary.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: staff_salary.id)).to eq(staff_salary)
    end

    it 'restores the record' do
      staff_salary.destroy
      staff_salary.restore
      expect(described_class.find_by(id: staff_salary.id)).to eq(staff_salary)
    end
  end
end
