require 'rails_helper'

RSpec.describe Admin, type: :model do
  let(:admin)       { create(:admin) }
  let(:department)  { create(:department) }

  describe 'Constants' do
    it { expect(described_class).to be_const_defined(:ADMIN_DEFAULT_START_DATE) }
    it { expect(described_class).to be_const_defined(:ADMIN_ATTRIBUTES) }
    it { expect(described_class).to be_const_defined(:ROLE_ADMIN) }
    it { expect(described_class).to be_const_defined(:ROLE_USER) }
  end

  describe 'WardenFunction' do
    it 'responds to methods from WardenFunction' do
      expect(described_class).to respond_to(:wardenable)
    end
  end

  describe 'Uploaders' do
    it 'mounts uploader' do
      admin = build(:admin)
      expect(admin.avatar).to be_a(AdminAvatarUploader)
    end

    it 'stores and retrieves uploaded file' do
      file = fixture_file_upload("sample.png", "image/png")
      admin = build(:admin, avatar: file)
      expect(admin.avatar.url).to be_present
    end
  end

  describe 'Validators' do
    subject{build(:admin)}

    describe 'presence' do
      it{should validate_presence_of(:role_id)}
      it{should validate_presence_of(:started_at)}
    end

    describe 'uniqueness' do
      it{should validate_uniqueness_of(:account_id)}
    end

    describe 'valid_date' do
      it 'is invalid if started_at not a valid date' do
        admin = build(:admin, started_at: 'invalid-date')
        expect(admin).not_to be_valid
        expect(admin.errors.details[:started_at]).to include(a_hash_including(error: :invalid_date_format))
      end

      it 'is invalid if stopped_at not a valid date' do
        admin = build(:admin, stopped_at: 'invalid-date')
        expect(admin).not_to be_valid
        expect(admin.errors.details[:stopped_at]).to include(a_hash_including(error: :invalid_date_format))
      end
    end

    describe 'date_greater_than' do
      it "is invalid if stopped_at is before started_at" do
        admin = build(:admin, started_at: Date.today, stopped_at: Date.yesterday)
        expect(admin).not_to be_valid
        expect(admin.errors.details[:stopped_at]).to include(a_hash_including(error: :date_greate_than))
      end

      it "is invalid if stopped_at is equal to started_at" do
        admin = build(:admin, started_at: Date.today, stopped_at: Date.today)
        admin.validate
        expect(admin.errors.details[:stopped_at]).to include(a_hash_including(error: :date_greate_than))
      end
    end

    describe 'departments_used_by_staff' do
    end

    describe 'remove_department_attributes' do
      context 'on create' do
        it 'does not trigger remove_department_attributes' do
          new_admin = build(:admin)
          new_admin.validate
          expect(new_admin.errors.details[:admin_departments])
            .not_to include(a_hash_including(error: :can_not_remove_department))
        end
      end

      context 'on update' do
        let!(:admin){create(:admin)}
        let!(:departments){create_list(:department, 3)}

        before(:example) do
          admin.admin_departments = departments.map do |dep|
            build(:admin_department, department: dep, admin: admin)
          end
        end

        context 'when update without removing departments' do
          it 'returns nil if there is no removed departments' do
            admin.update(stopped_at: ServerTime.now + 1.day)
            expect(admin.errors.details[:admin_departments])
              .not_to include(a_hash_including(error: :used_by_staff))
            expect(admin.errors.details[:admin_departments])
              .not_to include(a_hash_including(error: :can_not_remove_department))
          end
        end

        context 'when remove departments' do
          before(:example) do
            allow_any_instance_of(AdminDepartment).to receive(:marked_for_destruction?).and_return(true)
          end

          it 'returns nil if department is not used' do
            admin.update(stopped_at: ServerTime.now + 1.day)
            expect(admin.errors.details[:admin_departments])
              .not_to include(a_hash_including(error: :used_by_staff))
            expect(admin.errors.details[:admin_departments])
              .not_to include(a_hash_including(error: :can_not_remove_department))
          end

          it 'adds error if department is used by staff' do
            allow(StaffDepartment).to receive(:by_department)
              .and_return([build(:staff_department)])

            admin.update(stopped_at: ServerTime.now + 1.day)
            expect(admin.errors.details[:admin_departments])
              .to include(a_hash_including(error: :used_by_staff))
          end

          it 'adds error if department is used by corporation_group' do
            allow(CorporationGroup).to receive(:by_pic_id_and_pic_department_id)
              .and_return([build(:corporation_group)])

            admin.update(stopped_at: ServerTime.now + 1.day)
            expect(admin.errors.details[:admin_departments])
              .to include(a_hash_including(error: :can_not_remove_department))
          end

          it 'adds error if department is used by corporation' do
            allow(Corporation).to receive(:by_pic_id_and_pic_department_id)
              .and_return([build(:corporation)])

            admin.update(stopped_at: ServerTime.now + 1.day)
            expect(admin.errors.details[:admin_departments])
              .to include(a_hash_including(error: :can_not_remove_department))
          end
        end
      end
    end

    describe 'must_have_admin_department' do
      it "is invalid if admin_departments are empty" do
        invalid_admin = build(:admin, admin_departments: [])
        invalid_admin.validate

      end
    end
  end

  describe 'Associations' do
    it{should belong_to(:account)}
    it{should belong_to(:role)}
    it{should have_many(:admin_departments)}
    it{should have_many(:departments)}
    it{should have_many(:admin_order_search_conditions)}
    it{should have_many(:admin_staff_search_conditions)}
    it{should have_many(:admin_corporation_search_conditions)}
    it{should have_many(:admin_location_search_conditions)}
    it{should have_many(:admin_training_schedule_search_conditions)}
    it{should have_many(:admin_search_conditions)}
    it{should have_many(:user_search_conditions)}
    it{should have_many(:admin_device_verification_search_conditions)}
    it{should have_many(:admin_device_login_log_search_conditions)}
    it{should have_many(:admin_arrange_search_conditions)}
    it{should have_many(:admin_corporation_group_tag_search_conditions)}
    it{should have_many(:admin_staff_contract_search_conditions)}
    it{should have_many(:foreign_employment_status_search_conditions)}
    it{should have_many(:admin_information_form_search_conditions)}
    it{should have_many(:admin_batch_arrange_search_conditions)}
    it{should have_many(:corporation_group_violations)}
    it{should have_many(:arrange_logs)}
    it{should have_many(:admin_arrange_billing_search_conditions)}
    it{should have_many(:admin_payroll_search_conditions)}
    it{should have_many(:calculate_payroll_logs)}
    it{should have_many(:rank_level_calculation_logs)}
    it{should have_many(:staff_work_condition_export_logs)}
    it{should have_many(:admin_access_user_histories)}
  end

  describe 'Delegations' do
    it {should delegate_method(:tel).to(:account).allow_nil}
    it {should delegate_method(:email).to(:account).allow_nil}
    it {should delegate_method(:name).to(:account).allow_nil}
    it {should delegate_method(:name_kana).to(:account).allow_nil}
    it {should delegate_method(:admin_admin?).to(:role).with_prefix}
    it {should delegate_method(:admin_user?).to(:role).with_prefix}
    it {should delegate_method(:name).to(:role).with_prefix}
    it {should delegate_method(:permissions).to(:role).with_prefix}
    it {should delegate_method(:except_permissions).to(:role).with_prefix}
  end

  describe 'Scopes' do
    describe 'role_admin' do
      it 'filters admin by ROLE_ADMIN' do
        role_admin = Admin.find_by(id: Admin::ROLE_ADMIN) || create(:role, id: Admin::ROLE_ADMIN)
        role_user = Admin.find_by(id: Admin::ROLE_USER) || create(:role, id: Admin::ROLE_USER)
        admin1 = create(:admin, role: role_admin)
        admin2 = create(:admin, role: role_user)

        admins = Admin.role_admin
        expect(admins).to include(admin1)
        expect(admins).not_to include(admin2)
      end

      it 'excludes admin in insurance_receiver_ids' do
        role_admin = Admin.find_by(id: Admin::ROLE_ADMIN) || create(:role, id: Admin::ROLE_ADMIN)
        admin1 = create(:admin, role: role_admin)
        admin2 = create(:admin, role: role_admin)

        allow(Settings).to receive(:insurance_receiver_ids).and_return([admin2.id])

        admins = Admin.role_admin
        expect(admins).to include(admin1)
        expect(admins).not_to include(admin2)
      end
    end

    describe 'active_admins' do
      it 'filters admins who is still active' do
        admin1 = create(:admin, stopped_at: nil)
        admin2 = create(:admin, stopped_at: ServerTime.now + 1.day)
        admin3 = create(:admin)
        admin3.stopped_at = ServerTime.now - 1.day
        admin3.save(validate: false)

        admins = Admin.active_admins
        expect(admins).to include(admin1)
        expect(admins).to include(admin2)
        expect(admins).not_to include(admin3)
      end
    end

    describe 'role_admin_user' do
      it 'filters admin by ROLE_ADMIN and ROLE_USER' do
        role_admin = Admin.find_by(id: Admin::ROLE_ADMIN) || create(:role, id: Admin::ROLE_ADMIN)
        role_user = Admin.find_by(id: Admin::ROLE_USER) || create(:role, id: Admin::ROLE_USER)
        role_other = create(:role)
        admin1 = create(:admin, role: role_admin)
        admin2 = create(:admin, role: role_user)
        admin3 = create(:admin, role: role_other)

        admins = Admin.role_admin_user
        expect(admins).to include(admin1)
        expect(admins).to include(admin2)
        expect(admins).not_to include(admin3)
      end

      it 'excludes admin in insurance_receiver_ids' do
        role_user = Admin.find_by(id: Admin::ROLE_USER) || create(:role, id: Admin::ROLE_USER)
        admin1 = create(:admin, role: role_user)
        admin2 = create(:admin, role: role_user)

        allow(Settings).to receive(:insurance_receiver_ids).and_return([admin2.id])

        admins = Admin.role_admin_user
        expect(admins).to include(admin1)
        expect(admins).not_to include(admin2)
      end

      it 'excludes inactive admin' do
        role_user = Admin.find_by(id: Admin::ROLE_USER) || create(:role, id: Admin::ROLE_USER)
        admin1 = create(:admin, role: role_user, stopped_at: nil)
        admin2 = create(:admin, role: role_user, stopped_at: ServerTime.now + 1.day)
        admin3 = create(:admin, role: role_user)
        admin3.stopped_at = ServerTime.now - 1.day
        admin3.save(validate: false)

        admins = Admin.role_admin_user
        expect(admins).to include(admin1)
        expect(admins).to include(admin2)
        expect(admins).not_to include(admin3)
      end
    end

    describe 'search_by' do
      let!(:with_space) do
        account = create(:account, name: "Hello World")
        create(:admin, account: account)
      end

      let!(:with_jp_space) do
        account = create(:account, name: "Hello　World")
        create(:admin, account: account)
      end

      let!(:without_space) do
        account = create(:account, name: "HelloWorld")
        create(:admin, account: account)
      end

      let(:keyword_with_space) { "Hello World" }
      let(:keyword_with_jp_space) { "Hello　World" }

      context 'when keyword is present' do
        it 'filters records regardless of space type' do
          with_space_scope = Admin.search_by(keyword_with_space)
          expect(with_space_scope).to include(with_space)
          expect(with_space_scope).to include(with_jp_space)
          expect(with_space_scope).to include(without_space)
        end

        it 'filters partial matches' do
          scope = Admin.search_by("Hello")
          expect(scope).to include(with_space)
          expect(scope).to include(with_jp_space)
          expect(scope).to include(without_space)
        end
      end

      context 'when keyword is blank' do
        it 'returns all records' do
          expect(Admin.search_by(nil)).to eq(Admin.all)
          expect(Admin.search_by("")).to eq(Admin.all)
          expect(Admin.search_by("   ")).to eq(Admin.all)
        end
      end
    end

    shared_examples 'active admin with boolean flag' do |flag|
      it "filters active admin who #{flag}" do
        admin1 = create(:admin, flag => true, stopped_at: nil)
        admin2 = create(:admin, flag => true, stopped_at: ServerTime.now + 1.day)
        admin3 = create(:admin, flag => true)
        admin3.stopped_at = ServerTime.now - 1.day
        admin3.save(validate: false)
        admin4 = create(:admin, flag => false, stopped_at: nil)

        admins = Admin.send(flag)
        expect(admins).to include(admin1)
        expect(admins).to include(admin2)
        expect(admins).not_to include(admin3)
        expect(admins).not_to include(admin4)
      end
    end

    it_behaves_like 'active admin with boolean flag', 'is_insurance_mail_receiver'
    it_behaves_like 'active admin with boolean flag', 'can_view_my_number'
    it_behaves_like 'active admin with boolean flag', 'can_update_billing_payment'
    it_behaves_like 'active admin with boolean flag', 'can_create_billing_payment_template'
  end

  describe '#is_expired_login?' do
    context 'when stopped_at is nil' do
      it 'returns nil' do
        admin = build(:admin, stopped_at: nil)
        expect(admin.is_expired_login?).to be_nil
      end
    end

    context 'when stopped_at exists' do
      it 'returns true if admin has stopped' do
        admin = build(:admin, stopped_at: ServerTime.now - 1.day)
        expect(admin.is_expired_login?).to be true
      end

      it 'returns false if admin has not stopped' do
        admin = build(:admin, stopped_at: ServerTime.now + 1.day)
        expect(admin.is_expired_login?).to be false
      end
    end
  end

  describe '#is_started?' do
    context 'when started_at is nil' do
      it 'returns nil' do
        admin = build(:admin, started_at: nil)
        expect(admin.is_started?).to be_nil
      end
    end

    context 'when started_at exists' do
      it 'returns true if admin has started' do
        admin = build(:admin, started_at: ServerTime.now - 1.day)
        expect(admin.is_started?).to be true
      end

      it 'returns false if admin has not started' do
        admin = build(:admin, started_at: ServerTime.now + 1.day)
        expect(admin.is_started?).to be false
      end
    end
  end

  describe '#can_authenticate?' do
    it 'returns true if admin has started and not stopped' do
      admin1 = build(:admin, started_at: ServerTime.now - 1.day, stopped_at: nil)
      admin2 = build(:admin, started_at: ServerTime.now - 1.day, stopped_at: ServerTime.now + 1.day)

      expect(admin1.can_authenticate?).to be true
      expect(admin2.can_authenticate?).to be true
    end

    it 'returns false if admin has not started' do
      admin = build(:admin, started_at: ServerTime.now + 1.day)

      expect(admin.can_authenticate?).to be false
    end

    it 'returns false if admin has stopped' do
      admin = build(:admin, stopped_at: ServerTime.now - 1.day)

      expect(admin.can_authenticate?).to be false
    end
  end

  describe '#active_for_authentication?' do
    it 'calls can_authenticate?' do
      admin = build(:admin)

      expect(admin).to receive(:can_authenticate?)
      admin.active_for_authentication?
    end

    it 'returns true if admin has started and not stopped' do
      admin1 = build(:admin, started_at: ServerTime.now - 1.day, stopped_at: nil)
      admin2 = build(:admin, started_at: ServerTime.now - 1.day, stopped_at: ServerTime.now + 1.day)

      expect(admin1.active_for_authentication?).to be true
      expect(admin2.active_for_authentication?).to be true
    end

    it 'returns false if admin has not started' do
      admin = build(:admin, started_at: ServerTime.now + 1.day)

      expect(admin.active_for_authentication?).to be false
    end

    it 'returns false if admin has stopped' do
      admin = build(:admin, stopped_at: ServerTime.now - 1.day)

      expect(admin.active_for_authentication?).to be false
    end
  end

  describe '#formatted_created_at' do
    it 'returns formatted created_at' do
      now = ServerTime.now
      admin = build(:admin, created_at: now)
      expect(admin.formatted_created_at).to eq(now.strftime('%Y/%m/%d'))
    end
  end

  describe '#can_manage_verification?' do
    context 'when admin has manage permission for DeviceVerification' do
      it 'returns true' do
        admin = build(:admin)
        allow_any_instance_of(Ability).to receive(:can?).and_return(true)
        expect(admin.can_manage_verification?).to be true
      end
    end

    context 'when user lacks manage permission for DeviceVerification' do
      it 'returns false' do
        admin = build(:admin)
        allow_any_instance_of(Ability).to receive(:can?).and_return(false)
        expect(admin.can_manage_verification?).to be false
      end
    end
  end

  describe '#get_search_condition' do
    context 'when cache exists' do
      it 'returns cached value' do
        allow_any_instance_of(Lawson::RedisConnector).to receive(:get)
          .and_return({"foo" => "bar"})

        expect(admin.get_search_condition(Staff)).to eq({"foo" => "bar"})
      end
    end

    context 'when cache is missing' do
      it 'initializes cache and re-fetches it' do
        allow_any_instance_of(Lawson::RedisConnector).to receive(:get)
          .and_return(nil)
        allow_any_instance_of(Lawson::RedisConnector).to receive(:set)
          .and_return("ok")

        expect(admin).to receive(:set_search_condition).with(Staff, {})
        expect(admin.get_search_condition(Staff)).to eq({})
      end
    end
  end

  describe '#set_search_condition' do
    it "saves search condition to redis" do
      allow_any_instance_of(Lawson::RedisConnector).to receive(:set)
        .and_return("ok")

      expect_any_instance_of(Lawson::RedisConnector).to receive(:set)
        .with("admin:#{admin.id}:search_conditions:staff", {"foo" => "bar"}, ex: 1.week)
      admin.set_search_condition(Staff, {"foo" => "bar"})
    end
  end

  describe '.options_for_select' do
    it "returns formatted list of admin name and id" do
      admin_name = admin.name
      admin_id = admin.id
      admin_list = Admin.options_for_select
      expect(admin_list).to be_a(Array)
      expect(admin_list).to include([admin_name, admin_id])
    end
  end

  describe '.ransackable_attributes' do
    it 'returns list of ransackable attributes' do
      expect(described_class.ransackable_attributes).to eq(%w(account_id))
    end
  end

  describe '.ransackable_associations' do
    it 'returns list of ransackable associations' do
      expect(described_class.ransackable_associations).to eq(%w(account))
    end
  end
end
