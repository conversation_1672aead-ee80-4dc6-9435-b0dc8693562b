require 'rails_helper'

RSpec.describe TrainingScheduleCondition, type: :model do
  describe 'Two sessions' do
    let(:first_session) { create(:training_schedule, :training_first_round) }
    let(:second_session) { create(:training_schedule, :training_second_round) }
    let(:schedule_ids) { [first_session.id, second_session.id] }

    describe '#valid_schedule?' do
      subject { described_class.new(schedule_ids).valid_schedule? }

      context 'when schedule is invalid' do
        it { expect(subject[:valid]).to be_truthy }
        it { expect(subject[:error_messages]).to be_empty }
        it { expect(subject[:error_details]).to be_empty }
      end

      context 'when any schedule was deleted' do
        let(:first_session) { create(:training_schedule, :training_first_round, deleted_at: ServerTime.now) }

        it { expect(subject[:valid]).to be_falsey }
        it { expect(subject[:error_messages]).to include(I18n.t("staff.training_schedule.errors.schedule_unavailable")) }
        it { expect(subject[:error_details]).to include(:schedule_was_deleted) }
      end

      context 'when schedule is not found' do
        let(:schedule_ids) { 'invalid_id' }

        it { expect(subject[:valid]).to be_falsey }
        it { expect(subject[:error_messages]).to include(I18n.t("staff.training_schedule.errors.must_select_training_session")) }
        it { expect(subject[:error_details]).to include(:session_empty) }
      end

      context 'when any schedule is full' do
        let(:second_session) { create(:training_schedule, :training_second_round, is_full: true) }

        it { expect(subject[:valid]).to be_falsey }
        it { expect(subject[:error_messages]).to include(I18n.t("staff.training_schedule.errors.schedule_unavailable")) }
        it { expect(subject[:error_details]).to include(:session_is_full) }
      end

      context 'when second round is started before first round' do
        let(:second_session) do
          create(:training_schedule, :training_second_round, start_time: first_session.start_time - 1.day)
        end

        it { expect(subject[:valid]).to be_falsey }
        it { expect(subject[:error_messages]).to include(I18n.t("staff.training_schedule.errors.training_sessions_order")) }
        it { expect(subject[:error_details]).to include(:second_session_after_first_session) }
      end
    end
  end

  describe 'Single session' do
    let(:single_session) { create(:training_schedule, :single_session) }
    let(:schedule_ids) { single_session.id }

    describe '#valid_schedule?' do
      subject { described_class.new(schedule_ids).valid_schedule? }

      context 'when schedule is invalid' do
        it { expect(subject[:valid]).to be_truthy }
        it { expect(subject[:error_messages]).to be_empty }
        it { expect(subject[:error_details]).to be_empty }
      end

      context 'when schedule was deleted' do
        let(:single_session) { create(:training_schedule, :single_session, deleted_at: ServerTime.now) }

        it { expect(subject[:valid]).to be_falsey }
        it { expect(subject[:error_messages]).to include(I18n.t("staff.training_schedule.errors.schedule_unavailable")) }
        it { expect(subject[:error_details]).to include(:schedule_was_deleted) }
      end

      context 'when schedule is not found' do
        let(:schedule_ids) { 'invalid_id' }

        it { expect(subject[:valid]).to be_falsey }
        it { expect(subject[:error_messages]).to include(I18n.t("staff.training_schedule.errors.must_select_training_session")) }
        it { expect(subject[:error_details]).to include(:session_empty) }
      end

      context 'when schedule is full' do
        let(:single_session) { create(:training_schedule, :single_session, is_full: true) }

        it { expect(subject[:valid]).to be_falsey }
        it { expect(subject[:error_messages]).to include(I18n.t("staff.training_schedule.errors.schedule_unavailable")) }
        it { expect(subject[:error_details]).to include(:session_is_full) }
      end
    end
  end
end
