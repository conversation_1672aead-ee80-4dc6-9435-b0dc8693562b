require 'rails_helper'

RSpec.describe Location, type: :model do
  let(:corporation_group) { create(:corporation_group) }
  let(:organization) { create(:organization) }
  let(:prefecture) { create(:prefecture) }
  let(:job_category) { create(:job_category) }
  let(:location) { build(:location, corporation_group: corporation_group, organization: organization, prefecture: prefecture, job_category: job_category) }

  shared_examples 'Location image URL' do |method, matching_const, type|
    it "returns URL matched #{type} location" do
      allow(location).to receive(:get_type).and_return(type)

      matching_type = described_class::LOCATION_TYPE[type]
      expect(location.send(method)).to eq(matching_const[matching_type])
    end
  end

  shared_examples 'Station info' do |method|
    it "returns #{method.humanize} information" do
      trans_time = Faker::Number.number(digits: 2)
      location.send("#{method}_walking_time=", trans_time)

      expect(location.send("#{method}_info"))
        .to include('Tokyo Station')
        .and include("#{I18n.t("admin.location.station_transportation_methods.walking")}#{trans_time}")
    end

    it 'returns nil if no association to station' do
      location.send("#{method}=", nil)

      expect(location.send("#{method}_info")).to be_nil
    end

    it 'does not include transport time if it is blank' do
      location.send("#{method}_walking_time=", nil)
      expect(location.send("#{method}_info")).to eq('Tokyo Station')
    end

    it 'does not raise error when station is not found' do
      allow(location).to receive(described_class::STATION_KEY_AND_ASSOCIATION[method.to_sym].to_sym)
        .and_return(nil)

      expect { location.send("#{method}_info") }.not_to raise_error
    end
  end

  describe 'Instance Methods' do
    let(:location) { build(:location) }

    describe '#prefecture_json_format' do
      let(:location) { build(:location, prefecture: prefecture) }

      it 'returns location data in JSON format for prefecture' do
        expect(location.prefecture_json_format).to a_hash_including(
          id: location.id,
          prefecture_name: prefecture.name,
          name: location.name
        )
      end
    end

    describe '#basic_info' do
      let(:location) { build(:location, prefecture: prefecture, job_category: job_category) }

      it 'returns comprehensive location information' do
        result = location.basic_info

        expect(result).to a_hash_including(
          postal_code: location.postal_code,
          city: [location.prefecture.name, location.city].join(" "),
          address: [location.street_number, location.building].join,
          id: location.id,
          get_type: location.get_type,
          thumbnail_path: location.thumbnail_background_path,
          name: location.name,
          job_category_name: location.job_category_name,
          job_content: location.job_content,
          tel: location.tel,
          fax: location.fax,
          latitude: location.latitude,
          longitude: location.longitude,
          is_store_parking_area_usable: location.is_store_parking_area_usable,
          caution_to_staff: location.caution_to_staff,
          personal_things: location.personal_things,
          clothes: location.clothes,
          special_note: location.special_note
        )
      end
    end

    describe '#prefecture_city' do
      it 'returns combined prefecture and city' do
        location.prefecture = build(:prefecture, name: 'Tokyo')
        location.city = 'Shibuya'
        expect(location.prefecture_city).to eq('TokyoShibuya')
      end
    end

    describe '#stations_info' do
      let(:location) { build(:location) }

      before do
        station_1, station_2, station_3, station_4, station_5 = FactoryBot.create_list(:station, 5)
        location.station_1 = station_1.id
        location.station_2 = station_2.id
        location.station_3 = station_3.id
        location.station_4 = station_4.id
        location.station_5 = station_5.id

        location.save!
      end

      it 'returns station information' do
        expect(location.stations_info).to a_hash_including(
          station_1: include(Station.find(location.station_1).name_with_railway_line),
          station_2: include(Station.find(location.station_2).name_with_railway_line),
          station_3: include(Station.find(location.station_3).name_with_railway_line),
          station_4: include(Station.find(location.station_4).name_with_railway_line),
          station_5: include(Station.find(location.station_5).name_with_railway_line),
        )
      end

      (1..5).to_a.each do |index|
        it "includes transportation information for station_#{index} if has transportation method attribute" do
          trans_time = Faker::Number.number(digits: 2)
          trans_method = %w(walking bus taxi).sample

          location.send("station_#{index}_walking_time=", trans_time)
          location.send("station_#{index}_transportation_method_id=", trans_method)
          result = location.stations_info

          expect(result["station_#{index}".to_sym])
            .to include("#{I18n.t("admin.location.station_transportation_methods.#{trans_method}")}#{trans_time}")
        end
      end
    end

    describe '#stations_info_value_only' do
      before do
        allow(location).to receive(:stations_info).and_return({station_1: 'Station 1', station_2: 'Station 2'})
      end

      it { expect(location.stations_info_value_only).to eq(['Station 1', 'Station 2']) }
    end

    describe '#full_name' do
      it 'returns the location name' do
        expect(location.full_name).to eq(location.name)
      end
    end

    describe '#corporation_name' do
      let(:location) { build(:location, corporation_group: corporation_group) }

      it 'returns the corporation full name' do
        corporation = build(:corporation, name_1: 'Test Corp')

        corporation_group.corporation = corporation
        allow(corporation).to receive(:full_name).and_return('Test Corp Full Name')
        expect(location.corporation_name).to eq('Test Corp Full Name')
      end

      it 'returns empty string when corporation_group is nil' do
        location.corporation_group = nil

        expect(location.corporation_name).to eq('')
      end

      it 'returns empty string when corporation is nil' do
        allow(corporation_group).to receive(:corporation).and_return(nil)

        expect(location.corporation_name).to eq('')
      end

      it 'returns empty string when corporation full name are nil' do
        allow(corporation_group).to receive_message_chain(:corporation, :full_name).and_return(nil)

        expect(location.corporation_name).to eq('')
      end
    end

    describe '#evaluation' do
      it 'returns last evaluation summary' do
        last_evaluation_summary = create(:location_evaluation_summary, location: location)
        allow(location).to receive(:last_evaluation_summaries).and_return([last_evaluation_summary])

        expect(location.evaluation).to eq(last_evaluation_summary)
      end

      it 'returns nil when no evaluation summary' do
        allow(location).to receive(:last_evaluation_summaries).and_return([])

        expect(location.evaluation).to be_nil
      end
    end

    describe '#id_with_leading_zeros' do
      it 'returns ID with leading zeros' do
        location.id = 123
        expect(location.id_with_leading_zeros).to eq('0123')
      end
    end

    describe '#full_address' do
      it 'returns complete address' do
        location.prefecture = build(:prefecture, name: 'Tokyo')
        location.city = 'Shibuya'
        location.street_number = '1-1-1'
        location.building = 'Test Building'

        result = location.full_address
        expect(result).to eq('TokyoShibuya1-1-1Test Building')
      end
    end

    describe '#address' do
      it 'returns street address without prefecture and city' do
        location.street_number = '1-1-1'
        location.building = 'Test Building'

        result = location.address
        expect(result).to eq('1-1-1Test Building')
      end
    end

    describe '#get_coordinates' do
      it 'returns latitude and longitude as array' do
        location.latitude = 35.6762
        location.longitude = 139.6503

        result = location.get_coordinates
        expect(result).to eq([35.6762, 139.6503])
      end
    end

    described_class::STATION_NAME_FIELDS.each do |method|
      describe "#{method}_info" do
        let(:location) do
          build(:location,
            method => create(:station).id,
            "#{method}_walking_time" => 10,
            "#{method}_transportation_method_id": 'walking')
        end

        before do
          allow(location.send(described_class::STATION_KEY_AND_ASSOCIATION[method.to_sym]))
            .to receive(:name_with_railway_line).and_return('Tokyo Station')
        end

        it_behaves_like 'Station info', method
      end
    end

    describe '#station_1_short_info' do
      let(:location) { build(:location, station_1: create(:station, name: 'Kanagawa station').id) }

      it 'returns station_1 short info' do
        trans_time = Faker::Number.number(digits: 2)
        trans_method = %w(walking bus taxi).sample
        location.station_1_walking_time = trans_time
        location.station_1_transportation_method_id = trans_method

        expect(location.station_1_short_info).to include('Kanagawa station')
          .and include(I18n.t("admin.location.station_transportation_methods.#{trans_method}"))
          .and include("#{trans_time}")
      end

      it 'does not include transport time if it is blank' do
        trans_method = %w(walking bus taxi).sample

        location.station_1_walking_time = nil
        location.station_1_transportation_method_id = trans_method

        expect(location.station_1_short_info)
          .not_to include(I18n.t("admin.location.station_transportation_methods.#{trans_method}"))
      end
    end

    describe '#only_name' do
      it 'returns name without location type' do
        location.name = 'ローソン Test Store'

        allow(location).to receive(:get_type).and_return('ローソン')

        expect(location.only_name).to eq('Test Store')
      end
    end

    describe '#is_valid_order_info?' do
      it 'returns true when all required order attributes are present' do
        location.prefecture_id = 1
        location.city = 'Test City'
        location.street_number = '1-1-1'
        location.tel = '************'
        location.postal_code = '123-4567'
        location.is_store_parking_area_usable = true

        expect(location.is_valid_order_info?).to be true
      end

      it 'returns false when required attributes are missing' do
        location.city = nil
        expect(location.is_valid_order_info?).to be false
      end
    end

    describe '#display_order_violation_day' do
      let(:location) { build(:location, corporation_group: corporation_group) }

      it 'returns nil if corporation group violation day is blank' do
        corporation_group.violation_day = nil

        expect(location.display_order_violation_day).to be_nil
      end

      it 'returns corporation group violation day' do
        corporation_group.violation_day = Date.current

        expect(location.display_order_violation_day).to eq(I18n.l(Date.current, format: Settings.date.day_and_date))
      end
    end

    describe '#days_to_violation_day' do
      let(:location) { build(:location, corporation_group: corporation_group) }

      it 'returns nil if corporation group violation day is blank' do
        corporation_group.violation_day = nil

        expect(location.days_to_violation_day).to be_nil
      end

      it 'returns number of days from today to violation day' do
        num_days = Faker::Number.number(digits: 3)
        corporation_group.violation_day = Date.today + num_days

        expect(location.days_to_violation_day).to eq(num_days)
      end

      it 'returns negative number of days from today to violation day' do
        num_days = Faker::Number.number(digits: 3)
        corporation_group.violation_day = Date.today - num_days

        expect(location.days_to_violation_day).to eq(-num_days)
      end
    end

    describe '#closed_day_error_message' do
      it 'returns nil if closed_at is blank' do
        location.closed_at = nil

        expect(location.closed_day_error_message).to be_nil
      end

      it 'returns nil if closed_at is in the future' do
        location.closed_at = 1.day.from_now

        expect(location.closed_day_error_message).to be_nil
      end

      it 'returns message error for closed location' do
        location.closed_at = 1.day.ago

        expect(location.closed_day_error_message)
          .to eq(I18n.t("activerecord.errors.models.order.attributes.location_id.closed_location"))
      end
    end

    describe '#location_not_survey_error_message' do
      let(:location) { create(:location, is_show_survey: true, closed_at: nil, corporation_group: corporation_group) }

      before do
        allow(location).to receive(:location_survey).and_return(nil)
        allow(Order).to receive_message_chain(:by_locations, :where, :exists?).and_return(false)
        allow(corporation_group.corporation).to receive(:labor?).and_return(false)
      end

      it 'returns message error for location is not survey' do
        expect(location.location_not_survey_error_message)
          .to eq(I18n.t("activerecord.errors.models.order.attributes.location_id.not_survey"))
      end

      it 'returns nil if is_show_survey is false' do
        location.is_show_survey = false

        expect(location.location_not_survey_error_message).to be_nil
      end

      it 'returns nil if location has survey' do
        allow(location).to receive(:location_survey).and_return(FactoryBot.build(:location_survey))

        expect(location.location_not_survey_error_message).to be_nil
      end

      it 'returns nil if location is closed' do
        location.closed_at = 1.day.ago

        expect(location.location_not_survey_error_message).to be_nil
      end
    end

    describe '#valid_order_step_1_info' do
      let(:corporation_group) { create(:corporation_group, corporation: create(:corporation)) }
      let(:location) { create(:location, :skip_callback, corporation_group: corporation_group) }

      before do
        allow(location).to receive(:is_valid_order_info?).and_return(true)
        allow(location).to receive(:days_to_violation_day).and_return(1)
        allow(location).to receive(:closed_at).and_return(nil)
        allow(corporation_group.corporation).to receive(:transaction_error_message).and_return(nil)

        allow(OrderLocationDataService).to receive(:new)
          .with(location.id)
          .and_return(instance_double('OrderLocationDataService',
            pics_data: { haken_destination: anything, claim: anything, mandator: anything }
          ))
      end

      context 'when all required conditions are valid' do
        it 'returns true' do
          expect(location.valid_order_step_1_info?).to be true
        end
      end

      context 'when invalid order info' do
        it 'returns false' do
          allow(location).to receive(:is_valid_order_info?).and_return(false)

          expect(location.valid_order_step_1_info?).to be false
        end
      end

      context 'when numbers violation day is not positive' do
        it 'returns false' do
          allow(location).to receive(:days_to_violation_day).and_return(0)

          expect(location.valid_order_step_1_info?).to be false

          allow(location).to receive(:days_to_violation_day).and_return(-1)
          expect(location.valid_order_step_1_info?).to be false

          allow(location).to receive(:days_to_violation_day).and_return(nil)
          expect(location.valid_order_step_1_info?).to be false
        end
      end

      context 'when location is closed' do
        it 'returns false' do
          allow(location).to receive(:closed_at).and_return(Faker::Time.backward)

          expect(location.valid_order_step_1_info?).to be false
        end
      end

      context 'when pics data is blank' do
        it 'returns false' do
          allow(OrderLocationDataService).to receive(:new)
            .with(location.id)
            .and_return(instance_double('OrderLocationDataService',
              pics_data: { haken_destination: nil, claim: anything, mandator: anything }
            ))

          expect(location.valid_order_step_1_info?).to be false
        end
      end

      context 'when corporation has transaction error message' do
        it 'returns false' do
          allow(corporation_group.corporation).to receive(:transaction_error_message).and_return(anything)

          expect(location.valid_order_step_1_info?).to be false
        end
      end
    end

    describe '#station_info' do
      let(:location) { build(:location) }

      before do
        station_1, station_2 = FactoryBot.create_list(:station, 2, railway_line: create(:railway_line))
        location.station_1 = station_1.id
        location.station_2 = station_2.id

        location.save!
      end

      it 'returns the information of stations' do
        expect(location.station_info).to match_array([
          a_hash_including(:line_name, :station_name, :walking_time),
          a_hash_including(:line_name, :station_name, :walking_time)
        ])
      end

      it 'does not raise error when station is not found' do
        allow(Station).to receive(:find_by).and_return(nil)

        expect { location.station_info }.not_to raise_error
      end
    end

    describe '#total_recruiting_job' do
      let(:finish_recruiting_ocs) { build_list(:order_case, 3, status_id: :finish_recruiting) }
      let(:recruiting_ocs) { build_list(:order_case, 3, status_id: :recruiting) }

      it 'returns total recruiting order cases' do
        allow(location).to receive(:order_cases).and_return(finish_recruiting_ocs + recruiting_ocs)
        expect(location.total_recruiting_job).to eq(recruiting_ocs.size)
      end
    end

    describe '#get_type' do
      it 'returns location type for Lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(true)
        location.name = 'ローソン Test Store'
        expect(location.get_type).to eq(Settings.location_type[0])
      end

      it 'returns NOT_LAWSON for non-Lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(false)
        expect(location.get_type).to eq('not_lawson')
      end
    end

    describe '#camelized_type' do
      it 'returns camelized type based on location type' do
        allow(location).to receive(:get_type).and_return(Settings.location_type[0])

        expect(location.camelized_type).to eq(Settings.location_type_camel[0])
      end

      it 'returns camelized type 1 for location type 1' do
        allow(location).to receive(:get_type).and_return(Settings.location_type[1])

        expect(location.camelized_type).to eq(Settings.location_type_camel[1])
      end

      it 'returns camelized type 2 for location type 2' do
        allow(location).to receive(:get_type).and_return(Settings.location_type[2])

        expect(location.camelized_type).to eq(Settings.location_type_camel[2])
      end

      it 'returns camelized type 3 for not_lawson locations' do
        allow(location).to receive(:get_type).and_return('not_lawson')
        expect(location.camelized_type).to eq(Settings.location_type_camel[3])
      end

      it 'returns camelized type based when nil' do
        allow(location).to receive(:get_type).and_return(nil)

        expect(location.camelized_type).to eq(Settings.location_type_camel[0])
      end
    end

    describe '#is_active_at_moment?' do
      it 'returns true when location is not closed' do
        location.closed_at = nil
        expect(location.is_active_at_moment?(Time.current)).to be true
      end

      it 'returns true when closed date is greater than or equal to inputted time' do
        location.closed_at = 1.day.from_now
        expect(location.is_active_at_moment?(Time.current)).to be true
      end

      it 'returns false when closed date is less than inputted time' do
        location.closed_at = 1.day.ago
        expect(location.is_active_at_moment?(Time.current)).to be false
      end
    end

    describe '#thumbnail_path' do
      context 'when corporation is lawson' do
        before do
          allow(location).to receive(:corporation_group_is_lawson).and_return(true)
        end

        described_class::LOCATION_TYPE.keys.each do |location_type|
          it_behaves_like 'Location image URL', :thumbnail_path, described_class::JOB_THUMBNAIL_URL, location_type
        end

        it 'returns URL of lawson when location type is not found' do
          allow(location).to receive(:get_type).and_return(nil)

          expect(location.thumbnail_path).to eq(described_class::JOB_THUMBNAIL_URL[:lawson])
        end
      end

      context 'when corporation is not lawson' do
        let(:location) { build(:location_outside_lawson) }

        before do
          allow(location).to receive(:corporation_group_is_lawson).and_return(false)
        end

        it 'returns custom thumbnail if it is exists' do
          allow(location.thumbnail).to receive_message_chain(:thumbnail_custom, :file, :exists?).and_return(true)

          expect(location.thumbnail_path).to eq(location.thumbnail_url(:thumbnail_custom))
        end

        it 'returns thumbnail url if custom thumbnail is not exists' do
          allow(location.thumbnail).to receive_message_chain(:thumbnail_custom, :file).and_return(nil)

          expect(location.thumbnail_path).to eq(location.thumbnail_url)
        end
      end
    end

    describe '#remove_thumbnail' do
      context 'when corporation is lawson' do
        before do
          allow(location).to receive(:corporation_group_is_lawson).and_return(true)
        end

        it 'removes thumbnail if corporation is lawson and has thumbnail' do
          expect(location).to receive(:remove_thumbnail!).and_call_original
          expect(location).to receive(:save).and_call_original

          location.remove_thumbnail
        end
      end

      context 'when corporation is not lawson' do
        before do
          allow(location).to receive(:corporation_group_is_lawson).and_return(false)
        end

        it 'does not remove thumbnail if corporation is not lawson' do
          expect(location).not_to receive(:remove_thumbnail!).and_call_original
          expect(location).not_to receive(:save).and_call_original

          location.remove_thumbnail
        end
      end
    end

    describe '#logo' do
      context 'when corporation is lawson' do
        before do
          allow(location).to receive(:corporation_group_is_lawson).and_return(true)
        end

        described_class::LOCATION_TYPE.keys.each do |location_type|
          it_behaves_like 'Location image URL', :logo, described_class::JOB_LOGO_URL, location_type
        end

        it 'returns URL of lawson when location type is not found' do
          allow(location).to receive(:get_type).and_return(nil)

          expect(location.logo).to eq(described_class::JOB_LOGO_URL[:lawson])
        end
      end

      context 'when corporation is not lawson' do
        before do
          allow(location).to receive(:corporation_group_is_lawson).and_return(false)
        end

        it 'returns corporation group thumbnail path' do
          expect(location.logo).to eq(location.corporation_group_thumbnail_path)
        end
      end
    end

    describe '#job_categories_text' do
      it 'returns job category name when present' do
        job_category = build(:job_category, name: 'Test Category')
        location.job_category = job_category
        expect(location.job_categories_text).to eq('Test Category')
      end

      it 'returns default lawson job category when job_category_name is nil' do
        allow(location).to receive(:job_category_name).and_return(nil)

        expect(location.job_categories_text).to eq(Settings.lawson_job_category)
      end
    end

    describe '#new_location_survey' do
      let(:location_survey) { build_stubbed(:location_survey) }

      it 'returns location survey if location is not new record and location survey is present' do
        allow(location).to receive(:location_survey).and_return(location_survey)
        result = location.new_location_survey

        expect(result).to eq(location_survey)
        expect(result).to be_persisted
      end

      it 'returns new location survey if location is not new record and location survey is nil' do
        location = FactoryBot.create(:location)
        allow(location).to receive(:location_survey).and_return(nil)
        result = location.new_location_survey

        expect(result).to be_a(LocationSurvey)
        expect(result).not_to be_persisted
        expect(result.location).to eq(location)
      end

      it 'returns nil if location is new record' do
        result = location.new_location_survey

        expect(result).to be_nil
      end
    end

    describe '#thumbnail_backgroud_path' do
      it 'returns thumbnail background url if custom thumbnail background is not exists' do
        allow(location.thumbnail_background)
          .to receive_message_chain(:thumbnail_background_custom, :file)
          .and_return(nil)

        expect(location.thumbnail_background_path).to eq(location.thumbnail_background_url)
      end

      it 'returns custom thumbnail background url if custom thumbnail background is exists' do
        allow(location.thumbnail_background)
          .to receive_message_chain(:thumbnail_background_custom, :file, :exists?)
          .and_return(true)

        expect(location.thumbnail_background_path).to eq(location.thumbnail_background_url(:thumbnail_background_custom))
      end
    end

    describe '#name_and_code' do
      it 'returns combined code and name' do
        location.code = 'TEST001'
        location.name = 'Test Location'
        expect(location.name_and_code).to eq('TEST001 Test Location')
      end
    end

    describe '#only_name' do
      it 'returns name without location type' do
        location.name = 'ローソン Test Store'
        allow(location).to receive(:get_type).and_return('ローソン')
        expect(location.only_name).to eq('Test Store')
      end
    end
  end
end
