require 'rails_helper'

RSpec.describe Location, type: :model do
  describe 'Class methods' do
    describe '.training_center_locs_json_arr' do
      context 'when cache is not present' do
        let!(:prefectures) do
          create_list(:prefecture, 3) do |prefecture|
            create(:location, is_training_center: true, prefecture: prefecture)
          end
        end

        before do
          allow(RedisModels::TrainingCenter).to receive(:get_cache).and_return(nil)
        end

        it 'returns JSON string of training center locations' do
          result = described_class.training_center_locs_json_arr

          expect(result).to be_a(String)
          expect(JSON.parse(result)).to all(a_hash_including('id', 'name', 'prefecture_name'))
        end

        it 'returns sorted by prefecture if settings is present' do
          Settings.prefectures_sorting_order = [prefectures[2].name]

          results = JSON.parse(described_class.training_center_locs_json_arr).map{|location| location['prefecture_name']}

          expect(results).to eq([prefectures[2].name, prefectures[0].name, prefectures[1].name])
        end
      end

      context 'when cache is present' do
        before do
          allow(RedisModels::TrainingCenter).to receive(:get_cache).and_return('{"foo": "bar"}')
        end

        it 'returns cached content' do
          result = described_class.training_center_locs_json_arr

          expect(result).to eq('{"foo": "bar"}')
        end
      end
    end

    describe '.categories_by_locations' do
      before do
        create_list(:location, 3) do |location|
          create(:location_job_category, job_category_id: 1, location: location)
          create(:location_job_category, job_category_id: 2, location: location)
        end
      end

      it 'returns list of object data by each location and location job category for location' do
        result = described_class.categories_by_locations(Location.ids)

        expect(result.size).to eq(9)
        expect(result).to all(a_hash_including('id', 'latitude', 'longitude', 'city',
          'is_store_parking_area_usable', 'station_1_info', 'thumbnail_path', 'logo',
          'job_categories_text', 'station_1_short_info', 'only_name', 'corporation_name', 'prefecture_name',
          'job_category_key', 'location_job_category_id'))
      end

      it 'returns pagination by location if page and per_page are present' do
        result = described_class.categories_by_locations(Location.ids, 1, 2)

        expect(result.size).to eq(6)
      end
    end

    describe '.options_for_select_with_corporation' do
      let(:corporation) { create(:corporation) }
      let!(:locations) { create_list(:location, 3, corporation_group: create(:corporation_group, corporation: corporation)) }

      it 'returns list of corporation name and id' do
        result = described_class.options_for_select_with_corporation(corporation.id)

        expect(result).to match_array(locations.pluck(:name, :id))
      end
    end

    describe '.options_for_select_training_schedule_form' do
      let!(:training_center_locs) { create_list(:location, 3, is_training_center: true) }

      it 'returns list of training center location name and id' do
        result = described_class.options_for_select_training_schedule_form

        expect(result).to match_array(training_center_locs.pluck(:name, :id))
      end
    end
  end
end
