require 'rails_helper'

RSpec.describe Location, type: :model do
  describe 'Constants' do
    it 'has SEARCH_ADDRESS_ATTRS constant' do
      expect(described_class::SEARCH_ADDRESS_ATTRS).to match_array(%w(city street_number building prefecture_name))
    end

    it 'has LIMIT_STATIONS constant' do
      expect(described_class::LIMIT_STATIONS).to eq(20)
    end

    it 'has REQUIRE_ORDER_ATTRS constant' do
      expect(described_class::REQUIRE_ORDER_ATTRS).to match_array(%i(prefecture_id city street_number tel postal_code))
    end

    it 'has BOOLEAN_ATTRS constant' do
      expect(described_class::BOOLEAN_ATTRS).to match_array(%i(is_store_parking_area_usable))
    end

    it 'has PIC_TYPES constant' do
      expect(described_class::PIC_TYPES).to match_array(%i(haken_destination claim mandator))
    end

    it 'has STATION_NAME_FIELDS constant' do
      expect(described_class::STATION_NAME_FIELDS).to match_array(%w(station_1 station_2 station_3 station_4 station_5))
    end

    it 'has DEFAULT_SURVEY_ANSWER constant' do
      expect(described_class::DEFAULT_SURVEY_ANSWER).to eq({answer_18: true})
    end

    it 'has LOCATION_TYPE constant' do
      expect(described_class::LOCATION_TYPE).to eq({Settings.location_type[0] => :lawson,
        Settings.location_type[1] => :nature_lawson, Settings.location_type[2] => :lawson_store_100})
    end

    it 'has JOB_LOGO_URL constant' do
      expect(described_class::JOB_LOGO_URL.keys).to match_array(%i(lawson nature_lawson lawson_store_100))
      expect(described_class::JOB_LOGO_URL).to a_hash_including(
        lawson: a_string_including('lawson_logo_update', '.png'),
        nature_lawson: a_string_including('natural-lawson', '.png'),
        lawson_store_100: a_string_including('lawson-store', '.png')
      )
    end

    it 'has JOB_THUMBNAIL_URL constant' do
      expect(described_class::JOB_THUMBNAIL_URL.keys).to match_array(%i(lawson nature_lawson lawson_store_100))

      expect(described_class::JOB_THUMBNAIL_URL).to a_hash_including(
        lawson: a_string_including('bg_img', '.png'),
        nature_lawson: a_string_including('nature_lawson', '.jpg'),
        lawson_store_100: a_string_including('lawson_store', '.jpg')
      )
    end

    it 'has NOT_LAWSON constant' do
      expect(described_class::NOT_LAWSON).to eq("not_lawson")
    end

    it 'has STATION_KEY_AND_ASSOCIATION constant' do
      expect(described_class::STATION_KEY_AND_ASSOCIATION)
        .to eq({station_1: "stations_1", station_2: "stations_2", station_3: "stations_3",
          station_4: "stations_4", station_5: "stations_5"})
    end

    it 'has OUTSIDE_LAWSON_ATTRS constant' do
      expect(described_class::OUTSIDE_LAWSON_ATTRS).to match_array(%i(job_content personal_things clothes special_note))
    end

    it 'has API_BASIC_INFO constant' do
      expect(described_class::API_BASIC_INFO)
        .to match_array(%i(id postal_code name job_content tel fax latitude longitude is_store_parking_area_usable
          caution_to_staff personal_things clothes special_note city))
    end

    it 'has JOB_SEARCH_ATTRS constant' do
      expect(described_class::JOB_SEARCH_ATTRS).to match_array(%i(id latitude longitude city is_store_parking_area_usable))
    end

    it 'has JOB_SEARCH_METHODS constant' do
      expect(described_class::JOB_SEARCH_METHODS)
        .to match_array(%i(station_1_info thumbnail_path logo job_categories_text station_1_short_info only_name
          corporation_name prefecture_name))
    end

    it 'has LOCATION_ATTRIBUTES constant' do
      expect(described_class::LOCATION_ATTRIBUTE)
        .to match_array(%i(code location_type name name_kana short_name corporation_group_id
          organization_id note postal_code prefecture_id city street_number building tel fax
          email station_1 station_1_walking_time station_2 station_2_walking_time
          station_3 station_3_walking_time station_4 station_4_walking_time department_id
          station_5 station_5_walking_time is_store_parking_area_usable is_approval_required
          haken_acceptance_started_at closed_at pos_type_id station_1_transportation_method_id
          station_2_transportation_method_id station_3_transportation_method_id
          station_4_transportation_method_id station_5_transportation_method_id is_export_timesheet disable_create_survey
          is_auto_matching default_invoice_target
          caution_to_staff caution_to_staff_mail thumbnail thumbnail_background
          is_jacket is_pants is_shoes job_category_id is_training_center) <<
          described_class::OUTSIDE_LAWSON_ATTRS << [location_pics_attributes: %i(id pic_type_id department
            position name tel fax cellphone email note _destroy)] <<
            [priority_staffs_attributes: %i(id location_id staff_id _destroy)] <<
              {location_survey_attributes: LocationSurvey.column_names.map(&:to_sym)})
    end

    it 'has ORDER_ATTR_JSON constant' do
      expect(described_class::ORDER_ATTR_JSON)
        .to match_array(%i(id name postal_code city street_number building tel fax
          is_store_parking_area_usable overall_started_at organization_id organization_full_name
          default_invoice_target))
    end

    it 'has CATEGORY_ATTRS contsant' do
      expect(described_class::CATEGORY_ATTRS)
        .to match_array(%i(job_content caution_to_staff personal_things clothes special_note))
    end
  end

  describe 'Enums' do
    it do
      is_expected.to define_enum_for(:pos_type_id)
        .with_values(old_pos: 1, new_pos: 2, no_pos: 3)
    end

    it do
      is_expected.to define_enum_for(:station_1_transportation_method_id)
        .with_values(walking: 0, bus: 1, taxi: 2)
        .with_prefix(true)
    end

    it do
      is_expected.to define_enum_for(:station_2_transportation_method_id)
        .with_values(walking: 0, bus: 1, taxi: 2)
        .with_prefix(true)
    end

    it do
      is_expected.to define_enum_for(:station_3_transportation_method_id)
        .with_values(walking: 0, bus: 1, taxi: 2)
        .with_prefix(true)
    end

    it do
      is_expected.to define_enum_for(:station_4_transportation_method_id)
        .with_values(walking: 0, bus: 1, taxi: 2)
        .with_prefix(true)
    end

    it do
      is_expected.to define_enum_for(:station_5_transportation_method_id)
        .with_values(walking: 0, bus: 1, taxi: 2)
        .with_prefix(true)
    end

    it do
      is_expected.to define_enum_for(:default_invoice_target)
        .with_values(lawson_invoice: 0, separate_invoice: 1)
    end
  end

  describe 'Modules' do
    it_behaves_like 'FormatPostalCodeBeforeSaveExamples', :postal_code
  end

  describe 'Delegations' do
    %i(full_name violation_day_str violation_day is_lawson thumbnail_path).each do |method|
      it { should delegate_method(method).to(:corporation_group).with_prefix(true).allow_nil }
    end

    it { should delegate_method(:id).to(:location_survey).with_prefix(true).allow_nil }

    it { should delegate_method(:full_name).to(:organization).with_prefix(true).allow_nil }

    it { should delegate_method(:name).to(:prefecture).with_prefix(true).allow_nil }

    %i(name station_name).each do |method|
      it { should delegate_method(method).to(:stations_1).with_prefix(true).allow_nil }
    end

    it { should delegate_method(:name).to(:stations_2).with_prefix(true).allow_nil }\

    it { should delegate_method(:name).to(:stations_3).with_prefix(true).allow_nil }

    it { should delegate_method(:name).to(:stations_4).with_prefix(true).allow_nil }

    it { should delegate_method(:name).to(:stations_5).with_prefix(true).allow_nil }

    it { should delegate_method(:name).to(:job_category).with_prefix(true).allow_nil }

    it { should delegate_method(:name).to(:department).with_prefix(true).allow_nil}
  end

  describe 'Acts as paranoid' do
    let(:location) { create(:location, :skip_callback) }

    it 'soft deletes the record' do
      location.destroy
      expect(described_class.find_by(id: location.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: location.id)).to eq(location)
    end

    it 'restores the soft deleted record' do
      location.destroy
      location.restore
      expect(described_class.find_by(id: location.id)).to eq(location)
    end
  end

  describe 'Uploaders' do
    describe '#thumbnail' do
      it 'mounts LocationImageFileUploader on :thumbnail' do
        expect(described_class.new.thumbnail).to be_an_instance_of(LocationImageFileUploader)
      end

      context 'when handling file uploads' do
        let(:file_path) { Rails.root.join('spec/fixtures/files/sample.png') }
        let(:uploaded_file) { Rack::Test::UploadedFile.new(file_path) }
        let!(:location) { create(:location, :skip_callback, thumbnail: uploaded_file) }

        it 'stores the uploaded file' do
          expect(location.thumbnail.file).to exist
          expect(File.basename(location.thumbnail.path)).to be_present
        end

        it 'allows removing the attachment via remove_thumbnail' do
          location.remove_thumbnail = true
          location.save(validate: false)
          location.reload
          expect(location.thumbnail.file).to be_nil
        end
      end
    end

    describe '#thumbnail_background' do
      it 'mounts LocationImageFileUploader on :thumbnail_background' do
        expect(described_class.new.thumbnail_background).to be_an_instance_of(LocationImageFileUploader)
      end

      context 'when handling file uploads' do
        let(:file_path) { Rails.root.join('spec/fixtures/files/sample.png') }
        let(:uploaded_file) { Rack::Test::UploadedFile.new(file_path) }
        let!(:location) { create(:location, :skip_callback, thumbnail_background: uploaded_file) }

        it 'stores the uploaded file' do
          expect(location.thumbnail_background.file).to exist
          expect(File.basename(location.thumbnail_background.path)).to be_present
        end

        it 'allows removing the attachment via remove_thumbnail_background' do
          location.remove_thumbnail_background = true
          location.save(validate: false)
          location.reload
          expect(location.thumbnail_background.file).to be_nil
        end
      end
    end
  end

  describe 'Nested attributes' do
    let(:parent) { create(:location) }

    describe '.location_pics' do
      let(:nested_attribute_params) { FactoryBot.attributes_for_list(:location_pic, 2) }

      it_behaves_like 'Nested attributes with allow_destroy', :location_pics
    end

    describe '.location_survey' do
      let(:nested_attribute_params) { FactoryBot.attributes_for(:location_survey) }

      before do
        allow(User).to receive(:by_role_can_survey).and_return(anything)
      end

      it 'creates nested attribute' do
        parent.assign_attributes(location_survey_attributes: nested_attribute_params)

        expect { parent.save! }.to change { LocationSurvey.count }.by(1)
      end

      it 'cannot create nested attribute if location is new record' do
        parent = FactoryBot.build(:location)
        parent.assign_attributes(location_survey_attributes: nested_attribute_params)

        expect { parent.save! }.not_to change { LocationSurvey.count }
      end

      it 'cannot create nested attribute if no any user can survey' do
        allow(User).to receive(:by_role_can_survey).and_return([])
        parent.assign_attributes(location_survey_attributes: nested_attribute_params)

        expect { parent.save! }.not_to change { LocationSurvey.count }
      end

      it 'cannot create nested attribute if disable_create_survey is passed' do
        parent.assign_attributes(location_survey_attributes: nested_attribute_params, disable_create_survey: '1')

        expect { parent.save! }.not_to change { LocationSurvey.count }
      end
    end

    describe '.priority_staffs' do
      let(:nested_attribute_params) { FactoryBot.attributes_for_list(:priority_staff, 2) }

      it_behaves_like 'Nested attributes with allow_destroy', :priority_staffs
    end
  end
end
