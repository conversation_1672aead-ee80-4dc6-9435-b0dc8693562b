require 'rails_helper'

RSpec.describe Location, type: :model do
  let(:corporation_group) { create(:corporation_group) }
  let(:organization) { create(:organization) }
  let(:prefecture) { create(:prefecture) }
  let(:job_category) { create(:job_category) }
  let(:location) do
    build(:location,
      corporation_group: corporation_group, organization: organization,
      prefecture: prefecture, job_category: job_category)
  end

  describe 'Callbacks' do
    describe 'before_save :refresh_cached_content' do
      it 'calls refresh_cached_content when is_training_center changes' do
        expect(location).to receive(:refresh_cached_content).and_call_original
        expect(RedisModels::TrainingCenter).to receive(:del_cache).and_call_original

        location.is_training_center = true
        location.save
      end

      it 'does not call refresh_cached_content when is_training_center does not change' do
        location.save # Initial save
        expect(location).not_to receive(:refresh_cached_content)
        expect(RedisModels::TrainingCenter).not_to receive(:del_cache)

        location.name = 'New Name'
        location.save
      end
    end

    describe 'before_save :set_lat_long' do
      it 'calls set_lat_long when address fields change' do
        expect(location).to receive(:set_lat_long_value).and_call_original
        location.postal_code = '123-4567'
        location.save
      end

      it 'does not call set_lat_long when address fields do not change' do
        location.save # Initial save
        expect(location).not_to receive(:set_lat_long_value)
        location.name = 'New Name'
        location.save
      end
    end

    describe 'before_save :set_outside_lawson_attr' do
      it 'clears outside lawson attributes for lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(true)
        location.job_content = 'Test content'
        location.personal_things = 'Test personal'
        location.save

        expect(location.job_content).to eq('')
        expect(location.personal_things).to eq('')
      end

      it 'preserves outside lawson attributes for non-lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(false)
        location.job_content = 'Test content'
        location.personal_things = 'Test personal'
        location.save

        expect(location.job_content).to eq('Test content')
        expect(location.personal_things).to eq('Test personal')
      end
    end

    describe 'after_create :create_user_locations_for_owner' do
      it 'creates user locations for corporation owners' do
        corporation = create(:corporation, :with_users)
        location.corporation_group = create(:corporation_group, corporation: corporation)

        expect { location.save }.to change { UserLocation.count }
      end
    end

    describe 'after_update :cancel_order_case_and_arrangement_after_closed_at' do
      let(:location) { create(:location, closed_at: nil) }

      it 'cancels arranged after closed at' do
        location.closed_at = Date.current
        expect(CancelArrangeAfterClosedAtWorker).to receive(:perform_async)

        location.save!
      end

      it 'does not cancel arranged after closed at if closed at unchanged' do
        location.closed_at = nil
        location.name = 'Test name'
        expect(CancelArrangeAfterClosedAtWorker).not_to receive(:perform_async)

        location.save!
      end
    end
  end
end
