require 'rails_helper'

RSpec.describe Location, type: :model do
  describe 'Scopes' do
    before(:all) do
      create_list(:location, 5)
    end

    describe '.by_corporation_group_id' do
      it { expect(described_class.by_corporation_group_id(1)).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_corporation_id' do
      it { expect(described_class.by_corporation_id(1)).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_configured_store_com' do
      it { expect(described_class.by_configured_store_com).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_not_configured_store_com' do
      it { expect(described_class.by_not_configured_store_com).to be_an(ActiveRecord::Relation) }
    end

    describe '.search_and_sort_by_ids' do
      it { expect(described_class.search_and_sort_by_ids([1,2,3])).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_name' do
      it { expect(described_class.by_name('name')).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_code' do
      it { expect(described_class.by_code('code')).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_surveyed' do
      it { expect(described_class.by_surveyed).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_not_survey' do
      it { expect(described_class.by_not_survey).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_has_violation_file' do
      it { expect(described_class.by_has_violation_file).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_addresses' do
      it { expect(described_class.by_addresses('test')).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_violation_day' do
      it { expect(described_class.by_violation_day('2025/01/01', '2025/02/01')).to be_an(ActiveRecord::Relation) }

      it { expect(described_class.by_violation_day('2025/01/01', nil)).to be_an(ActiveRecord::Relation) }

      it { expect(described_class.by_violation_day(nil, '2025/01/01')).to be_an(ActiveRecord::Relation) }

      it { expect(described_class.by_violation_day(nil, 'invalid')).to be_an(ActiveRecord::Relation) }
    end

    describe '.search_all_text' do
      it { expect(described_class.search_all_text('')).to eq(described_class.all)}

      it { expect(described_class.search_all_text('test')).to be_an(ActiveRecord::Relation) }

      it { expect(described_class.search_all_text('test', %w(prefecture_name))).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_order_case_from' do
      it { expect(described_class.by_order_case_from('2025/01/01')).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_order_case_to' do
      it { expect(described_class.by_order_case_to('2025/01/01')).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_organization_id' do
      it { expect(described_class.by_organization_id(1)).to be_an(ActiveRecord::Relation) }
    end

    describe '.search_by' do
      it { expect(described_class.search_by('')).to eq(described_class.all) }

      it { expect(described_class.search_by('test')).to be_an(ActiveRecord::Relation) }

      it { expect(described_class.search_by('test', %w(city))).to be_an(ActiveRecord::Relation) }
    end

    describe '.where_keep_order' do
      it { expect(described_class.where_keep_order([1,2,3])).to be_an(ActiveRecord::Relation) }
    end

    describe '.is_active' do
      it { expect(described_class.is_active).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_ids' do
      it { expect(described_class.by_ids([1,2,3])).to be_an(ActiveRecord::Relation) }
    end

    describe '.closed' do
      it { expect(described_class.closed).to be_an(ActiveRecord::Relation) }
    end

    describe '.not_closed_until' do
      it { expect(described_class.not_closed_until(Date.today)).to be_an(ActiveRecord::Relation) }
    end

    describe '.by_pic_department_id' do
      it { expect(described_class.by_pic_department_id(1)).to be_an(ActiveRecord::Relation) }
    end

    describe '.filter_by_payment_rates' do
      it { expect(described_class.filter_by_payment_rates(true)).to be_an(ActiveRecord::Relation) }

      it { expect(described_class.filter_by_payment_rates(false, [1,2,3])).to be_an(ActiveRecord::Relation) }

      it { expect(described_class.filter_by_payment_rates(false, [1,2,3], 'test')).to be_an(ActiveRecord::Relation) }
    end

    describe '.not_auto_matching' do
      it { expect(described_class.not_auto_matching).to be_an(ActiveRecord::Relation) }
    end

    describe '.training_center_locs' do
      it { expect(described_class.training_center_locs).to be_an(ActiveRecord::Relation) }
    end
  end
end
