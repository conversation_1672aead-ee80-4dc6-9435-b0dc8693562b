require 'rails_helper'

RSpec.describe Location, type: :model do
  describe 'Validations' do
    describe 'presence validations' do
      it { should validate_presence_of(:name) }
      it { should validate_presence_of(:creator_id) }
      it { should validate_presence_of(:updater_id) }
      it { should validate_presence_of(:organization_id) }
      it { should validate_presence_of(:corporation_group_id) }
      it { should validate_presence_of(:postal_code) }
      it { should validate_presence_of(:prefecture_id) }
      it { should validate_presence_of(:city) }
      it { should validate_presence_of(:street_number) }
      it { should validate_presence_of(:tel) }
      it { should validate_presence_of(:short_name) }
      it { should validate_presence_of(:job_category_id) }
    end

    describe 'format validations' do
      it_behaves_like 'FormatRuleValidation', :name_kana,
        valid: ['ﾃｽﾄﾅｶﾔﾏ'],
        invalid: ['テスト中山'],
        type: :katakana_name

      it_behaves_like 'FormatRuleValidation', :code,
        valid: ['abcXYZ123', '<EMAIL>'],
        invalid: ['hello world', 'abc~xyz', 'abc\tdef']

      it_behaves_like 'FormatRuleValidation', :postal_code,
        valid: ['123-4567'],
        invalid: ['123-4124124asd']

      it_behaves_like 'FormatRuleValidation', :email,
        valid: ['<EMAIL>'],
        invalid: ['hello world', 'abc~xyz', 'abc\tdef', 'email@example']

      it_behaves_like 'FormatRuleValidation', :tel,
        valid: ['03-1234-5678', '1234567890'],
        invalid: ['123456789', '12345 abc']

      it_behaves_like 'FormatRuleValidation', :fax,
        valid: ['03-1234-5678', '1234567890'],
        invalid: ['123456789', '12345 abc']

      it_behaves_like 'FormatRuleValidation', :name,
        valid: ['テスト中山', 'TEst'],
        invalid: ['#Test', 'テスト中山?']

      it_behaves_like 'ValidDateValidation', :closed_at,
        valid: ['2025-01-01', '2025/01/01'],
        invalid: ['2025-01-01T00:00:00+09:00', '2025-01-01 00:00:00 +0900',
          '2025-01-01 00:00:00', '2025-01-32', '2025/1/1', '2025/13/01']
    end

    describe 'numericality validations' do
      %i(station_1_walking_time station_2_walking_time station_3_walking_time station_4_walking_time
        station_5_walking_time).each do |attribute|
          it do
            should validate_numericality_of(attribute)
              .only_integer
              .is_greater_than_or_equal_to(0)
              .allow_nil
          end
        end
    end

    describe 'length validations' do
      described_class::OUTSIDE_LAWSON_ATTRS.each do |attribute|
        it { should validate_length_of(attribute).is_at_most(255) }
      end
    end

    describe 'inclusion validations' do
      it { should validate_inclusion_of(:location_type).in_array(Settings.location_type) }
    end

    describe '.exists_order_after_closed_at' do
      let(:location) { create(:location, closed_at: nil) }

      it 'is valid if no orders after closed_at' do
        location.closed_at = Date.current

        allow(location.orders).to receive(:exist_order_less_than_closed_at)
          .with(location.closed_at)
          .and_return(false)

        expect(location).to be_valid
      end

      it 'is invalid if there are orders after closed_at' do
        location.closed_at = Date.current

        allow(location.orders).to receive(:exist_order_less_than_closed_at)
          .with(location.closed_at)
          .and_return(true)

        expect(location).not_to be_valid
        expect(location.errors.details[:closed_at]).to include(a_hash_including(error: :exists_order_after_closed_at))
      end
    end

    describe '.uniqueness_of_pritority_staffs' do
      let(:staff) { create(:staff) }
      let(:location_attributes) { FactoryBot.attributes_for(:location) }
      let(:priority_staff_params) do
        [
          { staff_id: staff.id },
          { staff_id: create(:staff).id },
        ]
      end

      context 'on create' do
        it 'is valid if no duplication staff' do
          location_attributes[:priority_staffs_attributes] = priority_staff_params
          location = described_class.new(location_attributes)

          expect(location).to be_valid
        end

        it 'is invalid if duplication staff' do
          priority_staff_params.push({ staff_id: staff.id })
          location_attributes[:priority_staffs_attributes] = priority_staff_params
          location = described_class.new(location_attributes)

          expect(location).not_to be_valid
          expect(location.errors.details[:id]).to include(a_hash_including(error: :invalid))
        end
      end

      context 'on update' do
        let(:location) { described_class.new(location_attributes.merge(priority_staffs_attributes: priority_staff_params)) }

        before do
          location.save
        end

        it 'is valid if no duplication staff' do
          priority_staffs_attributes = location.priority_staffs.as_json.push({ staff_id: create(:staff).id })
          location.assign_attributes(priority_staffs_attributes: priority_staffs_attributes)

          expect(location).to be_valid
        end

        it 'is invalid if duplication staff' do
          priority_staffs_attributes = location.priority_staffs.as_json.push({ staff_id: staff.id })
          location.assign_attributes(priority_staffs_attributes: priority_staffs_attributes)

          expect(location).not_to be_valid
          expect(location.errors.details[:id]).to include(a_hash_including(error: :invalid))
        end

        it 'is valid if duplication staff but _destroy is set' do
          priority_staffs_attributes = location.priority_staffs.as_json.push({ staff_id: staff.id, _destroy: true })
          location.assign_attributes(priority_staffs_attributes: priority_staffs_attributes)

          expect(location).to be_valid
        end
      end
    end

    describe '.must_input_thumbnail' do
      let(:location) { build(:location, thumbnail: nil, thumbnail_background: nil) }
      let(:uploaded_file) { Rack::Test::UploadedFile.new(Rails.root.join('spec/fixtures/files/sample.png')) }

      it 'is valid if corporation is lawson' do
        allow(location.corporation_group).to receive(:is_lawson).and_return(true)

        expect(location).to be_valid
      end

      it 'is valid if corporation is not lawson and thumbnail is set' do
        allow(location.corporation_group).to receive(:is_lawson).and_return(false)

        location.thumbnail = Rack::Test::UploadedFile.new(uploaded_file)
        location.thumbnail_background = Rack::Test::UploadedFile.new(uploaded_file)

        expect(location).to be_valid
      end

      it 'is invalid if corporation is not lawson and thumbnail is not set' do
        allow(location.corporation_group).to receive(:is_lawson).and_return(false)

        expect(location).not_to be_valid
        expect(location.errors.details[:thumbnail]).to include(a_hash_including(error: :blank))
        expect(location.errors.details[:thumbnail_background]).to include(a_hash_including(error: :invalid))
      end
    end
  end
end
