require 'rails_helper'

RSpec.describe Location, type: :model do
  let(:location) { build(:location, :skip_callback) }

  describe 'Associations' do
    it { should belong_to(:corporation_group) }
    it { should belong_to(:organization) }
    it { should belong_to(:prefecture).optional }
    it { should belong_to(:stations_1).class_name('Station').optional }
    it { should belong_to(:stations_2).class_name('Station').optional }
    it { should belong_to(:stations_3).class_name('Station').optional }
    it { should belong_to(:stations_4).class_name('Station').optional }
    it { should belong_to(:stations_5).class_name('Station').optional }
    it { should belong_to(:job_category) }
    it { should belong_to(:department).class_name('Department').optional }

    it { should have_many(:user_locations).dependent(:destroy) }
    it { should have_many(:location_job_categories).dependent(:destroy) }
    it { should have_many(:user_order_case_search_conditions).dependent(:destroy) }
    it { should have_many(:users).through(:user_locations) }
    it { should have_many(:location_pics).dependent(:destroy) }
    it { should have_many(:orders).dependent(:restrict_with_error) }
    it { should have_many(:order_cases).through(:orders) }
    it { should have_many(:staff_apply_order_cases).dependent(:destroy) }
    it { should have_many(:staff_like_locations).dependent(:destroy) }
    it { should have_many(:location_evaluation_summaries).dependent(:destroy) }
    it { should have_many(:location_evaluations).dependent(:destroy) }
    it { should have_many(:staff_complaints).dependent(:destroy) }
    it { should have_many(:staff_messages).dependent(:destroy) }
    it { should have_many(:user_arrange_billing_search_conditions).dependent(:destroy) }
    it { should have_many(:owner_notifications).dependent(:destroy) }

    context '.last_evaluation_summaries' do
      it 'should have many last_evaluation_summaries' do
        should have_many(:last_evaluation_summaries).class_name('LocationEvaluationSummary')
          .with_foreign_key(:location_id)
      end

      it 'should use scope last_updated' do
        summaries = create_list(:location_evaluation_summary, 5, location: location)

        expect(location.last_evaluation_summaries)
          .to eq(LocationEvaluationSummary.where(id: summaries.map(&:id)).last_updated)
      end
    end

    it { should have_many(:priority_staffs).dependent(:destroy) }
    it { should have_many(:order_templates).dependent(:destroy) }
    it { should have_many(:location_payment_rates).dependent(:destroy) }
    it { should have_many(:billing_payment_templates).dependent(:destroy) }

    it { should have_one(:location_survey).dependent(:destroy) }

    context '.now_base_price' do
      it 'should have one now_base_price' do
        should have_one(:now_base_price).class_name('LocationPaymentRate')
      end

      it 'should use scope current_price and order by start_date' do
        current = create(:location_payment_rate, :skip_callback,
          location: location, start_date:  4.days.ago, end_date: Date.current)

        past = create(:location_payment_rate, :skip_callback,
          location: location, start_date:  10.days.ago, end_date: 5.days.ago)

        future = create(:location_payment_rate, location: location, start_date: 3.day.from_now)

        expect(location.now_base_price).to eq(current)
      end
    end

    context '.new_base_price' do
      it 'should have one new_base_price' do
        should have_one(:new_base_price).class_name('LocationPaymentRate')
      end

      it 'should use scope new_price and order by start_date' do
        current = create(:location_payment_rate, :skip_callback,
          location: location, start_date:  4.days.ago, end_date: Date.current)

        past = create(:location_payment_rate, :skip_callback,
          location: location, start_date:  10.days.ago, end_date: 5.days.ago)

        future = create(:location_payment_rate, location: location, start_date: 3.day.from_now)

        expect(location.reload.new_base_price).to eq(future)
      end
    end
  end
end
