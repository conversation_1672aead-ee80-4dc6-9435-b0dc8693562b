require 'rails_helper'

RSpec.describe StaffDepartment, type: :model do
  describe 'Associations' do
    it { should belong_to(:staff) }
    it { should belong_to(:department) }
  end

  describe 'Validations' do
    let(:staff) { create(:staff) }
    let(:staff_department) { build(:staff_department) }

    it 'should validate presence of affiliation date' do
      staff_department.affiliation_date = nil

      expect(staff_department).not_to be_valid
      expect(staff_department.errors[:affiliation_date])
        .to include(I18n.t('activerecord.errors.models.staff_department.attributes.blank'))
    end

    it 'should validate presence of department' do
      staff_department.department = nil

      expect(staff_department).not_to be_valid
      expect(staff_department.errors[:department])
        .to include(I18n.t('activerecord.errors.models.staff_department.attributes.blank'))
    end

    it 'should validate uniqueness of affiliation date of staff' do
      exists_department = FactoryBot.create(:staff_department, staff: staff, affiliation_date: Date.current)
      staff_department.staff = staff
      staff_department.affiliation_date = Date.current

      expect(staff_department).not_to be_valid
      expect(staff_department.errors[:affiliation_date])
        .to include(I18n.t('activerecord.errors.messages.affiliation_date_taken'))
    end
  end

  describe 'Delegations' do
    it { should delegate_method(:name).to(:department).with_prefix(true) }
    it { should delegate_method(:name_kana).to(:department).with_prefix(true) }
  end

  describe 'Scopes' do
    let(:time) { Faker::Time.backward }
    let!(:department1) { create(:department) }
    let!(:department2) { create(:department) }
    let!(:staff_department1) { create(:staff_department, department: department1, affiliation_date: time + 1.day) }
    let!(:staff_department2) { create(:staff_department, department: department2, affiliation_date: time - 1.day) }

    describe '.by_department' do
      it 'returns staff departments by department ids' do
        result = described_class.by_department([department1.id])

        expect(result).to include(staff_department1)
        expect(result).not_to include(staff_department2)
      end
    end

    describe '.affiliation_before_date' do
      it 'returns staff departments affiliated before the specified date' do
        result = described_class.affiliation_before_date(time)

        expect(result).to include(staff_department2)
        expect(result).not_to include(staff_department1)
      end
    end

    describe '.order_by_affiliation_date_before_now' do
      it 'returns staff departments ordered by affiliation date before now' do
        result = described_class.order_by_affiliation_date_before_now
        expect(result).to eq([staff_department1, staff_department2])
      end
    end
  end

  describe 'Instance methods' do
    let(:staff_department) { build(:staff_department) }

    describe '#add_affiliation_date_taken_error' do
      it 'adds an affiliation date taken error' do
        staff_department.add_affiliation_date_taken_error
        expect(staff_department.errors[:affiliation_date]).to include(I18n.t('activerecord.errors.messages.affiliation_date_taken'))
      end
    end

    describe '#affiliation_date_format' do
      it 'returns the formatted affiliation date' do
        date = Date.new(2023, 1, 1)
        staff_department.affiliation_date = date
        expect(staff_department.affiliation_date_format).to eq(date.strftime(Settings.date.formats))
      end

      it 'returns nil if affiliation date is nil' do
        staff_department.affiliation_date = nil
        expect(staff_department.affiliation_date_format).to be_nil
      end
    end
  end

  describe 'Acts as paranoid' do
    let!(:staff_department) { create(:staff_department) }

    it 'soft deletes the record' do
      staff_department.destroy
      expect(described_class.find_by(id: staff_department.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: staff_department.id)).to eq(staff_department)
    end

    it 'restores the soft deleted record' do
      staff_department.destroy
      staff_department.restore
      expect(described_class.find_by(id: staff_department.id)).to eq(staff_department)
    end
  end
end
