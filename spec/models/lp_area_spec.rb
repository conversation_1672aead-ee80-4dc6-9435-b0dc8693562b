require 'rails_helper'

RSpec.describe LpArea, type: :model do
  describe 'Associations' do
    it{should belong_to(:department)}

    it{should belong_to(:prefecture)}

    it{should have_many(:lp_jobs)}

    it{should have_many(:lp_training_centers)}
  end

  describe 'Scopes' do
    describe '.by_name' do
      let!(:lp_area1) { create(:lp_area, name: 'Tokyo') }
      let!(:lp_area2) { create(:lp_area, name: 'Osaka') }

      it 'returns lp_areas matching the name' do
        expect(described_class.by_name('Tokyo')).to include(lp_area1)
        expect(described_class.by_name('Tokyo')).not_to include(lp_area2)
      end

      it 'returns lp_areas matching the name with partial match' do
        expect(described_class.by_name('Tok')).to include(lp_area1)
        expect(described_class.by_name('Tok')).not_to include(lp_area2)
      end

      it 'returns empty array if no lp_area matches the name' do
        expect(described_class.by_name('Kyoto')).to be_empty
      end
    end
  end

  describe 'Instance methods' do
    describe '#tel' do
      it 'returns the default tel' do
        lp_area = create(:lp_area)
        expect(lp_area.tel).to eq(FormatDisplay::OwnerHomepage::DEFAULT_TEL)
      end
    end
  end

  describe 'Class methods' do
    describe '.ransackable_attributes' do
      it 'returns the searchable attributes' do
        expect(described_class.ransackable_attributes).to eq(%w(name))
      end
    end
  end
end
