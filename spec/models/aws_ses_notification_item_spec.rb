require "rails_helper"

RSpec.describe AwsSesNotificationItem, type: :model do
  let(:complaint_item) do
    sqs_message = Aws::SQS::Types::Message.new(
      body: {
        "Message": {
          "notificationType": "Complaint",
          "complaint": {
            "feedbackId": "000000",
            "complaintSubType": "null",
            "complainedRecipients": [
              {
                "emailAddress": "<EMAIL>"
              }
            ],
            "timestamp": "2024-07-12T23:03:52.000Z",
            "userAgent": "example.com",
            "complaintFeedbackType": "abuse",
            "arrivalDate": "2024-07-05T23:00:13.000Z"
          },
          "mail": {
            "timestamp": "2024-07-05T23:00:13.265Z",
            "source": "=?UTF-00000=?= <<EMAIL>>",
            "sourceArn": "arn:aws:ses:us-west-2:00000:identity/workz.jp",
            "sourceIp": "0.0.0.0",
            "callerIdentity": "lss-hs-prd-bastion",
            "sendingAccountId": "00000",
            "messageId": "000000",
            "destination": ["<EMAIL>"]
          }
        }.to_json
      }.to_json
    )
    AwsSesNotificationItem.new(sqs_message)
  end

  let(:complaint_item_missing_mail_destination) do
    sqs_message = Aws::SQS::Types::Message.new(
      body: {
        "Message": {
          "notificationType": "Complaint",
          "complaint": {
            "feedbackId": "000000",
            "complaintSubType": "null",
            "complainedRecipients": [
              {
                "emailAddress": "<EMAIL>"
              }
            ],
            "timestamp": "2024-07-12T23:03:52.000Z",
            "userAgent": "example.com",
            "complaintFeedbackType": "abuse",
            "arrivalDate": "2024-07-05T23:00:13.000Z"
          },
          "mail": {
            "timestamp": "2024-07-05T23:00:13.265Z",
            "source": "=?UTF-00000=?= <<EMAIL>>",
            "sourceArn": "arn:aws:ses:us-west-2:00000:identity/workz.jp",
            "sourceIp": "0.0.0.0",
            "callerIdentity": "lss-hs-prd-bastion",
            "sendingAccountId": "00000",
            "messageId": "000000"
          }
        }.to_json
      }.to_json
    )
    AwsSesNotificationItem.new(sqs_message)
  end

  let(:bounce_permanent_item) do
    sqs_message = Aws::SQS::Types::Message.new(
      body: {
        "Message": {
          "notificationType": "Bounce",
          "bounce": {
            "feedbackId": "000000",
            "bounceType": "Permanent",
            "bounceSubType": "Suppressed",
            "bouncedRecipients": [
              {
                "emailAddress": "<EMAIL>",
                "action": "failed",
                "status": "5.1.1",
                "diagnosticCode": "Amazon SES has suppressed sending to this address."
              }
            ],
            "timestamp": "2025-01-20T11:51:11.000Z",
            "reportingMTA": "dns; amazonses.com"
          },
          "mail": {
            "timestamp": "2025-01-20T11:51:11.795Z",
            "source": "=?UTF-8?=?= <<EMAIL>>",
            "sourceArn": "arn:aws:ses:us-west-2:00000:identity/workz.jp",
            "sourceIp": "0.0.0.0",
            "callerIdentity": "lss-hs-prd-web",
            "sendingAccountId": "00000",
            "messageId": "000000",
            "destination": ["<EMAIL>"]
          }
        }.to_json
      }.to_json
    )
    AwsSesNotificationItem.new(sqs_message)
  end

  let(:bounce_transient_item) do
    sqs_message = Aws::SQS::Types::Message.new(
      body: {
        "Message": {
          "notificationType": "Bounce",
          "bounce": {
            "feedbackId": "000000",
            "bounceType": "Transient",
            "bounceSubType": "MailboxFull",
            "bouncedRecipients": [
              {
                "emailAddress": "<EMAIL>",
                "action": "failed",
                "status": "5.2.2",
                "diagnosticCode": "User is over quota"
              }
            ],
            "timestamp": "2025-01-15T01:26:50.000Z",
            "remoteMtaIp": "************",
            "reportingMTA": "dns; a27-66.smtp-out.us-west-2.amazonses.com"
          },
          "mail": {
            "timestamp": "2025-01-15T01:26:48.286Z",
            "source": "=?UTF-00000=?= <<EMAIL>>",
            "sourceArn": "arn:aws:ses:us-west-2:00000:identity/workz.jp",
            "sourceIp": "0.0.0.0",
            "callerIdentity": "lss-hs-prd-web",
            "sendingAccountId": "00000",
            "messageId": "000000",
            "destination": ["<EMAIL>"]
          }
        }.to_json
      }.to_json
    )
    AwsSesNotificationItem.new(sqs_message)
  end

  it do
    expect(complaint_item).to be_a(AwsSesNotificationItem)
    expect(complaint_item_missing_mail_destination).to be_a(AwsSesNotificationItem)
    expect(bounce_permanent_item).to be_a(AwsSesNotificationItem)
    expect(bounce_transient_item).to be_a(AwsSesNotificationItem)
  end

  describe "#new" do
    it "raises ArgumentError" do
      expect{AwsSesNotificationItem.new}.to raise_error(ArgumentError)
    end

    it "raises CustomExceptions::NotSqsMessageError" do
      expect{AwsSesNotificationItem.new({})}.to raise_error(CustomExceptions::NotSqsMessageError)
    end

    it "raises CustomExceptions::InvalidJsonError" do
      expect{AwsSesNotificationItem.new(Aws::SQS::Types::Message.new(body: {"Message": "NotAJson"}.to_json))}
        .to raise_error(CustomExceptions::InvalidJsonError)
    end

    it "initializes new complaint item" do
      expect(complaint_item.source).to eq("<EMAIL>")
      expect(complaint_item.destination).to eq("<EMAIL>")
      expect(complaint_item.notification_type).to eq("Complaint")
      expect(complaint_item.complaint_feedback_type).to eq("abuse")
      expect(complaint_item.bounce_type).to be_nil
      expect(complaint_item.bounce_sub_type).to be_nil
      expect(complaint_item.bounce_diagnosis).to be_nil
    end

    it "initializes new bounce_permanent item" do
      expect(bounce_permanent_item.source).to eq("<EMAIL>")
      expect(bounce_permanent_item.destination).to eq("<EMAIL>")
      expect(bounce_permanent_item.notification_type).to eq("Bounce")
      expect(bounce_permanent_item.bounce_type).to eq("Permanent")
      expect(bounce_permanent_item.bounce_sub_type).to eq("Suppressed")
      expect(bounce_permanent_item.bounce_diagnosis).to eq("Amazon SES has suppressed sending to this address.")
      expect(bounce_permanent_item.complaint_feedback_type).to be_nil
    end

    it "initializes new bounce_transient item" do
      expect(bounce_transient_item.source).to eq("<EMAIL>")
      expect(bounce_transient_item.destination).to eq("<EMAIL>")
      expect(bounce_transient_item.notification_type).to eq("Bounce")
      expect(bounce_transient_item.bounce_type).to eq("Transient")
      expect(bounce_transient_item.bounce_sub_type).to eq("MailboxFull")
      expect(bounce_transient_item.bounce_diagnosis).to eq("User is over quota")
      expect(bounce_transient_item.complaint_feedback_type).to be_nil
    end
  end

  describe "#bounce?" do
    it "returns true if mail is bounce" do
      expect(bounce_permanent_item.bounce?).to be true
    end

    it "returns false if mail is not bounce" do
      expect(complaint_item.bounce?).to be false
      expect(complaint_item_missing_mail_destination.bounce?).to be false
    end
  end

  describe "#complaint?" do
    it "returns true if mail is complaint" do
      expect(complaint_item.complaint?).to be true
      expect(complaint_item_missing_mail_destination.complaint?).to be true
    end

    it "returns false if mail is not complaint" do
      expect(bounce_permanent_item.complaint?).to be false
    end
  end

  describe "#bounce_permanent?" do
    it "returns true if mail is bounce_permanent" do
      expect(bounce_permanent_item.bounce_permanent?).to be true
    end

    it "returns false if mail is not bounce_permanent" do
      expect(bounce_transient_item.bounce_permanent?).to be false
    end
  end

  describe "#bounce_transient?" do
    it "returns true if mail is bounce_transient" do
      expect(bounce_transient_item.bounce_transient?).to be true
    end

    it "returns false if mail is not bounce_transient" do
      expect(bounce_permanent_item.bounce_transient?).to be false
    end
  end
end
