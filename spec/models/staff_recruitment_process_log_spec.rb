require 'rails_helper'

RSpec.describe StaffRecruitmentProcessLog, type: :model do
  describe 'Associations' do
    it { should belong_to(:staff) }
  end

  describe 'Validations' do
    it { should validate_presence_of(:staff_id) }
  end

  describe 'Acts as Paranoid' do
    let(:staff_recruitment_process_log) { create(:staff_recruitment_process_log) }

    it 'soft deletes the record' do
      staff_recruitment_process_log.destroy
      expect(described_class.find_by(id: staff_recruitment_process_log.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: staff_recruitment_process_log.id))
        .to eq(staff_recruitment_process_log)
    end

    it 'restores the soft deleted record' do
      staff_recruitment_process_log.destroy
      staff_recruitment_process_log.restore
      expect(described_class.find_by(id: staff_recruitment_process_log.id)).to eq(staff_recruitment_process_log)
    end
  end
end
