require 'rails_helper'

RSpec.describe ArrangeBilling, type: :model do
  describe 'Constants' do
    it 'defines BILLING_FIELD_ATTRS' do
      expect(described_class::BILLING_FIELD_ATTRS).to eq(%i(billing_field_1 billing_field_2 billing_field_3
        billing_field_4 billing_field_5 billing_field_6 billing_field_7
        billing_tax_exemption billing_other_addition_fee))
    end

    it 'defines BILLING_AMOUNT_FIELDS' do
      expect(described_class::BILLING_AMOUNT_FIELDS).to eq([:billing_basic_unit_price, :billing_night_unit_price,
        :billing_unit_price_addition, :billing_ot_time, :billing_late_time, :billing_leave_early_time,
        :billing_basic_time, :billing_actual_working_time, :billing_night_time])
    end

    it 'defines UPDATABLE_ATTRS' do
      expect(described_class::UPDATABLE_ATTRS).to eq(described_class::BILLING_FIELD_ATTRS +
        described_class::BILLING_AMOUNT_FIELDS +
        [:billing_started_at, :billing_ended_at, :adjusment_type_id, :billing_note])
    end

    it 'defines BILLING_TIMES' do
      expect(described_class::BILLING_TIMES).to eq(%w(started ended))
    end

    it 'defines REST_TIMES' do
      expect(described_class::REST_TIMES).to eq(%w(1 2 3))
    end

    it 'defines COPY_FIELD_ATTRS' do
      expect(described_class::COPY_FIELD_ATTRS).to eq(%w(billing_field_1 billing_field_2 billing_field_5 billing_field_6
        billing_field_7 billing_tax_exemption))
    end

    it 'defines UPDATABLE_BILLING_FIELDS' do
      expect(described_class::UPDATABLE_BILLING_FIELDS).to eq([:billing_basic_unit_price, :billing_night_unit_price, :billing_field_1,
        :billing_field_2, :billing_field_3])
    end
  end

  describe 'Associations' do
    it { should belong_to(:arrangement) }
  end

  describe 'Validations' do
    subject(:arrange_billing) { FactoryBot.build(:arrange_billing) }

    describe 'integer number' do
      it 'validates billing_basic_unit_price is an integer' do
        arrange_billing.billing_basic_unit_price = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_basic_unit_price])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_night_unit_price is an integer' do
        arrange_billing.billing_night_unit_price = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_night_unit_price])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_unit_price_addition is an integer' do
        arrange_billing.billing_unit_price_addition = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_unit_price_addition])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_ot_time is an integer' do
        arrange_billing.billing_ot_time = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_ot_time])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_late_time is an integer' do
        arrange_billing.billing_late_time = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_late_time])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_leave_early_time is an integer' do
        arrange_billing.billing_leave_early_time = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_leave_early_time])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_basic_time is an integer' do
        arrange_billing.billing_basic_time = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_basic_time])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_actual_working_time is an integer' do
        arrange_billing.billing_actual_working_time = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_actual_working_time])
          .to include(a_hash_including(error: :not_a_number))
      end

      it 'validates billing_night_time is an integer' do
        arrange_billing.billing_night_time = 'abc'
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_night_time])
          .to include(a_hash_including(error: :not_a_number))
      end
    end

    it 'validates billing_ended_at is greater than billing_started_at' do
      arrange_billing = FactoryBot.build(:arrange_billing,
        billing_started_at: Time.current, billing_ended_at: Time.current - 1.day)
      expect(arrange_billing).not_to be_valid
      expect(arrange_billing.errors.details[:billing_ended_at])
        .to include(a_hash_including(error: :value_greater_than))
    end

    it { should validate_presence_of(:arrangement_id) }

    describe 'when regular order' do
      subject(:arrange_billing) { FactoryBot.build(:arrange_billing, is_regular_order: true) }

      it { should validate_presence_of(:billing_basic_unit_price) }
      it { should validate_presence_of(:billing_night_unit_price) }
    end

    it 'validates record_locked when is_locked? and is_validate_locked' do
      arrange_billing = FactoryBot.build(:arrange_billing, is_validate_locked: true)
      allow(arrange_billing).to receive(:is_locked?).and_return(true)
      expect(arrange_billing).not_to be_valid
      expect(arrange_billing.errors.details[:record_locked]).to include(a_hash_including(error: :record_locked))
    end

    describe 'validate fields when inputed' do
      subject(:arrange_billing) { FactoryBot.build(:arrange_billing, is_regular_order: true) }

      it 'skips when is not regular order' do
        arrange_billing.is_regular_order = false
        expect(arrange_billing).to be_valid
      end

      it 'is valid when billing night unit price is greater than billing basic unit price' do
        arrange_billing.billing_basic_unit_price = 50
        arrange_billing.billing_night_unit_price = 100
        expect(arrange_billing).to be_valid
      end

      it 'is valid when billing night unit price is equal to billing basic unit price' do
        arrange_billing.billing_basic_unit_price = 100
        arrange_billing.billing_night_unit_price = 100
        expect(arrange_billing).to be_valid
      end

      it 'is invalid when regular order and billing night unit price is less than billing basic unit price' do
        arrange_billing.billing_basic_unit_price = 100
        arrange_billing.billing_night_unit_price = 50
        expect(arrange_billing).not_to be_valid
        expect(arrange_billing.errors.details[:billing_night_unit_price])
          .to include(a_hash_including(error: :greater_than_billing_basic_unit_price))
      end
    end
  end

  describe 'Enums' do
    it { should define_enum_for(:adjusment_type_id).with_values(normal: 1, adjust: 2) }
  end

  describe 'Callbacks' do
    describe 'before validation' do
      let(:arrangement) { FactoryBot.create(:arrangement, working_started_at: '2025-01-05 08:00:00'.in_time_zone) }
      let(:arrange_billing) { FactoryBot.build(:arrange_billing, arrangement: arrangement) }

      before do
        arrange_billing.is_manual_update = true
      end

      it 'does not set billing time when is_manual_update is not provided' do
        arrange_billing.is_manual_update = nil

        expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_started_at)
        expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_ended_at)
      end

      context 'when record is new' do
        it 'does not change billing date when billing_started_at is not assigned before' do
          arrange_billing.billing_started_at = nil

          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_started_at)
          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_ended_at)
        end

        it 'does not change billing date when billing_ended_at is not assigned before' do
          arrange_billing.billing_ended_at = nil

          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_started_at)
          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_ended_at)
        end

        it 'sets billing date by working started date' do
          arrange_billing.billing_started_at = Time.zone.parse('2025-01-01 10:00:00')
          arrange_billing.billing_ended_at = Time.zone.parse('2025-01-01 12:00:00')

          arrange_billing.valid?

          expect(arrange_billing.billing_started_at).to eq(Time.zone.parse('2025-01-05 10:00:00'))
          expect(arrange_billing.billing_ended_at).to eq(Time.zone.parse('2025-01-05 12:00:00'))
        end

        it 'sets billing ended at to next day when ended at is less than started at' do
          arrange_billing.billing_started_at = Time.zone.parse('2025-01-01 22:00:00')
          arrange_billing.billing_ended_at = Time.zone.parse('2025-01-01 02:00:00')

          arrange_billing.valid?

          expect(arrange_billing.billing_started_at).to eq(Time.zone.parse('2025-01-05 22:00:00'))
          expect(arrange_billing.billing_ended_at).to eq(Time.zone.parse('2025-01-06 02:00:00'))
        end
      end

      context 'when record is not new' do
        let(:arrange_billing) do
          FactoryBot.create(:arrange_billing, arrangement: arrangement,
            billing_started_at: Time.zone.parse('2025-01-01 10:00:00'),
            billing_ended_at: Time.zone.parse('2025-01-01 12:00:00'))
        end

        it 'does not change billing time when billing_started_at is not present or assigned before' do
          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_started_at)
          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_ended_at)
        end

        it 'does not change billing time when billing_ended_at is not present or assigned before' do
          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_started_at)
          expect { arrange_billing.valid? }.not_to change(arrange_billing, :billing_ended_at)
        end

        it 'sets billing time by inputted billing_started_at' do
          arrange_billing.billing_started_at = Time.zone.parse('2025-01-05 08:00:00')
          arrange_billing.billing_ended_at = Time.zone.parse('2025-01-05 14:00:00')

          arrange_billing.valid?

          expect(arrange_billing.billing_started_at).to eq(Time.zone.parse('2025-01-01 08:00:00'))
          expect(arrange_billing.billing_ended_at).to eq(Time.zone.parse('2025-01-01 14:00:00'))
        end

        it 'sets billing ended at to next day when ended at is less than started at' do
          arrange_billing.billing_started_at = Time.zone.parse('2025-01-05 22:00:00')
          arrange_billing.billing_ended_at = Time.zone.parse('2025-01-05 02:00:00')

          arrange_billing.valid?

          expect(arrange_billing.billing_started_at).to eq(Time.zone.parse('2025-01-01 22:00:00'))
          expect(arrange_billing.billing_ended_at).to eq(Time.zone.parse('2025-01-02 02:00:00'))
        end
      end
    end
  end

  describe 'Scopes' do
    describe '.filter_billing_in_range' do
      let(:time) { Time.zone.parse('2025-01-01 10:00:00') }
      let(:arrangement) { FactoryBot.create(:arrangement, working_started_at: time) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.filter_billing_in_range(time, time + 1.day)).to contain_exactly(arrange_billing)
        expect(described_class.filter_billing_in_range(time + 1.day, time + 2.day)).to be_empty
      end
    end

    describe '.by_regist_history' do
      let(:staff) { FactoryBot.create(:staff, registration_history: 10) }
      let(:arrangement) { FactoryBot.create(:arrangement, staff: staff) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_regist_history(10)).to contain_exactly(arrange_billing)
        expect(described_class.by_regist_history(20)).to be_empty
      end
    end

    describe '.by_where_keep_order' do
      let!(:arrange_billing1) { FactoryBot.create(:arrange_billing) }
      let!(:arrange_billing2) { FactoryBot.create(:arrange_billing) }

      it { expect(described_class.where_keep_order([arrange_billing1.id, arrange_billing2.id]))
        .to eq([arrange_billing1, arrange_billing2]) }
    end

    describe '.by_working_status' do
      let(:arrangement) { FactoryBot.create(:arrangement) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      before do
        FactoryBot.create(:work_achievement, arrangement: arrangement, working_time_status_id: 1)
      end

      it 'returns correct records' do
        expect(described_class.by_working_status(1)).to contain_exactly(arrange_billing)
        expect(described_class.by_working_status([:not_inputted, :staff_approved])).to contain_exactly(arrange_billing)
        expect(described_class.by_working_status(2)).to be_empty
      end
    end

    describe '.by_corporation_id' do
      let(:arrangement) do
        FactoryBot.create(:arrangement, order: create(:order, :skip_callback, corporation_id: 10))
      end

      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_corporation_id(10)).to contain_exactly(arrange_billing)
        expect(described_class.by_corporation_id(20)).to be_empty
      end
    end

    describe '.by_arrangement_status' do
      let(:arrangement) { FactoryBot.create(:arrangement, display_status_id: 1) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_arrangement_status(1)).to contain_exactly(arrange_billing)
        expect(described_class.by_arrangement_status(2)).to be_empty
      end
    end

    describe '.by_order_portion_status' do
      let(:arrangement) do
        FactoryBot.create(:arrangement, order_portion: create(:order_portion, :skip_callback, status_id: 1))
      end

      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_order_portion_status(1)).to contain_exactly(arrange_billing)
        expect(described_class.by_order_portion_status(2)).to be_empty
      end
    end

    describe '.by_segment_haken' do
      let(:arrangement) do
        FactoryBot.create(:arrangement, order_case: create(:order_case, :skip_callback, segment_id: [1, 7].sample))
      end

      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }
      let!(:arrange_billing2) do
        FactoryBot.create(:arrange_billing, arrangement: create(:arrangement,
          order_case: create(:order_case, :skip_callback, segment_id: 2)))
      end

      it 'returns correct records' do
        expect(described_class.by_segment_haken).to include(arrange_billing)
        expect(described_class.by_segment_haken).not_to include(arrange_billing2)
      end
    end

    describe '.by_arrangement_ids' do
      let(:arrangement) { FactoryBot.create(:arrangement) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_arrangement_ids([arrangement.id])).to contain_exactly(arrange_billing)
        expect(described_class.by_arrangement_ids([arrangement.id + 1])).to be_empty
      end
    end

    describe '.not_lawson_staff' do
      let(:arrangement1) do
        FactoryBot.create(:arrangement,
          order: create(:order, :skip_callback,
            corporation: create(:corporation, is_lawson_staff: false)))
      end

      let(:arrangement2) do
        FactoryBot.create(:arrangement,
          order: create(:order, :skip_callback,
            corporation: create(:corporation, is_lawson_staff: true)))
      end

      let!(:arrange_billing1) { FactoryBot.create(:arrange_billing, arrangement: arrangement1) }
      let!(:arrange_billing2) { FactoryBot.create(:arrange_billing, arrangement: arrangement2) }

      it 'returns correct records' do
        results = described_class.not_lawson_staff

        expect(results).to include(arrange_billing1)
        expect(results).not_to include(arrange_billing2)
      end
    end

    describe '.is_corporation_lawson' do
      let(:arrangement) do
        FactoryBot.create(:arrangement,
          order: create(:order, :skip_callback, corporation: create(:corporation, :is_lawson)))
      end

      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.is_corporation_lawson(true)).to include(arrange_billing)
        expect(described_class.is_corporation_lawson(false)).to be_empty
      end
    end

    describe '.billings_locked' do
      let!(:arrange_billing) do
        FactoryBot.create(:arrange_billing, :skip_callback,
          arrangement: create(:arrangement, :skip_callback, is_billing_locked: true))
      end

      it { expect(described_class.billings_locked).to include(arrange_billing) }
    end

    describe '.from_start_date' do
      let!(:arrange_billing) do
        create(:arrange_billing,
          billing_started_at: Time.zone.parse('2025-01-01 08:00:00'),
          arrangement: create(:arrangement)
        )
      end

      it 'returns correct records' do
        expect(described_class.from_start_date('2025/01/01')).to contain_exactly(arrange_billing)
        expect(described_class.from_start_date('2025/01/02')).to be_empty
      end
    end

    describe '.to_start_date' do
      let!(:arrange_billing) do
        create(:arrange_billing,
          billing_started_at: Time.zone.parse('2025-01-01 08:00:00'),
          arrangement: create(:arrangement)
        )
      end

      it 'returns correct records' do
        expect(described_class.to_start_date('2025/01/01')).to contain_exactly(arrange_billing)
        expect(described_class.to_start_date('2024/12/31')).to be_empty
      end
    end

    describe '.by_billing_locked' do
      let(:arrangement) { FactoryBot.create(:arrangement, :skip_callback, is_billing_locked: true) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, :skip_callback, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_billing_locked(true)).to contain_exactly(arrange_billing)
        expect(described_class.by_billing_locked(false)).to be_empty
      end
    end

    describe '.by_billing_confirmed' do
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, is_billing_confirmed: true) }

      it 'returns correct records' do
        expect(described_class.by_billing_confirmed(true)).to contain_exactly(arrange_billing)
        expect(described_class.by_billing_confirmed(false)).to be_empty
      end
    end

    describe '.by_corporation_group_id' do
      let(:arrangement) { create(:arrangement, order: create(:order, :skip_callback, corporation_group_id: 1)) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_corporation_group_id([1])).to contain_exactly(arrange_billing)
        expect(described_class.by_corporation_group_id([2])).to be_empty
      end
    end

    describe '.by_corporation_group_tag_id' do
      let(:order) do
        create(:order, :skip_callback,
          corporation_group: create(:corporation_group, corporation_group_tag_id: 1))
      end

      let(:arrangement) { FactoryBot.create(:arrangement, order: order) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_corporation_group_tag_id([1])).to contain_exactly(arrange_billing)
        expect(described_class.by_corporation_group_tag_id([2])).to be_empty
      end
    end

    describe '.by_corporation' do
      let(:arrangement) { FactoryBot.create(:arrangement, order: create(:order, :skip_callback, corporation_id: 1)) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_corporation([1])).to contain_exactly(arrange_billing)
        expect(described_class.by_corporation([2])).to be_empty
      end
    end

    describe '.by_location' do
      let(:arrangement) { FactoryBot.create(:arrangement, order: create(:order, :skip_callback, location_id: 1)) }
      let!(:arrange_billing) { FactoryBot.create(:arrange_billing, arrangement: arrangement) }

      it 'returns correct records' do
        expect(described_class.by_location([1])).to contain_exactly(arrange_billing)
        expect(described_class.by_location([2])).to be_empty
      end
    end

    describe '.sum_data_billing' do
      before do
        create(:arrange_billing, billing_total_amount: 1000, billing_actual_working_time: 100)
        create(:arrange_billing, billing_total_amount: 2000, billing_actual_working_time: 200)
      end

      it 'returns correct sum data' do
        results = described_class.sum_data_billing.first
        expect(results.total_billing_total_amount).to eq(3000)
        expect(results.total_billing_actual_working_time).to eq(300)
        expect(results.total_count).to eq(2)
      end
    end
  end

  describe 'Delegations' do
    let(:account) { FactoryBot.build(:account, name: "Account Name", name_kana: "アカウントネーム") }
    let(:staff) { FactoryBot.build(:staff, staff_number: "123456", account: account) }
    let(:department) { FactoryBot.build(:department, name: "Department Name") }
    let(:location) { FactoryBot.build(:location, name: "Location Name", code: "123456", department: department) }
    let(:corporation) { FactoryBot.build(:corporation, name_1: "Corporation Name") }
    let(:order) { FactoryBot.build(:order, location: location, corporation: corporation) }
    let(:order_case) { FactoryBot.build(:order_case, case_started_at: Time.zone.parse("2025-01-01 10:00:00")) }
    let(:arrangement) { FactoryBot.build(:arrangement, order: order, staff: staff, order_case: order_case) }
    let(:arrange_billing) { FactoryBot.build(:arrange_billing, arrangement: arrangement) }

    it 'delegates order_location_code to arrangement' do
      expect(arrange_billing.order_location_code).to eq("123456")
    end

    it 'delegates order_pic_department_name to arrangement' do
      expect(arrange_billing.order_pic_department_name).to eq("Department Name")
    end

    it 'delegates order_location_name to arrangement' do
      expect(arrange_billing.order_location_name).to eq("Location Name")
    end

    it 'delegates staff_account_name to arrangement' do
      expect(arrange_billing.staff_account_name).to eq("Account Name")
    end

    it 'delegates order_corporation_full_name to arrangement' do
      expect(arrange_billing.order_corporation_full_name).to eq("Corporation Name")
    end

    it 'delegates order_corporation_id to arrangement' do
      expect(arrange_billing.order_corporation_id).to eq(order.corporation_id)
    end

    it 'delegates order_case_case_started_at to arrangement' do
      expect(arrange_billing.order_case_case_started_at).to eq(Time.zone.parse("2025-01-01 10:00:00"))
    end

    it 'delegates work_achievement_working_time_status_id to arrangement' do
      FactoryBot.build(:work_achievement, arrangement: arrangement, working_time_status_id: "owner_approved")

      expect(arrange_billing.work_achievement_working_time_status_id).to eq("owner_approved")
    end

    it 'delegates staff_id to arrangement' do
      expect(arrange_billing.staff_id).to eq(arrangement.staff_id)
    end

    it 'delegates staff_account_name_kana to arrangement' do
      expect(arrange_billing.staff_account_name_kana).to eq("アカウントネーム")
    end

    it 'delegates staff_staff_number to arrangement' do
      expect(arrange_billing.staff_staff_number).to eq("123456")
    end
  end

  describe 'Alias methods' do
    let(:staff) { FactoryBot.build(:staff, staff_number: "123456") }
    let(:arrangement) { FactoryBot.build(:arrangement, staff: staff) }
    let(:arrange_billing) { FactoryBot.build(:arrange_billing, arrangement: arrangement) }

    it 'defines staff_number as staff_staff_number' do
      expect(arrange_billing.staff_number).to eq("123456")
    end
  end

  describe 'Instance methods' do
    let(:arrange_billing) do
      FactoryBot.build(:arrange_billing,
        billing_started_at: Time.zone.parse('2025-01-01 08:00:00'),
        billing_ended_at: Time.zone.parse('2025-01-01 12:00:00')
      )
    end

    describe '#billing_started_date' do
      let(:arrangement) { FactoryBot.build(:arrangement, working_started_at: Time.zone.parse('2025-01-02 08:00:00')) }

      it 'returns formatted billing_started_at' do
        expected = I18n.l(Time.zone.parse('2025-01-01 08:00:00'), format: Settings.date.day_and_date)

        expect(arrange_billing.billing_started_date).to eq(expected)
      end

      it 'returns formatted working_started_at when billing_started_at is nil' do
        arrange_billing = FactoryBot.build(:arrange_billing, arrangement: arrangement, billing_started_at: nil)
        expected = I18n.l(Time.zone.parse('2025-01-02 08:00:00'), format: Settings.date.day_and_date)

        expect(arrange_billing.billing_started_date).to eq(expected)
      end
    end

    describe '#working_time' do
      it { expect(described_class.new.working_time).to be_nil }

      it 'returns formatted working time' do
        expect(arrange_billing.working_time).to eq('08:00~12:00')
      end

      it 'returns formatted working time with next day label' do
        arrange_billing = FactoryBot.build(:arrange_billing,
          billing_started_at: Time.zone.parse('2025-01-01 22:00:00'),
          billing_ended_at: Time.zone.parse('2025-01-02 03:00:00')
        )

        next_day = I18n.t("staff.order_cases.next_day")

        expect(arrange_billing.working_time).to eq("22:00~#{next_day}03:00")
      end
    end

    describe '#actual_working_time' do
      it { expect(described_class.new.actual_working_time).to be_zero }

      it 'returns actual minutes working time' do
        expect(arrange_billing.actual_working_time).to eq(240)
      end

      it 'returns actual minutes working time with break time' do
        arrange_billing.billing_basic_break_time = 30

        expect(arrange_billing.actual_working_time).to eq(210)
      end
    end

    describe '#rest1_time' do
      let(:arrange_billing) do
        FactoryBot.build(:arrange_billing,
          billing_rest1_started_at: Time.zone.parse('2025-01-01 00:00:00'),
          billing_rest1_ended_at: Time.zone.parse('2025-01-01 05:00:00')
        )
      end

      it { expect(described_class.new.rest1_time).to be_nil }

      it 'returns formatted rest time' do
        expect(arrange_billing.rest1_time).to eq('00:00~05:00')
      end

      it 'returns formatted rest time with next day label' do
        arrange_billing.billing_rest1_started_at = Time.zone.parse('2024-12-31 22:00:00')
        next_day = I18n.t("staff.order_cases.next_day")

        expect(arrange_billing.rest1_time).to eq("22:00~#{next_day}05:00")
      end
    end

    describe '#rest2_time' do
      let(:arrange_billing) do
        FactoryBot.build(:arrange_billing,
          billing_rest2_started_at: Time.zone.parse('2025-01-01 00:00:00'),
          billing_rest2_ended_at: Time.zone.parse('2025-01-01 05:00:00')
        )
      end

      it { expect(described_class.new.rest2_time).to be_nil }

      it 'returns formatted rest time' do
        expect(arrange_billing.rest2_time).to eq('00:00~05:00')
      end

      it 'returns formatted rest time with next day label' do
        arrange_billing.billing_rest2_started_at = Time.zone.parse('2024-12-31 22:00:00')
        next_day = I18n.t("staff.order_cases.next_day")

        expect(arrange_billing.rest2_time).to eq("22:00~#{next_day}05:00")
      end
    end

    describe '#rest3_time' do
      let(:arrange_billing) do
        FactoryBot.build(:arrange_billing,
          billing_rest3_started_at: Time.zone.parse('2025-01-01 00:00:00'),
          billing_rest3_ended_at: Time.zone.parse('2025-01-01 05:00:00')
        )
      end

      it { expect(described_class.new.rest3_time).to be_nil }

      it 'returns formatted rest time' do
        expect(arrange_billing.rest3_time).to eq('00:00~05:00')
      end

      it 'returns formatted rest time with next day label' do
        arrange_billing.billing_rest3_started_at = Time.zone.parse('2024-12-31 22:00:00')
        next_day = I18n.t("staff.order_cases.next_day")

        expect(arrange_billing.rest3_time).to eq("22:00~#{next_day}05:00")
      end
    end

    describe '#is_locked?' do
      it { expect(described_class.new.is_locked?).to be_falsey }

      it 'returns true when arrangement is locked' do
        arrange_billing.arrangement = FactoryBot.build(:arrangement, is_billing_locked: true)

        expect(arrange_billing.is_locked?).to be_truthy
      end
    end

    describe '#get_billing_night_unit_price' do
      let(:arrange_billing) do
        FactoryBot.build(:arrange_billing,
          billing_basic_unit_price: 300,
          billing_night_unit_price: 200
        )
      end

      it 'returns difference between billing_night_unit_price and billing_basic_unit_price' do
        arrange_billing.billing_basic_unit_price = 100
        arrange_billing.billing_night_unit_price = 500

        expect(arrange_billing.get_billing_night_unit_price).to eq(400)
      end


      it 'returns billing_night_unit_price when it is less than or equal to billing_basic_unit_price' do
        expect(arrange_billing.get_billing_night_unit_price).to eq(200)
      end
    end
  end

  describe 'Class methods' do
    it 'returns adjusment type options' do
      expect(described_class.adjusment_type_options).to eq([
        { id: 1, key: "normal", i18n_label: I18n.t("enum_label.arrange_billing.adjusment_type_ids.normal") },
        { id: 2, key: "adjust", i18n_label: I18n.t("enum_label.arrange_billing.adjusment_type_ids.adjust") }
      ])
    end
  end

  describe 'Acts as paranoid' do
    let!(:arrange_billing) { FactoryBot.create(:arrange_billing) }

    it 'destroys record' do
      arrange_billing.destroy
      expect(described_class.find_by(id: arrange_billing.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: arrange_billing.id)).to eq(arrange_billing)
    end

    it 'restores record' do
      arrange_billing.destroy
      arrange_billing.restore
      expect(described_class.find_by(id: arrange_billing.id)).to eq(arrange_billing)
    end
  end
end
