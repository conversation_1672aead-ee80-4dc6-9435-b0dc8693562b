require 'rails_helper'

RSpec.describe StaffEvaluation, type: :model do
  let(:staff_evaluation) { create(:staff_evaluation) }

  describe 'Associations' do
    it { should belong_to(:staff) }
    it { should belong_to(:arrangement) }
    it { should belong_to(:user) }
  end

  describe 'Validations' do
    it { should validate_presence_of(:evaluation1) }
    it { should validate_presence_of(:evaluation2) }
    it { should validate_presence_of(:evaluation3) }

    it { should validate_numericality_of(:evaluation1).is_less_than_or_equal_to(StaffEvaluation::EVALUATION_RANGE.max) }
    it { should validate_numericality_of(:evaluation1).is_greater_than_or_equal_to(StaffEvaluation::EVALUATION_RANGE.min) }
    it { should validate_numericality_of(:evaluation1).only_integer }

    it { should validate_numericality_of(:evaluation2).is_less_than_or_equal_to(StaffEvaluation::EVALUATION_RANGE.max) }
    it { should validate_numericality_of(:evaluation2).is_greater_than_or_equal_to(StaffEvaluation::EVALUATION_RANGE.min) }
    it { should validate_numericality_of(:evaluation2).only_integer }

    it { should validate_numericality_of(:evaluation3).is_less_than_or_equal_to(StaffEvaluation::EVALUATION_RANGE.max) }
    it { should validate_numericality_of(:evaluation3).is_greater_than_or_equal_to(StaffEvaluation::EVALUATION_RANGE.min) }
    it { should validate_numericality_of(:evaluation3).only_integer }

    context 'when evaluation1 is in REQUIRE_EVALUATION' do
      subject(:staff_evaluation) { build(:staff_evaluation, evaluation1: 1, evaluation1_comment: nil) }

      it { should validate_presence_of(:evaluation1_comment) }
    end

    context 'when evaluation2 is in REQUIRE_EVALUATION' do
      subject(:staff_evaluation) { build(:staff_evaluation, evaluation2: 5, evaluation2_comment: nil) }

      it { should validate_presence_of(:evaluation2_comment) }
    end

    context 'when evaluation3 is in REQUIRE_EVALUATION' do
      subject(:staff_evaluation) { build(:staff_evaluation, evaluation3: 1, evaluation3_comment: nil) }

      it { should validate_presence_of(:evaluation3_comment) }
    end

    it { should validate_length_of(:evaluation1_comment).is_at_most(Settings.maxlength.work_achievement.comment) }
    it { should validate_length_of(:evaluation2_comment).is_at_most(Settings.maxlength.work_achievement.comment) }
    it { should validate_length_of(:evaluation3_comment).is_at_most(Settings.maxlength.work_achievement.comment) }
  end

  describe 'Enums' do
    it 'has REVIEW_INDEX constant' do
      expect(StaffEvaluation::REVIEW_INDEX).to eq(1..3)
    end

    it 'has EVALUATION_RANGE constant' do
      expect(StaffEvaluation::EVALUATION_RANGE).to eq(1..5)
    end

    it 'has REQUIRE_EVALUATION constant' do
      expect(StaffEvaluation::REQUIRE_EVALUATION).to eq([1, 5])
    end

    it 'has EVALUATION_ATTRS constant' do
      expect(StaffEvaluation::EVALUATION_ATTRS).to eq([:id, :evaluation1, :evaluation2, :evaluation3, :evaluation1_comment, :evaluation2_comment, :evaluation3_comment])
    end
  end

  describe 'Instance methods' do
    describe '#evaluation_title' do
      it 'returns the correct title for evaluation1' do
        expect(staff_evaluation.evaluation_title(1)).to eq(I18n.t("activerecord.attributes.staff_evaluation.evaluation1_title"))
      end

      it 'returns the correct title for evaluation2' do
        expect(staff_evaluation.evaluation_title(2)).to eq(I18n.t("activerecord.attributes.staff_evaluation.evaluation2_title"))
      end

      it 'returns the correct title for evaluation3' do
        expect(staff_evaluation.evaluation_title(3)).to eq(I18n.t("activerecord.attributes.staff_evaluation.evaluation3_title"))
      end

      it 'returns nil for invalid review_index' do
        expect(staff_evaluation.evaluation_title(4)).to be_nil
      end
    end

    describe '#evaluation' do
      it 'returns the correct evaluation for evaluation1' do
        expect(staff_evaluation.evaluation(1)).to eq(staff_evaluation.evaluation1)
      end

      it 'returns the correct evaluation for evaluation2' do
        expect(staff_evaluation.evaluation(2)).to eq(staff_evaluation.evaluation2)
      end

      it 'returns the correct evaluation for evaluation3' do
        expect(staff_evaluation.evaluation(3)).to eq(staff_evaluation.evaluation3)
      end

      it 'returns nil for invalid review_index' do
        expect(staff_evaluation.evaluation(4)).to be_nil
      end
    end

    describe '#evaluation_comment' do
      it 'returns the correct comment for evaluation1' do
        expect(staff_evaluation.evaluation_comment(1)).to eq(staff_evaluation.evaluation1_comment)
      end

      it 'returns the correct comment for evaluation2' do
        expect(staff_evaluation.evaluation_comment(2)).to eq(staff_evaluation.evaluation2_comment)
      end

      it 'returns the correct comment for evaluation3' do
        expect(staff_evaluation.evaluation_comment(3)).to eq(staff_evaluation.evaluation3_comment)
      end

      it 'returns nil for invalid review_index' do
        expect(staff_evaluation.evaluation_comment(4)).to be_nil
      end
    end

    describe '#check_evaluation' do
      it 'returns true if any evaluation is greater than or equal to Settings.staff.evaluations[3]' do
        staff_evaluation.evaluation1 = Settings.staff.evaluations[3]
        expect(staff_evaluation.check_evaluation).to be true
      end

      it 'returns false if all evaluations are less than Settings.staff.evaluations[3]' do
        staff_evaluation.evaluation1 = Settings.staff.evaluations[2]
        staff_evaluation.evaluation2 = Settings.staff.evaluations[2]
        staff_evaluation.evaluation3 = Settings.staff.evaluations[2]
        expect(staff_evaluation.check_evaluation).to be false
      end
    end

    describe '#avg_evaluation' do
      it 'returns the correct average evaluation' do
        staff_evaluation.evaluation1 = 3
        staff_evaluation.evaluation2 = 4
        staff_evaluation.evaluation3 = 5
        expect(staff_evaluation.avg_evaluation).to eq(4.0)
      end
    end
  end

  describe 'Scopes' do
    describe '.by_arrangement_id' do
      let!(:arrangement1) { create(:arrangement) }
      let!(:arrangement2) { create(:arrangement) }
      let!(:staff_evaluation1) { create(:staff_evaluation, arrangement: arrangement1) }
      let!(:staff_evaluation2) { create(:staff_evaluation, arrangement: arrangement2) }

      it 'returns staff evaluations for the given arrangement ids' do
        expect(StaffEvaluation.by_arrangement_id([arrangement1.id])).to include(staff_evaluation1)
        expect(StaffEvaluation.by_arrangement_id([arrangement1.id])).not_to include(staff_evaluation2)
      end
    end
  end

  # Test acts as paranoid
  describe 'Acts as paranoid' do
    it 'soft deletes the record' do
      staff_evaluation.destroy
      expect(StaffEvaluation.find_by(id: staff_evaluation.id)).to be_nil
      expect(StaffEvaluation.with_deleted.find_by(id: staff_evaluation.id)).to eq(staff_evaluation)
    end

    it 'restores the record' do
      staff_evaluation.destroy
      staff_evaluation.restore
      expect(StaffEvaluation.find_by(id: staff_evaluation.id)).to eq(staff_evaluation)
    end
  end
end
