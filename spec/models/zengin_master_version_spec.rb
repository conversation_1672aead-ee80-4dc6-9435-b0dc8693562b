require 'rails_helper'

RSpec.describe ZenginMasterVersion, type: :model do
  describe 'Validators' do
    describe 'presence' do
      describe 'version, applied_at, import_type' do
        it{should validate_presence_of(:version)}

        it{should validate_presence_of(:applied_at)}

        it{should validate_presence_of(:import_type)}
      end
    end
  end

  describe 'Enums' do
    it {should define_enum_for(:import_type).with_values(diff: 1, full: 2)}
  end

  describe 'self.last_applied_version' do
    before(:example) do
      described_class.all.delete_all
    end

    it 'returns last applied version' do
      create(:zengin_master_version)
      expect(described_class.last_applied_version).to eq('20241228T21b0964a')
    end

    it 'returns nil if there is no last applied version' do
      expect(described_class.last_applied_version).to eq(nil)
    end
  end

  describe 'self.last_version_is_behind?' do
    context 'when there is no last version' do
      before(:context) do
        described_class.all.delete_all
      end

      it 'returns true' do
        expect(described_class.last_version_is_behind?('20250101T22b1167e')).to be true
      end
    end

    context 'when there is a last_version' do
      before(:context) do
        described_class.all.delete_all
        create(:zengin_master_version)
      end

      it 'returns true when version is behind' do
        expect(described_class.last_version_is_behind?('20250101T22b1167e')).to be true
      end

      it 'returns true when hash part is different' do
        expect(described_class.last_version_is_behind?('20241228T00e7723f')).to be true
      end

      it 'returns false when version is the same' do
        expect(described_class.last_version_is_behind?('20241228T21b0964a')).to be false
      end

      it 'returns false when version is ahead' do
        expect(described_class.last_version_is_behind?('20240101T47d2917c')).to be false
      end
    end
  end
end
