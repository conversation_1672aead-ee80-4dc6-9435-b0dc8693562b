require 'rails_helper'

RSpec.describe BillingPaymentTemplate, type: :model do
  subject { build(:billing_payment_template) }

  describe 'Constants' do
    it { expect(described_class).to be_const_defined(:NUMBER_FIELDS) }
    it { expect(described_class).to be_const_defined(:TEMPLATE_ATTRS) }
    it { expect(described_class).to be_const_defined(:TEMPLATE_METHODS) }
    it { expect(described_class).to be_const_defined(:CREATE_TEMPLATE_ATTRS) }
    it { expect(described_class).to be_const_defined(:UPDATE_TEMPLATE_ATTRS) }
    it { expect(described_class).to be_const_defined(:BILLING_FIELDS) }
    it { expect(described_class).to be_const_defined(:PAYMENT_FIELDS) }
  end

  describe 'Associations' do
    it{ should belong_to(:location) }
    it{ should belong_to(:admin).class_name("Admin").with_foreign_key(:creator_id).optional }
  end

  describe 'Delegations' do
    it { should delegate_method(:name).to(:location).with_prefix }
  end

  describe 'Validators' do
    describe 'presence' do
      it { should validate_presence_of(:location_id) }
    end

    describe 'numericality' do
      it do
        should validate_numericality_of(:billing_basic_unit_price).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:billing_night_unit_price).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:billing_basic_unit_price).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:area_allowance).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:short_allowance).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:absence_discount).only_integer.allow_nil
      end

      it do
        should validate_numericality_of(:tax_exemption).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:payment_basic_unit_price).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:payment_night_unit_price).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end

      it do
        should validate_numericality_of(:transportation_fee).only_integer.is_greater_than_or_equal_to(0).allow_nil
      end
    end

    describe 'invalid number' do
      let(:template) { build(:billing_payment_template) }

      shared_examples 'when input is not_a_number' do |field|
        it 'returns not_a_number error' do
          template.assign_attributes(field => 'abcde')
          template.validate
          expect(template.errors.added?(field, :not_a_number))
        end
      end

      shared_examples 'when input is not_an_integer' do |field|
        it 'returns not_an_integer error' do
          template.assign_attributes(field => 1.1)
          template.validate
          expect(template.errors.added?(field, :not_an_integer))
        end
      end

      shared_examples 'when input is not greater_than_or_equal_to 0' do |field|
        it 'returns not_an_integer error' do
          template.assign_attributes(field => 1.1)
          template.validate
          expect(template.errors.added?(field, :greater_than_or_equal_to))
        end
      end

      described_class::NUMBER_FIELDS.each do |field|
        it_behaves_like 'when input is not_a_number', field
      end

      described_class::NUMBER_FIELDS.each do |field|
        it_behaves_like 'when input is not_an_integer', field
      end

      described_class::NUMBER_FIELDS.excluding(:absence_discount).each do |field|
        it_behaves_like 'when input is not_an_integer', field
      end
    end
  end

  describe 'Callbacks' do
    describe 'before_create' do
      let(:template) { build(:billing_payment_template) }

      it 'does nothing when absence_discount is blank' do
        template.absence_discount = nil
        expect{template.save!}.not_to change(template, :absence_discount)
      end

      it 'does nothing when absence_discount is already negative' do
        template.absence_discount = -500
        expect{template.save!}.not_to change(template, :absence_discount)
      end

      it 'sets absence_discount to negative' do
        template.absence_discount = 500
        expect{template.save!}.to change(template, :absence_discount).from(500).to(-500)
      end
    end
  end

  describe 'Scopes' do
    describe '.by_location_id' do
      it 'returns all when location_id is blank' do
        create_list(:billing_payment_template, 3)
        expect(described_class.by_location_id([])).to eq(described_class.all)
      end

      it 'filters template by location_id' do
        location_1 = create(:location)
        location_2 = create(:location)
        template_1 = create(:billing_payment_template, location: location_1)
        template_2 = create(:billing_payment_template, location: location_2)
        templates = described_class.by_location_id(location_1.id)

        expect(templates).to contain_exactly(template_1)
        expect(templates).not_to include(template_2)
      end
    end

    describe '.by_corporation_id' do
      it 'returns all when corporation_id is blank' do
        create_list(:billing_payment_template, 3)
        expect(described_class.by_corporation_id([])).to eq(described_class.all)
      end

      it 'filters templates by corporation_id' do
        corporation = create(:corporation)
        corporation_group = create(:corporation_group, corporation: corporation)
        location_1 = create(:location, corporation_group: corporation_group)
        location_2 = create(:location)
        template_1 = create(:billing_payment_template, location: location_1)
        template_2 = create(:billing_payment_template, location: location_2)
        templates = described_class.by_corporation_id(corporation.id)

        expect(templates).to contain_exactly(template_1)
        expect(templates).not_to include(template_2)
      end
    end

    describe '.by_name' do
      it 'returns all when name is blank' do
        create_list(:billing_payment_template, 3)
        expect(described_class.by_name([])).to eq(described_class.all)
      end

      it 'filters templates by exact name' do
        template_1 = create(:billing_payment_template, name: 'Template Name A')
        template_2 = create(:billing_payment_template, name: 'Template Name B')
        templates = described_class.by_name('Template Name A')

        expect(templates).to contain_exactly(template_1)
        expect(templates).not_to include(template_2)
      end

      it 'filters templates by partial name' do
        template_1 = create(:billing_payment_template, name: 'Template Name A')
        template_2 = create(:billing_payment_template, name: 'Template Name B')
        template_3 = create(:billing_payment_template, name: 'Another Template C')
        templates = described_class.by_name('Template Name')

        expect(templates).to contain_exactly(template_1, template_2)
        expect(templates).not_to include(template_3)
      end
    end
  end

  describe 'Alias' do
    let(:template) { build(:billing_payment_template) }
    let(:rand_value) { rand(9999) }

    it 'aliases payment_field_1 to transportation_fee' do
      template.payment_field_1 = rand_value
      expect(template.transportation_fee).to eq(rand_value)
    end

    it 'aliases billing_field_1 to area_allowance' do
      template.billing_field_1 = rand_value
      expect(template.area_allowance).to eq(rand_value)
    end

    it 'aliases billing_field_2 to short_allowance' do
      template.billing_field_2 = rand_value
      expect(template.short_allowance).to eq(rand_value)
    end

    it 'aliases billing_field_4 to absence_discount' do
      template.billing_field_4 = rand_value
      expect(template.absence_discount).to eq(rand_value)
    end

    it 'aliases billing_tax_exemption to tax_exemption' do
      template.billing_tax_exemption = rand_value
      expect(template.tax_exemption).to eq(rand_value)
    end
  end

  describe 'Instance methods' do
    describe '#creator_name' do
      it 'returns nil when admin is nil' do
        template = build(:billing_payment_template, admin: nil)
        expect(template.creator_name).to be_nil
      end

      it 'returns nil when admin.account is nil' do
        admin = build(:admin, account: nil)
        template = build(:billing_payment_template, admin: admin)
        expect(template.creator_name).to be_nil
      end

      it 'returns creator name when admin.account exists' do
        admin = build(:admin)
        template = build(:billing_payment_template, admin: admin)
        expect(template.creator_name).to eq(admin.account.name)
      end
    end
  end

  describe 'Soft delete' do
    it 'removes record from default scope' do
      template = create(:billing_payment_template)
      template.destroy
      expect(described_class.find_by(id: template.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: template.id)).to eq(template)
    end
  end
end
