require 'rails_helper'
require 'timecop'

RSpec.describe ApplyOrderCaseWarning, type: :model do
  let(:order) do
    instance_double(
      'Order', id: 1,
      corporation_id: 1,
      corporation_group_id: 1
    )
  end

  let(:order_case) do
    instance_double(
      'OrderCase', id: 1,
      order: order,
      corporation_id: 1,
      location_id: 1,
      regular_order?: false,
      segment_trainning: false,
      case_started_at: ServerTime.now + 3.days,
      case_ended_at: ServerTime.now + 3.days + 4.hours
    )
  end

  let(:staff) do
    instance_double('Staff', id: 1, haken?: true)
  end

  let(:condition) do
    described_class.new(staff, order_case)
  end

  describe 'out_of_contract' do
    context 'when other conditions not met' do
      it 'skips validation when staff is not type haken' do
        allow(staff).to receive(:haken?).and_return(false)
        expect(condition).to be_valid
        expect(condition.errors).to be_empty
      end

      it 'skips validation when order_case is segment_training' do
        allow(order_case).to receive(:segment_trainning).and_return(true)
        expect(condition).to be_valid
        expect(condition.errors).to be_empty
      end

      it 'skips validation when working_date is undetermined (regular)' do
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(nil)
        expect(condition).to be_valid
        expect(condition.errors).to be_empty
      end
    end

    context 'when other conditions met' do
      before(:context) do
        Timecop.freeze
      end

      it 'adds warning when staff has no contract during working_date, but has current contract' do
        allow(staff).to receive(:contract_history_by_date).with(order_case.case_started_at.to_date).and_return(nil)
        allow(staff).to receive(:contract_history_by_date).with(ServerTime.now.to_date).and_return(double)

        expect(condition).to be_invalid
        expect(condition.errors.added?(:contract_warning, :out_of_contract)).to be true
      end

      it 'adds warning when staff has no contract during working_date, but has current contract (regular)' do
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(order_case.case_started_at)
        allow(staff).to receive(:contract_history_by_date).with(order_case.case_started_at.to_date).and_return(nil)
        allow(staff).to receive(:contract_history_by_date).with(ServerTime.now.to_date).and_return(double)

        expect(condition).to be_invalid
        expect(condition.errors.added?(:contract_warning, :out_of_contract)).to be true
      end

      it 'does not add warning when staff has contract on working_date' do
        allow(staff).to receive(:contract_history_by_date).with(order_case.case_started_at.to_date).and_return(double)

        expect(condition).to be_valid
        expect(condition.errors).to be_empty
      end

      it 'does not add warning when staff has no contract on working_date or right now' do
        allow(staff).to receive(:contract_history_by_date).with(order_case.case_started_at.to_date).and_return(nil)
        allow(staff).to receive(:contract_history_by_date).with(ServerTime.now.to_date).and_return(nil)

        expect(condition).to be_valid
        expect(condition.errors).to be_empty
      end

      after(:context) do
        Timecop.return
      end
    end
  end
end
