require 'rails_helper'

RSpec.describe SurveyAnswer, type: :model do
  describe 'Constants' do
    it 'has a MAX_LENGTH constant' do
      expect(described_class::MAX_LENGTH).to eq(255)
    end
  end

  describe 'Associations' do
    it { should belong_to(:survey_question) }
  end

  describe 'Validations' do
    it { should validate_presence_of(:content) }
    it { should validate_length_of(:content).is_at_most(SurveyAnswer::MAX_LENGTH) }
  end

  describe 'Enums' do
    it { should define_enum_for(:answer_type).with_values(select_box: 0, text: 1) }
    it { should define_enum_for(:to_audience).with_values(to_all: 0, to_staff: 1, to_admin: 2) }
  end

  describe 'Scopes' do
    describe '.default_admin_absent_without_notice' do
      let!(:survey_question) { create(:survey_question, survey_question_type: :staff_cancel_training) }
      let!(:survey_answer) { create(:survey_answer, survey_question: survey_question, to_audience: :to_admin) }

      it 'returns the correct survey answer' do
        expect(described_class.default_admin_absent_without_notice).to eq(survey_answer)
      end
    end
  end

  describe 'Default Scope' do
    let!(:survey_answer1) { create(:survey_answer, answer_type: :text, order: 2) }
    let!(:survey_answer2) { create(:survey_answer, answer_type: :select_box, order: 1) }

    it 'orders survey answers by answer_type and order' do
      expect(described_class.all).to eq([survey_answer2, survey_answer1])
    end
  end
end
