require 'rails_helper'

RSpec.describe AdminArrangeBillingSearchCondition, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:admin_id) }
    it { should validate_presence_of(:conditions) }
  end

  describe 'Associations' do
    it { should belong_to(:admin) }
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      cond = create(:admin_arrange_billing_search_condition)
      cond.destroy
      expect(described_class.find_by(id: cond.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: cond.id)).to eq(cond)
    end
  end

  describe 'Instance methods' do
    describe '#corporation_id' do
      let(:admin_arrange_billing_search_condition) do
        create(:admin_arrange_billing_search_condition, conditions: '{"corporation_id": 123}')
      end

      it 'returns the corporation_id from the conditions JSON' do
        expect(admin_arrange_billing_search_condition.corporation_id).to eq(123)
      end

      it 'returns nil if corporation_id is not present in the conditions JSON' do
        admin_arrange_billing_search_condition.conditions = '{"foo": "bar"}'
        expect(admin_arrange_billing_search_condition.corporation_id).to be_nil
      end

      it 'returns nil if conditions is blank' do
        admin_arrange_billing_search_condition.conditions = ''
        expect(admin_arrange_billing_search_condition.corporation_id).to be_nil
      end

      it 'returns nil if conditions is not a valid JSON' do
        admin_arrange_billing_search_condition.conditions = 'invalid json'
        expect(admin_arrange_billing_search_condition.corporation_id).to be_nil
      end
    end
  end
end
