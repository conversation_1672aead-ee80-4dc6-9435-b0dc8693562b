require "rails_helper"

RSpec.describe Account, type: :model do
  subject {FactoryBot.create(:account, :with_tel)}

  describe 'WardenFunction' do
    it 'responds to methods from WardenFunction' do
      expect(described_class).to respond_to(:wardenable)
    end
  end

  describe 'Constants' do
    it { expect(described_class).to be_const_defined(:ACCOUNT_ATTRIBUTES) }
    it { expect(described_class).to be_const_defined(:REGISTRATION_ACCOUNT_ATTRIBUTES) }
    it { expect(described_class).to be_const_defined(:SQUISH_ATTRS) }
    it { expect(described_class).to be_const_defined(:CHANGE_PASSWORD_PARAMS) }
    it { expect(described_class).to be_const_defined(:REGISTER_PARAMS) }
  end

  describe "enums" do
    it { should define_enum_for(:ses_status).with_values(receiver: 0, bounce: 1, complaint: 2) }
  end

  describe "validations" do
    describe "presence" do
      describe "email" do
        before{allow(subject).to receive(:email_present?).and_return(true)}
        it{should validate_presence_of(:email)}
      end

      describe "tel" do
        before{allow(subject).to receive(:validate_tel_entry).and_return(true)}
        it{should validate_presence_of(:tel)}
      end

      describe "name, name_kana" do
        before{allow(subject).to receive(:execute_account_name_validation).and_return(true)}
        it{should validate_presence_of(:name)}
        it{should validate_presence_of(:name_kana)}
      end

      describe "password" do
        before{allow(subject).to receive(:password_required?).and_return(true)}
        it{should validate_presence_of(:password)}
      end
    end

    describe "uniqueness" do
      it { should validate_uniqueness_of(:email).case_insensitive.allow_nil }
    end

    it "validates format_rule" do
      account = FactoryBot.build(:account, tel: "not_a_number")
      expect(account).not_to be_valid
      expect(account.errors[:tel]).to be_present
    end

    it "validates password same as email" do
      account = FactoryBot.build(:account)
      email_part = account.email.split("@").first
      account.password = "#{email_part}1234"
      account.valid?
      expect(account.errors[:password]).to include(I18n.t("activerecord.errors.models.account.attributes.password.same_as_email"))
    end
  end

  describe "associations" do
    it{should have_one(:admin)}
    it{should have_one(:user)}
    it{should have_one(:staff)}
  end

  describe "callbacks" do
    it "removes extra whitespaces from name, name_kana, email" do
      account = FactoryBot.build(:account, name: "   Ponos   ", name_kana: "   タロウ   ", email: "   <EMAIL>   ")
      account.valid?
      expect(account.name).to eq("Ponos")
      expect(account.name_kana).to eq("タロウ")
      expect(account.email).to eq("<EMAIL>")
    end

    it "encrypts password" do
      pwd = "password@1234"
      account = FactoryBot.create(:account, password: pwd)
      expect(account.encrypted_password).not_to be_nil
      expect(account.encrypted_password).not_to eq(pwd)
    end

    it "converts name_kana to half sizes before validation" do
      account = FactoryBot.build(:account, name_kana: "タロウ")
      account.is_validate_name_kana = true
      account.valid?
      expect(account.name_kana).to eq(Unicode::Japanese.z2h("タロウ"))
    end
  end

  describe "scopes" do
    describe "by_location_code" do
      it "returns accounts with matching location_code" do
        matching_account = FactoryBot.create(:account, location_code: "123456")
        non_matching_account = FactoryBot.create(:account, location_code: "987654")
        accs_by_location_code = Account.by_location_code("123456")
        expect(accs_by_location_code).to include(matching_account)
        expect(accs_by_location_code).not_to include(non_matching_account)
      end
    end

    describe "is_valid" do
      it "returns account with valid date range" do
        now = Time.current
        valid = create(:account, valid_start_date: now - 1.day, valid_end_date: now + 1.day)
        forever_valid = create(:account, valid_end_date: nil)
        invalid = create(:account, valid_end_date: now - 1.day)
        valid_accs = Account.is_valid
        expect(valid_accs).to include(valid, forever_valid)
        expect(valid_accs).not_to include(invalid)
      end
    end
  end

  describe "class methods" do
    describe "by_login_id" do
      it "finds account by tel if login_id is a phone number" do
        account = create(:account, :with_tel)
        tel = account.tel
        expect(Account.by_login_id(tel)).to eq(account)
      end

      it "finds account by email if login_id is an email" do
        account = create(:account)
        email = account.email
        expect(Account.by_login_id(email)).to eq(account)
      end
    end

    describe "email_existed?" do
      it "returns true if email exists and account not in except_ids" do
        account = create(:account)
        email = account.email
        expect(Account.email_existed?(email)).to be true
        expect(Account.email_existed?(email, [account.id])).to be false
      end
    end

    describe "tel_existed?" do
      it "returns true if tel exists and account not in except_ids" do
        account = create(:account, :with_tel)
        tel = account.tel
        expect(Account.tel_existed?(tel)).to be true
        expect(Account.tel_existed?(tel, [account.id])).to be false
      end
    end

    describe "ransackable_attributes" do
      it "returns ransackable attributes" do
        expect(Account.ransackable_attributes).to contain_exactly("name", "name_kana")
      end
    end
  end

  describe "name_kana_to_half_size" do
    it "converts name_kana to half-size katakana" do
      account = FactoryBot.build(:account, name_kana: "タロウ")
      half_size_name = account.name_kana_to_half_size
      expect(half_size_name).to eq(Unicode::Japanese.z2h("タロウ"))
    end
  end

  describe "is_store_computer_account?" do
    it "returns true if migration_location_id is present" do
      expect(build(:account, migration_location_id: 1).is_store_computer_account?).to be true
    end

    it "returns true if location_code is present and email is blank" do
      expect(build(:account, location_code: "123456", email: "").is_store_computer_account?).to be true
    end

    it "returns true if both location_code and email are present" do
      expect(build(:account, location_code: "123456").is_store_computer_account?).to be false
    end

    it "returns false otherwise" do
      expect(build(:account, location_code: nil, migration_location_id: nil).is_store_computer_account?).to be false
    end
  end

  describe "validate_email" do
    it "adds blank error if email is blank" do
      subject.validate_email(["", nil].sample)
      expect(subject.errors.details[:email]).to include(a_hash_including(error: :blank))
    end

    it "adds taken error if email exists" do
      old_account = FactoryBot.create(:account)
      new_account = FactoryBot.create(:account)
      new_account.validate_email(old_account.email)
      expect(new_account.errors.details[:email]).to include(a_hash_including(error: :taken))
    end
  end

  describe "validate_tel" do
    it "adds blank error if tel is blank" do
      account = FactoryBot.create(:account, :with_tel)
      account.validate_tel(["", nil].sample)
      expect(account.errors.details[:tel]).to include(a_hash_including(error: :blank))
    end

    it "adds taken error if tel exists" do
      old_account = FactoryBot.create(:account, :with_tel)
      new_account = FactoryBot.create(:account)
      new_account.validate_tel(old_account.tel)
      expect(new_account.errors.details[:tel]).to include(a_hash_including(error: :taken))
    end

    it "sets skip_email_presence flag" do
        account = FactoryBot.build(:account, :with_tel)
        account.validate_tel("***********")
        expect(account.skip_email_presence).to be true
      end
  end

  describe "validate_password" do
    let(:account) {FactoryBot.create(:account, password: "Ponos@1234")}

    it "adds error if password is blank" do
      account.validate_password(["", nil].sample)
      expect(account.errors.details[:password]).to include(a_hash_including(error: :blank))
    end

    it "sets skip_password_validation if password is correct" do
      account.validate_password("Ponos@1234")
      expect(account.skip_password_validation).to be true
    end

    it "adds error if password is incorrect" do
      account.validate_password("wrongpassword")
      expect(account.errors.details[:password]).to include(a_hash_including(error: :incorrect))
    end
  end
end
