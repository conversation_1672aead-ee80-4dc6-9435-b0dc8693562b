require 'rails_helper'

RSpec.describe AdminArrangeSearchCondition, type: :model do
  describe 'Validations' do
    it { is_expected.to validate_presence_of(:admin_id) }
    it { is_expected.to validate_presence_of(:conditions) }
  end

  describe 'Associations' do
    it { is_expected.to belong_to(:admin) }
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      cond = create(:admin_arrange_search_condition)
      cond.destroy
      expect(described_class.find_by(id: cond.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: cond.id)).to eq(cond)
    end
  end

  describe 'Instance methods' do
    describe '#staff_id' do
      let(:admin_arrange_search_condition) { create(:admin_arrange_search_condition, conditions: '{"staff_id": 123}') }

      it 'returns the staff_id from the conditions JSON' do
        expect(admin_arrange_search_condition.staff_id).to eq(123)
      end

      it 'returns the id from the conditions JSON if staff_id is not present' do
        admin_arrange_search_condition.conditions = '{"id": 123}'
        expect(admin_arrange_search_condition.staff_id).to eq(123)
      end

      it 'returns nil if staff_id and id are not present in the conditions JSON' do
        admin_arrange_search_condition.conditions = '{"foo": "bar"}'
        expect(admin_arrange_search_condition.staff_id).to be_nil
      end

      it 'returns nil if conditions is blank' do
        admin_arrange_search_condition.conditions = ''
        expect(admin_arrange_search_condition.staff_id).to be_nil
      end

      it 'returns nil if conditions is not a valid JSON' do
        admin_arrange_search_condition.conditions = 'invalid json'
        expect(admin_arrange_search_condition.staff_id).to be_nil
      end
    end
  end
end
