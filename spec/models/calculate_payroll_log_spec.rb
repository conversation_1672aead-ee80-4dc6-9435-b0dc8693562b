require 'rails_helper'

RSpec.describe CalculatePayrollLog, type: :model do
  describe 'Associations' do
    it{should belong_to(:admin)}
  end

  describe 'Serialization' do
    it 'serializes content as a Array via YAML' do
      log = create(:calculate_payroll_log, staff_ids: [1, 2, 3])
      expect(log.reload.staff_ids).to eq([1, 2, 3])
    end
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      log = create(:calculate_payroll_log)
      log.destroy
      expect(described_class.find_by(id: log.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: log.id)).to eq(log)
    end
  end
end
