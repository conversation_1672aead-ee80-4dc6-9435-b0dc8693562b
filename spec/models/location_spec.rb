require "rails_helper"

RSpec.describe Location, type: :model do
  let(:corporation_group) { create(:corporation_group) }
  let(:organization) { create(:organization) }
  let(:prefecture) { create(:prefecture) }
  let(:job_category) { create(:job_category) }
  let(:location) { build(:location, corporation_group: corporation_group, organization: organization, prefecture: prefecture, job_category: job_category) }

  describe 'Constants' do
    it 'has TRAINING_CENTERS_CACHED_KEY constant' do
      expect(described_class::TRAINING_CENTERS_CACHED_KEY).to eq("training_center_locs")
    end

    it 'has SEARCH_ADDRESS_ATTRS constant' do
      expect(described_class::SEARCH_ADDRESS_ATTRS).to eq(%w(city street_number building prefecture_name))
    end

    it 'has LIMIT_STATIONS constant' do
      expect(described_class::LIMIT_STATIONS).to eq(20)
    end

    it 'has REQUIRE_ORDER_ATTRS constant' do
      expect(described_class::REQUIRE_ORDER_ATTRS).to eq(%i(prefecture_id city street_number tel postal_code))
    end

    it 'has BOOLEAN_ATTRS constant' do
      expect(described_class::BOOLEAN_ATTRS).to eq(%i(is_store_parking_area_usable))
    end

    it 'has PIC_TYPES constant' do
      expect(described_class::PIC_TYPES).to eq(%i(haken_destination claim mandator))
    end

    it 'has STATION_NAME_FIELDS constant' do
      expect(described_class::STATION_NAME_FIELDS).to eq(%w(station_1 station_2 station_3 station_4 station_5))
    end

    it 'has LOCATION_PIC_TYPES constant' do
      expect(described_class::LOCATION_PIC_TYPES).to eq({haken_destination: 1, claim: 2, mandator: 3})
    end

    it 'has NOT_LAWSON constant' do
      expect(described_class::NOT_LAWSON).to eq("not_lawson")
    end
  end

  describe 'Acts as paranoid' do
    let(:location) { create(:location, corporation_group: corporation_group, organization: organization, prefecture: prefecture, job_category: job_category) }

    it 'soft deletes the record' do
      location.destroy
      expect(described_class.find_by(id: location.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: location.id)).to eq(location)
    end

    it 'restores the soft deleted record' do
      location.destroy
      location.restore
      expect(described_class.find_by(id: location.id)).to eq(location)
    end
  end

  describe 'Enums' do
    it do
      is_expected.to define_enum_for(:pos_type_id)
        .with_values(old_pos: 1, new_pos: 2, no_pos: 3)
    end

    it do
      is_expected.to define_enum_for(:station_1_transportation_method_id)
        .with_values(walking: 0, bus: 1, taxi: 2)
        .with_prefix(true)
    end

    it do
      is_expected.to define_enum_for(:default_invoice_target)
        .with_values(lawson_invoice: 0, separate_invoice: 1)
    end
  end
  describe "Validations" do
    describe "presence validations" do
      it { should validate_presence_of(:name) }
      it { should validate_presence_of(:creator_id) }
      it { should validate_presence_of(:updater_id) }
      it { should validate_presence_of(:organization_id) }
      it { should validate_presence_of(:corporation_group_id) }
      it { should validate_presence_of(:postal_code) }
      it { should validate_presence_of(:prefecture_id) }
      it { should validate_presence_of(:city) }
      it { should validate_presence_of(:street_number) }
      it { should validate_presence_of(:tel) }
      it { should validate_presence_of(:short_name) }
      it { should validate_presence_of(:job_category_id) }
    end

    describe 'format validations' do
      before do
        allow(Settings).to receive_message_chain(:location_type).and_return(['ローソン', 'ナチュラルローソン', 'ローソンストア100'])
      end

      it 'validates postal_code format' do
        location.postal_code = 'invalid'
        expect(location).not_to be_valid
        expect(location.errors[:postal_code]).to be_present
      end

      it 'validates email format' do
        location.email = 'invalid-email'
        expect(location).not_to be_valid
        expect(location.errors[:email]).to be_present
      end

      it 'validates tel format' do
        location.tel = 'invalid-tel'
        expect(location).not_to be_valid
        expect(location.errors[:tel]).to be_present
      end

      it 'validates fax format' do
        location.fax = 'invalid-fax'
        expect(location).not_to be_valid
        expect(location.errors[:fax]).to be_present
      end
    end

    describe 'numericality validations' do
      it 'validates station walking times are non-negative integers' do
        location.station_1_walking_time = -1
        expect(location).not_to be_valid
        expect(location.errors[:station_1_walking_time]).to be_present

        location.station_1_walking_time = 1.5
        expect(location).not_to be_valid
        expect(location.errors[:station_1_walking_time]).to be_present

        location.station_1_walking_time = 10
        location.valid?
        expect(location.errors[:station_1_walking_time]).to be_empty
      end
    end

    describe 'length validations' do
      it 'validates maximum length for outside lawson attributes' do
        long_text = 'a' * 256
        location.job_content = long_text
        expect(location).not_to be_valid
        expect(location.errors[:job_content]).to be_present

        location.job_content = 'a' * 255
        location.valid?
        expect(location.errors[:job_content]).to be_empty
      end
    end

    describe 'inclusion validations' do
      before do
        allow(Settings).to receive_message_chain(:location_type).and_return(['ローソン', 'ナチュラルローソン', 'ローソンストア100'])
      end

      it 'validates location_type inclusion' do
        location.location_type = 'invalid_type'
        expect(location).not_to be_valid
        expect(location.errors[:location_type]).to be_present

        location.location_type = 'ローソン'
        location.valid?
        expect(location.errors[:location_type]).to be_empty
      end
    end

    describe 'custom validations' do
      it 'validates name without special characters' do
        location.name = 'Valid Name'
        location.valid?
        expect(location.errors[:name]).to be_empty
      end
    end
  end

  describe 'Associations' do
    it { should belong_to(:corporation_group) }
    it { should belong_to(:organization) }
    it { should belong_to(:prefecture).optional }
    it { should belong_to(:stations_1).class_name('Station').optional }
    it { should belong_to(:stations_2).class_name('Station').optional }
    it { should belong_to(:stations_3).class_name('Station').optional }
    it { should belong_to(:stations_4).class_name('Station').optional }
    it { should belong_to(:stations_5).class_name('Station').optional }
    it { should belong_to(:job_category) }
    it { should belong_to(:department).class_name('Department').optional }

    it { should have_one(:location_survey).dependent(:destroy) }
    it { should have_one(:now_base_price).class_name('LocationPaymentRate') }
    it { should have_one(:new_base_price).class_name('LocationPaymentRate') }

    it { should have_many(:user_locations).dependent(:destroy) }
    it { should have_many(:location_job_categories).dependent(:destroy) }
    it { should have_many(:user_order_case_search_conditions).dependent(:destroy) }
    it { should have_many(:users).through(:user_locations) }
    it { should have_many(:location_pics).dependent(:destroy) }
    it { should have_many(:orders).dependent(:restrict_with_error) }
    it { should have_many(:order_cases).through(:orders) }
    it { should have_many(:staff_apply_order_cases).dependent(:destroy) }
    it { should have_many(:staff_like_locations).dependent(:destroy) }
    it { should have_many(:location_evaluation_summaries).dependent(:destroy) }
    it { should have_many(:location_evaluations).dependent(:destroy) }
    it { should have_many(:staff_complaints).dependent(:destroy) }
    it { should have_many(:staff_messages).dependent(:destroy) }
    it { should have_many(:user_arrange_billing_search_conditions).dependent(:destroy) }
    it { should have_many(:owner_notifications).dependent(:destroy) }
    it { should have_many(:last_evaluation_summaries).class_name('LocationEvaluationSummary') }
    it { should have_many(:priority_staffs).dependent(:destroy) }
    it { should have_many(:order_templates).dependent(:destroy) }
    it { should have_many(:location_payment_rates).dependent(:destroy) }
    it { should have_many(:billing_payment_templates).dependent(:destroy) }
  end

  describe 'Delegations' do
    let(:location) { create(:location, corporation_group: corporation_group, organization: organization, prefecture: prefecture, job_category: job_category) }

    it 'delegates corporation_group methods' do
      expect(location).to respond_to(:corporation_group_full_name)
      expect(location).to respond_to(:corporation_group_violation_day_str)
      expect(location).to respond_to(:corporation_group_violation_day)
      expect(location).to respond_to(:corporation_group_is_lawson)
      expect(location).to respond_to(:corporation_group_thumbnail_path)
    end

    it 'delegates organization methods' do
      expect(location).to respond_to(:organization_full_name)
    end

    it 'delegates prefecture methods' do
      expect(location).to respond_to(:prefecture_name)
    end

    it 'delegates station methods' do
      expect(location).to respond_to(:stations_1_name)
      expect(location).to respond_to(:stations_1_station_name)
      expect(location).to respond_to(:stations_2_name)
      expect(location).to respond_to(:stations_3_name)
      expect(location).to respond_to(:stations_4_name)
      expect(location).to respond_to(:stations_5_name)
    end

    it 'delegates job_category and department methods' do
      expect(location).to respond_to(:job_category_name)
      expect(location).to respond_to(:department_name)
    end

    it 'delegates location_survey methods' do
      expect(location).to respond_to(:location_survey_id)
    end
  end

  describe 'Scopes' do
    let!(:corporation_group1) { create(:corporation_group) }
    let!(:corporation_group2) { create(:corporation_group) }
    let!(:location1) { create(:location, corporation_group: corporation_group1, name: 'Test Location 1') }
    let!(:location2) { create(:location, corporation_group: corporation_group2, name: 'Test Location 2') }

    describe '.by_corporation_group_id' do
      it 'returns locations for specified corporation group' do
        result = described_class.by_corporation_group_id(corporation_group1.id)
        expect(result).to include(location1)
        expect(result).not_to include(location2)
      end
    end

    describe '.by_name' do
      it 'returns locations matching name pattern' do
        result = described_class.by_name('Test Location 1')
        expect(result).to include(location1)
        expect(result).not_to include(location2)
      end
    end

    describe '.by_code' do
      let!(:location_with_code) { create(:location, code: 'TEST001') }

      it 'returns locations with matching code' do
        result = described_class.by_code('TEST001')
        expect(result).to include(location_with_code)
        expect(result).not_to include(location1)
      end
    end

    describe '.is_active' do
      let!(:active_location) { create(:location, closed_at: nil) }
      let!(:future_closed_location) { create(:location, closed_at: 1.day.from_now) }
      let!(:closed_location) { create(:location, closed_at: 1.day.ago) }

      it 'returns active locations (not closed or closed in future)' do
        result = described_class.is_active
        expect(result).to include(active_location)
        expect(result).to include(future_closed_location)
        expect(result).not_to include(closed_location)
      end
    end

    describe '.closed' do
      let!(:active_location) { create(:location, closed_at: nil) }
      let!(:closed_location) { create(:location, closed_at: 1.day.ago) }

      it 'returns closed locations' do
        result = described_class.closed
        expect(result).to include(closed_location)
        expect(result).not_to include(active_location)
      end
    end

    describe '.training_center_locs' do
      let!(:training_center) { create(:location, is_training_center: true) }
      let!(:regular_location) { create(:location, is_training_center: false) }

      it 'returns training center locations' do
        result = described_class.training_center_locs
        expect(result).to include(training_center)
        expect(result).not_to include(regular_location)
      end
    end

    describe '.not_auto_matching' do
      let!(:auto_matching_location) { create(:location, is_auto_matching: true) }
      let!(:manual_location) { create(:location, is_auto_matching: false) }

      it 'returns locations with auto matching disabled' do
        result = described_class.not_auto_matching
        expect(result).to include(manual_location)
        expect(result).not_to include(auto_matching_location)
      end
    end
  end

  describe 'Callbacks' do
    let(:location) { build(:location, corporation_group: corporation_group, organization: organization, prefecture: prefecture, job_category: job_category) }

    describe 'before_save :set_lat_long' do
      it 'calls set_lat_long when address fields change' do
        expect(location).to receive(:set_lat_long_value).and_call_original
        location.postal_code = '123-4567'
        location.save
      end

      it 'does not call set_lat_long when address fields do not change' do
        location.save # Initial save
        expect(location).not_to receive(:set_lat_long_value)
        location.name = 'New Name'
        location.save
      end
    end

    describe 'before_save :set_outside_lawson_attr' do
      it 'clears outside lawson attributes for lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(true)
        location.job_content = 'Test content'
        location.personal_things = 'Test personal'
        location.save

        expect(location.job_content).to eq('')
        expect(location.personal_things).to eq('')
      end

      it 'preserves outside lawson attributes for non-lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(false)
        location.job_content = 'Test content'
        location.personal_things = 'Test personal'
        location.save

        expect(location.job_content).to eq('Test content')
        expect(location.personal_things).to eq('Test personal')
      end
    end

    describe 'after_create :create_user_locations_for_owner' do
      it 'creates user locations for corporation owners' do
        corporation = create(:corporation, :with_users)
        location.corporation_group = create(:corporation_group, corporation: corporation)

        expect { location.save }.to change { UserLocation.count }
      end
    end
  end

  describe 'Class Methods' do
    describe '.training_center_locs_json_arr' do
      let!(:training_center1) { create(:location, is_training_center: true, prefecture: create(:prefecture, name: 'Tokyo')) }
      let!(:training_center2) { create(:location, is_training_center: true, prefecture: create(:prefecture, name: 'Osaka')) }

      before do
        # Mock Redis
        redis_mock = double('redis')
        allow(Lawson::RedisConnector).to receive(:new).and_return(redis_mock)
        allow(redis_mock).to receive(:get).and_return(nil)
        allow(redis_mock).to receive(:set)
      end

      it 'returns JSON array of training center locations' do
        result = described_class.training_center_locs_json_arr
        expect(result).to be_a(String)
        parsed_result = JSON.parse(result)
        expect(parsed_result).to be_an(Array)
        expect(parsed_result.length).to eq(2)
      end
    end

    describe '.options_for_select_with_corporation' do
      let!(:corporation) { create(:corporation) }
      let!(:location1) { create(:location, corporation_group: create(:corporation_group, corporation: corporation)) }
      let!(:location2) { create(:location) }

      it 'returns location options for specific corporation' do
        result = described_class.options_for_select_with_corporation(corporation.id)
        expect(result).to include([location1.name, location1.id])
        expect(result).not_to include([location2.name, location2.id])
      end
    end

    describe '.options_for_select_training_schedule_form' do
      let!(:training_center) { create(:location, is_training_center: true) }
      let!(:regular_location) { create(:location, is_training_center: false) }

      it 'returns training center options' do
        result = described_class.options_for_select_training_schedule_form
        expect(result).to include([training_center.name, training_center.id])
        expect(result).not_to include([regular_location.name, regular_location.id])
      end
    end

    describe '.categories_by_locations' do
      let!(:location1) { create(:location) }
      let!(:location2) { create(:location) }

      it 'returns location data with categories' do
        result = described_class.categories_by_locations([location1.id, location2.id])
        expect(result).to be_an(Array)
        expect(result.length).to eq(2) # One entry per location
        expect(result.first).to have_key('id')
        expect(result.first).to have_key('job_category_key')
      end
    end

    describe '.ransackable_attributes' do
      it 'returns searchable attributes' do
        result = described_class.ransackable_attributes
        expect(result).to include('name', 'code', 'city', 'street_number')
      end
    end
  end

  describe 'Instance Methods' do
    let(:location) do
      create(:location,
        corporation_group: corporation_group,
        organization: organization, prefecture: prefecture, job_category: job_category)
    end

    describe '#prefecture_json_format' do
      it 'returns location data in JSON format for prefecture' do
        result = location.prefecture_json_format
        expect(result).to have_key(:id)
        expect(result).to have_key(:prefecture_name)
        expect(result).to have_key(:name)
        expect(result[:id]).to eq(location.id)
      end
    end

    describe '#basic_info' do
      it 'returns comprehensive location information' do
        result = location.basic_info
        expect(result).to have_key(:postal_code)
        expect(result).to have_key(:city)
        expect(result).to have_key(:address)
        expect(result).to have_key(:id)
        expect(result).to have_key(:name)
        expect(result).to have_key(:tel)
        expect(result).to have_key(:fax)
        expect(result).to have_key(:latitude)
        expect(result).to have_key(:longitude)
        expect(result[:id]).to eq(location.id)
      end
    end

    describe '#prefecture_city' do
      it 'returns combined prefecture and city' do
        location.prefecture = build(:prefecture, name: 'Tokyo')
        location.city = 'Shibuya'
        expect(location.prefecture_city).to eq('TokyoShibuya')
      end
    end

    describe '#station_info' do
      before do
        station_1, station_2, station_3, station_4, station_5 = FactoryBot.create_list(:station, 5)
        location.station_1 = station_1.id
        location.station_2 = station_2.id
        location.station_3 = station_3.id
        location.station_4 = station_4.id
        location.station_5 = station_5.id

        location.save!
      end

      it 'returns station information' do
        result = location.stations_info

        expect(result).to be_a(Hash)
        expect(result).to have_key(:station_1)
        expect(result).to have_key(:station_2)
        expect(result).to have_key(:station_3)
        expect(result).to have_key(:station_4)
        expect(result).to have_key(:station_5)
      end

      it 'includes transportation information if has transportation method attribute' do
        location.station_1_walking_time = 10
        location.station_1_transportation_method_id = 'walking'
        result = location.stations_info

        expect(result[:station_1]).to include("#{I18n.t("admin.location.station_transportation_methods.walking")}10")
      end
    end

    describe '#stations_info_value_only' do
      before do
        allow(location).to receive(:stations_info).and_return({station_1: 'Station 1', station_2: 'Station 2'})
      end

      it { expect(location.stations_info_value_only).to eq(['Station 1', 'Station 2']) }
    end

    describe '#full_name' do
      it 'returns the location name' do
        expect(location.full_name).to eq(location.name)
      end
    end

    describe '#corporation_name' do
      it 'returns the corporation full name' do
        corporation = build(:corporation, name_1: 'Test Corp')

        corporation_group.corporation = corporation
        allow(corporation).to receive(:full_name).and_return('Test Corp Full Name')
        expect(location.corporation_name).to eq('Test Corp Full Name')
      end

      it 'returns empty string when corporation_group is nil' do
        location.corporation_group = nil

        expect(location.corporation_name).to eq('')
      end

      it 'returns empty string when corporation is nil' do
        allow(location).to receive_message_chain(:corporation_group, :corporation).and_return(nil)

        expect(location.corporation_name).to eq('')
      end

      it 'returns empty string when corporation full name are nil' do
        allow(location).to receive_message_chain(:corporation_group, :corporation, :full_name).and_return(nil)

        expect(location.corporation_name).to eq('')
      end
    end

    describe '#evaluation' do
      it 'returns last evaluation summary' do
        last_evaluation_summary = create(:location_evaluation_summary, location: location)
        allow(location).to receive(:last_evaluation_summaries).and_return([last_evaluation_summary])

        expect(location.evaluation).to eq(last_evaluation_summary)
      end

      it 'returns nil when no evaluation summary' do
        allow(location).to receive(:last_evaluation_summaries).and_return([])

        expect(location.evaluation).to be_nil
      end
    end

    describe '#id_with_leading_zeros' do
      it 'returns ID with leading zeros' do
        location.id = 123
        expect(location.id_with_leading_zeros).to eq('0123')
      end
    end

    describe '#full_address' do
      it 'returns complete address' do
        location.prefecture = build(:prefecture, name: 'Tokyo')
        location.city = 'Shibuya'
        location.street_number = '1-1-1'
        location.building = 'Test Building'

        result = location.full_address
        expect(result).to eq('TokyoShibuya1-1-1Test Building')
      end
    end

    describe '#address' do
      it 'returns street address without prefecture and city' do
        location.street_number = '1-1-1'
        location.building = 'Test Building'

        result = location.address
        expect(result).to eq('1-1-1Test Building')
      end
    end

    describe '#get_coordinates' do
      it 'returns latitude and longitude as array' do
        location.latitude = 35.6762
        location.longitude = 139.6503

        result = location.get_coordinates
        expect(result).to eq([35.6762, 139.6503])
      end
    end

    describe '#station_1_info' do
      let(:station) { create(:station) }
      let(:location) do
        create(:location,
          station_1: station, station_1_walking_time: 10, station_1_transportation_method_id: 'walking')
      end

      before do
        allow(station).to receive(:name_with_railway_line).and_return('Tokyo Station')
      end

      it 'returns station 1 information' do
        result = location.station_1_info

        expect(result).to include('Tokyo Station')
      end
    end

    describe '#is_valid_order_info?' do
      it 'returns true when all required order attributes are present' do
        location.prefecture_id = 1
        location.city = 'Test City'
        location.street_number = '1-1-1'
        location.tel = '************'
        location.postal_code = '123-4567'
        location.is_store_parking_area_usable = true

        expect(location.is_valid_order_info?).to be true
      end

      it 'returns false when required attributes are missing' do
        location.city = nil
        expect(location.is_valid_order_info?).to be false
      end
    end

    describe '#is_active_at_moment?' do
      it 'returns true when location is not closed' do
        location.closed_at = nil
        expect(location.is_active_at_moment?(Time.current)).to be true
      end

      it 'returns true when closed_at is in the future' do
        location.closed_at = 1.day.from_now
        expect(location.is_active_at_moment?(Time.current)).to be true
      end

      it 'returns false when closed_at is in the past' do
        location.closed_at = 1.day.ago
        expect(location.is_active_at_moment?(Time.current)).to be false
      end
    end

    describe '#get_type' do
      it 'returns location type for Lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(true)
        location.name = 'ローソン Test Store'
        expect(location.get_type).to eq('ローソン')
      end

      it 'returns NOT_LAWSON for non-Lawson locations' do
        allow(location).to receive(:corporation_group_is_lawson).and_return(false)
        expect(location.get_type).to eq('not_lawson')
      end
    end

    describe '#camelized_type' do
      it 'returns camelized type based on location type' do
        allow(location).to receive(:get_type).and_return('ローソン')
        expect(location.camelized_type).to eq('Lawson')
      end

      it 'returns NotLawson for non-Lawson locations' do
        allow(location).to receive(:get_type).and_return('not_lawson')
        expect(location.camelized_type).to eq('NotLawson')
      end
    end

    describe '#job_categories_text' do
      it 'returns job category name when present' do
        job_category = build(:job_category, name: 'Test Category')
        location.job_category = job_category
        expect(location.job_categories_text).to eq('Test Category')
      end

      it 'returns default lawson job category when job_category_name is nil' do
        allow(location).to receive(:job_category_name).and_return(nil)
        expect(location.job_categories_text).to eq('コンビニエンスストア')
      end
    end

    describe '#name_and_code' do
      it 'returns combined code and name' do
        location.code = 'TEST001'
        location.name = 'Test Location'
        expect(location.name_and_code).to eq('TEST001 Test Location')
      end
    end

    describe '#only_name' do
      it 'returns name without location type' do
        location.name = 'ローソン Test Store'
        allow(location).to receive(:get_type).and_return('ローソン')
        expect(location.only_name).to eq('Test Store')
      end
    end
  end
end
