require 'rails_helper'

RSpec.describe BillingUnitPrice, type: :model do
  subject { build(:billing_unit_price) }

  describe 'Associations' do
    it { should belong_to(:prefecture) }
  end

  describe 'Validators' do
    describe 'presence' do
      it { should validate_presence_of(:prefecture_id) }
    end
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      price = create(:billing_unit_price)
      price.destroy
      expect(BillingUnitPrice.find_by(id: price.id)).to be_nil
      expect(BillingUnitPrice.with_deleted.find_by(id: price.id)).to eq(price)
    end
  end
end
