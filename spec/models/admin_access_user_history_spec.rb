require 'rails_helper'

RSpec.describe AdminAccessUserHistory, type: :model do
  describe 'enums' do
    it { should define_enum_for(:access_type).with_values(view: 0, change: 1, download: 2) }
    it { should define_enum_for(:target_type).with_values(staff: 0, owner: 1, registration: 3) }
  end

  describe 'associations' do
    it { should belong_to(:admin) }
  end

  describe 'validations' do
    it 'is valid with valid attributes' do
      history = described_class.new(
        admin: build_stubbed(:admin),
        access_type: :view,
        target_type: :staff
      )
      expect(history).to be_valid
    end

    it 'is invalid without an admin' do
      history = described_class.new(
        admin: nil,
        access_type: :view,
        target_type: :owner
      )
      expect(history).not_to be_valid
      expect(history.errors[:admin]).to include("Adminを入力してください。")
    end
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      history = create(:admin_access_user_history)
      history.destroy
      expect(described_class.find_by(id: history.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: history.id)).to eq(history)
    end
  end
end
