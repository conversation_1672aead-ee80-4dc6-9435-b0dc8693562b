require 'rails_helper'

RSpec.describe CorpGrViolationDateLog, type: :model do
  describe 'Validators' do
    describe 'valid_date' do
      shared_examples 'validate date format' do |field|
        it "skips validation if #{field} is nil" do
          log = build(:corp_gr_violation_date_log, field => nil)
          log.validate
          expect(log.errors.added?(field, :invalid_date_format)).to be false
        end

        it "skips validation if #{field} is blank string" do
          log = build(:corp_gr_violation_date_log, field => "")
          log.validate
          expect(log.errors.added?(field, :invalid_date_format)).to be false
        end

        it "skips validation if #{field} is not a string" do
          log = build(:corp_gr_violation_date_log, field => 123)
          log.validate
          expect(log.errors.added?(field, :invalid_date_format)).to be false
        end

        it "does not add error if #{field} match date regex" do
          log = build(:corp_gr_violation_date_log, field => "2025/01/01")
          log.validate
          expect(log.errors.added?(field, :invalid_date_format)).to be false
        end

        it "adds error if #{field} does not match date regex" do
          log = build(:corp_gr_violation_date_log, field => "abc")
          log.validate
          expect(log.errors.added?(field, :invalid_date_format)).to be true
        end
      end

      it_behaves_like 'validate date format', :haken_start_date
      it_behaves_like 'validate date format', :extended_date
      it_behaves_like 'validate date format', :violation_day
    end

    describe 'Uploaders' do
      it 'mounts uploader' do
        log = build(:corp_gr_violation_date_log)
        expect(log.violation_file).to be_a(CorporationViolationFileUploader)
      end

      it 'stores and retrieves uploaded file' do
        file = fixture_file_upload("sample.png", "image/png")
        log = build(:corp_gr_violation_date_log, violation_file: file)
        expect(log.violation_file.url).to be_present
      end
    end
  end

  describe 'Associations' do
    it{should belong_to(:corporation_group)}
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      log = create(:corp_gr_violation_date_log)
      log.destroy
      expect(described_class.find_by(id: log.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: log.id)).to eq(log)
    end
  end

  describe '#violation_file_name' do
    let(:log) { build(:corp_gr_violation_date_log) }

    it 'returns nil if violation_file is blank' do
      allow(log).to receive(:violation_file).and_return(nil)
      expect(log.violation_file_name).to be_nil
    end

    it 'returns violation_file name when violation_file exists' do
      file = fixture_file_upload("sample.png", "image/png")
      log.violation_file = file
      expect(log.violation_file_name).to eq("sample.png")
    end
  end
end
