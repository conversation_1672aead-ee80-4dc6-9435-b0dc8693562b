require 'rails_helper'
require 'timecop'

RSpec.describe Arrangement, type: :model do
  let(:day){Time.zone.local(2025, 1, 1, 0, 0)}
  let(:order){create(:order)}
  let(:order_branch){create(:order_branch, order: order)}
  let(:order_case){create(:order_case, order: order, order_branch: order_branch)}
  let(:order_portion){create(:order_portion, order: order, order_branch: order_branch, order_case: order_case)}
  subject { build(:arrangement, order: order, order_branch: order_branch, order_case: order_case, order_portion: order_portion) }

  before(:example) do
    Timecop.freeze
  end

  after(:example) do
    Timecop.return
  end

  describe 'Validation' do
    describe 'uniqueness' do
      context 'when staff_id is present' do
        it 'validates uniqueness of parent_id' do
          subject.staff_id = 1
          should validate_uniqueness_of(:parent_id).scoped_to([:order_case_id, :staff_id])
        end

        it 'validates uniqueness of order_portion_id' do
          allow_any_instance_of(described_class).to receive(:is_migrated).and_return(true)
          should validate_uniqueness_of(:order_portion_id)
        end
      end
    end

    describe 'presence' do
      it{should validate_presence_of(:order_id)}
      it{should validate_presence_of(:order_branch_id)}
      it{should validate_presence_of(:order_case_id)}
      it{should validate_presence_of(:order_portion_id)}
      it{should validate_presence_of(:working_started_at)}
      it{should validate_presence_of(:working_ended_at)}
      it{should validate_presence_of(:order_segment_id)}

      it 'validates presence of break_time' do
        allow(subject).to receive(:break_time).and_return(nil)
        should validate_presence_of(:break_time)
      end

      it "validates presence of rest1_started_at, rest1_ended_at" do
        allow(subject).to receive(:rest2_editable).and_return(true)
        should validate_presence_of(:rest1_started_at)
        should validate_presence_of(:rest1_ended_at)
      end

      it "validates presence of rest2_started_at, rest2_ended_at" do
        allow(subject).to receive(:rest2_editable).and_return(true)
        should validate_presence_of(:rest2_started_at)
        should validate_presence_of(:rest2_ended_at)
      end

      it "validates presence of rest3_started_at, rest3_ended_at" do
        allow(subject).to receive(:rest3_editable).and_return(true)
        should validate_presence_of(:rest3_started_at)
        should validate_presence_of(:rest3_ended_at)
      end

      it "validates presence of staff_id" do
        allow(subject).to receive(:staff_present).and_return(true)
        should validate_presence_of(:staff_id)
      end
    end

    describe "value_greater_than" do
      before(:example) do
        allow(subject).to receive(:set_working_time).and_return(nil)
        allow(subject).to receive(:set_rest_time_date).and_return(nil)
        allow(subject).to receive(:set_break_time).and_return(nil)
      end

      it 'adds error when rest1_ended_at is greater than rest1_started_at' do
        subject.rest1_started_at = '2025/01/01 09:00'.in_time_zone
        subject.rest1_ended_at = '2025/01/01 08:00'.in_time_zone
        subject.validate
        rest1_name = described_class.human_attribute_name(:rest1_started_at)
        expect(subject.errors.added?(:rest1_ended_at, :value_greater_than, with: rest1_name)).to be true
      end

      it 'adds error when rest2_ended_at is greater than rest2_started_at' do
        subject.rest2_started_at = '2025/01/01 09:00'.in_time_zone
        subject.rest2_ended_at = '2025/01/01 08:00'.in_time_zone
        subject.validate
        rest2_name = described_class.human_attribute_name(:rest2_started_at)
        expect(subject.errors.added?(:rest2_ended_at, :value_greater_than, with: rest2_name)).to be true
      end

      it 'adds error when rest3_ended_at is greater than rest3_started_at' do
        subject.rest3_started_at = '2025/01/01 09:00'.in_time_zone
        subject.rest3_ended_at = '2025/01/01 08:00'.in_time_zone
        subject.validate
        rest3_name = described_class.human_attribute_name(:rest3_started_at)
        expect(subject.errors.added?(:rest3_ended_at, :value_greater_than, with: rest3_name)).to be true
      end

      it 'adds error when working_ended_at is greater than working_started_at' do
        subject.working_started_at = '2025/01/01 09:00'.in_time_zone
        subject.working_ended_at = '2025/01/01 08:00'.in_time_zone
        subject.validate
        name = described_class.human_attribute_name(:working_started_at)
        expect(subject.errors.added?(:working_ended_at, :value_greater_than, with: name)).to be true
      end
    end

    describe 'working_time_limit_rule' do
      let(:day){Time.zone.local(2025, 1, 1, 0, 0)}

      it 'skips validation if order is_migrated' do
        allow(subject).to receive(:is_migrated).and_return(true)
        subject.validate
        expect(subject.errors.added?(:working_ended_at, :over_12)).to be false
      end

      it 'skips validation if working_started_at is blank' do
        allow(subject).to receive(:working_started_at).and_return(nil)
        subject.validate
        expect(subject.errors.added?(:working_ended_at, :over_12)).to be false
      end

      it 'skips validation if working_ended_at is blank' do
        allow(subject).to receive(:working_ended_at).and_return(nil)
        subject.validate
        expect(subject.errors.added?(:working_ended_at, :over_12)).to be false
      end

      it 'adds error when shift time is over 12 hours' do
        subject.working_started_at = day.change(hour: 7, min: 0)
        subject.working_ended_at = day.change(hour: 21, min: 0)

        subject.validate
        expect(subject.errors.added?(:working_ended_at, :over_12)).to be true
      end

      it 'adds error when shift time is over 12 hours (overnight)' do
        subject.working_started_at = day.change(hour: 20, min: 0)
        subject.working_ended_at = day.change(hour: 11, min: 0)

        subject.validate
        expect(subject.errors.added?(:working_ended_at, :over_12)).to be true
      end
    end

    describe 'required_break_time_rule' do
      context 'when conditions not met to validate required break time' do
        before(:example) do
          allow(subject).to receive(:required_break_6h_shift).and_return(45)
          allow(subject).to receive(:required_break_8h_shift).and_return(60)
        end

        it 'skips validation if order is_migrated' do
          allow(subject).to receive(:is_migrated).and_return(true)
          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 45)).to be false
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
        end

        it 'skips validation if working_started_at is blank' do
          allow(subject).to receive(:working_started_at).and_return(nil)
          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 45)).to be false
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
        end

        it 'skips validation if working_ended_at is blank' do
          allow(subject).to receive(:working_ended_at).and_return(nil)
          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 45)).to be false
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
        end
      end

      context 'when break time satisfy required_break_time_rule' do
        it 'does not add error if working_time is under 6 hours' do
          allow(subject).to receive(:break_time).and_return(0)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 13, min: 0)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 45)).to be false
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
          expect(subject.errors.added?(:rest1_started_at, :rest_time_too_much)).to be false
        end

        it 'does not add error if working_time is between 6-8 hours and break_time is greater than 45 mins' do
          allow(subject).to receive(:break_time).and_return(45)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 15, min: 0)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 45)).to be false
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
          expect(subject.errors.added?(:rest1_started_at, :rest_time_too_much)).to be false
        end

        it 'does not add error if working_time is between 8-12 hours and break_time is greater than 60 mins' do
          allow(subject).to receive(:break_time).and_return(60)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 18, min: 0)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 45)).to be false
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
          expect(subject.errors.added?(:rest1_started_at, :rest_time_too_much)).to be false
        end
      end

      context 'when too little/too much break' do
        it 'adds error when work between 6-8 hours but break is under 45 mins' do
          allow(subject).to receive(:break_time).and_return(40)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 15, min: 0)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 45)).to be true
        end

        it 'adds error when work between 6-8 hours but break is under 45 mins (required_break under 45 mins)' do
          allow(subject).to receive(:break_time).and_return(30)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 14, min: 40)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_1, break_time: 40)).to be true
        end

        it 'adds error when work between 8-12 hours but break is under 60 mins' do
          allow(subject).to receive(:break_time).and_return(50)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 18, min: 0)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be true
        end

        it 'adds error when work between 8-12 hours but break is under 60 mins & (required_break under 60 mins)' do
          allow(subject).to receive(:break_time).and_return(50)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 16, min: 55)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 55)).to be true
        end

        it 'adds error when too much break time' do
          allow(subject).to receive(:break_time).and_return(300)

          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 13, min: 0)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :rest_time_too_much)).to be true
        end

        it 'adds error on rest2_started_at' do
          subject.working_started_at = day.change(hour: 8, min: 0)
          subject.working_ended_at = day.change(hour: 20, min: 0)
          subject.rest1_started_at = day.change(hour: 10, min: 00)
          subject.rest1_ended_at = day.change(hour: 10, min: 30)
          subject.rest2_started_at = day.change(hour: 15, min: 0)
          subject.rest2_ended_at = day.change(hour: 15, min: 20)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
          expect(subject.errors.added?(:rest2_started_at, :over_working_time_limit_2, break_time: 60)).to be true
        end

        it 'adds error on rest3_started_at' do
          subject.working_started_at  = day.change(hour: 8, min: 0)
          subject.working_ended_at    = day.change(hour: 20, min: 0)
          subject.rest1_started_at    = day.change(hour: 10, min: 0)
          subject.rest1_ended_at = day.change(hour: 10, min: 10)
          subject.rest2_started_at = day.change(hour: 15, min: 0)
          subject.rest2_ended_at = day.change(hour: 15, min: 10)
          subject.rest3_started_at = day.change(hour: 18, min: 0)
          subject.rest3_ended_at = day.change(hour: 18, min: 20)

          subject.validate
          expect(subject.errors.added?(:rest1_started_at, :over_working_time_limit_2, break_time: 60)).to be false
          expect(subject.errors.added?(:rest2_started_at, :over_working_time_limit_2, break_time: 60)).to be false
          expect(subject.errors.added?(:rest3_started_at, :over_working_time_limit_2, break_time: 60)).to be true
        end
      end
    end

    describe 'rest_time_range_rule' do
      it 'skips validation if working_ended_at is blank' do
        allow(subject).to receive(:working_ended_at).and_return(nil)

        subject.validate
        described_class::REST_TIMES.each do |n|
          expect(subject.errors.added?("rest#{n}_started_at", :rest_time_in_range)).to be false
          expect(subject.errors.added?("rest#{n}_ended_at", :rest_time_in_range)).to be false
        end
      end

      it 'adds error' do
        # TODO
      end
    end

    describe 'rest_time_overlap_rule' do
      # TODO
    end

    describe 'closed_day_rule' do

    end

    describe "in_future" do
      it do
        # :working_ended_at
      end
    end

    describe "record_locked" do
      it do
        # :record_locked
      end
    end

    describe 'working_time_overlap_rule' do
      # TODO
    end
  end
end
