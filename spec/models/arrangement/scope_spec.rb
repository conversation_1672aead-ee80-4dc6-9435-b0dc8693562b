require 'rails_helper'

RSpec.describe Arrangement, type: :model do
  describe 'Scopes' do
    shared_examples 'scope returns ActiveRecord::Relation' do |scope, options|
      it "filters arrangement by scope #{scope}" do
        if options.blank?
          expect(described_class.send(scope)).to be_a(ActiveRecord::Relation)
        else
          expect(described_class.send(scope, *options)).to be_a(ActiveRecord::Relation)
        end
      end
    end

    context 'when scope filters by datetime' do
      let!(:arr1){create(:arrangement, working_started_at: "2025/01/01 09:00".in_time_zone)}
      let!(:arr2){create(:arrangement, working_started_at: "2025/01/01 12:00".in_time_zone)}
      let!(:arr3){create(:arrangement, working_started_at: "2025/01/01 14:00".in_time_zone)}

      describe 'after_time' do
        it 'filters arrangements with working_started_at after time' do
          arrs = described_class.after_time("2025/01/01 12:00".in_time_zone)
          expect(arrs).not_to include(arr1)
          expect(arrs).not_to include(arr2)
          expect(arrs).to include(arr3)
        end
      end

      describe 'in_range_trigger' do
        it 'filters arrangements with working_started_at from time' do
          arrs = described_class.in_range_trigger("2025/01/01 12:00".in_time_zone)
          expect(arrs).not_to include(arr1)
          expect(arrs).to include(arr2)
          expect(arrs).to include(arr3)
        end
      end

      describe 'in_range' do
        it 'filters arrangements between time range' do
          arrs = described_class.in_range("2025/01/01 10:00".in_time_zone, "2025/01/01 13:00".in_time_zone)
          expect(arrs).not_to include(arr1)
          expect(arrs).to include(arr2)
          expect(arrs).not_to include(arr3)
        end
      end

      describe 'working_started_lte' do
        it 'filters arrangements before time' do
          arrs = described_class.working_started_lte("2025/01/01 12:00".in_time_zone)
          expect(arrs).to include(arr1)
          expect(arrs).to include(arr2)
          expect(arrs).not_to include(arr3)
        end
      end

      describe 'start_after_now' do
        it 'filters future arrangements' do
          future_arr = create(:arrangement, working_started_at: ServerTime.now + 1.day)
          arrs = described_class.start_after_now
          expect(arrs).not_to include(arr3)
          expect(arrs).to include(future_arr)
        end
      end
    end

    context 'when scope filters by display_status_id' do
      let!(:arr_public){create(:arrangement, display_status_id: :recruiting_public)}
      let!(:arr_limited){create(:arrangement, display_status_id: :recruiting_limited)}
      let!(:arr_hidden){create(:arrangement, display_status_id: :recruiting_hidden)}
      let!(:arr_has_apply){create(:arrangement, display_status_id: :has_apply)}
      let!(:arr_cancel){create(:arrangement, display_status_id: :cancel)}
      let!(:arr_temp_arrange){create(:arrangement, display_status_id: :temporary_arrange)}
      let!(:arr_arranged){create(:arrangement, display_status_id: :arranged)}
      let!(:arr_cancel_has_ins){create(:arrangement, display_status_id: :cancel_after_arrange_has_insurance)}
      let!(:arr_cancel_no_ins){create(:arrangement, display_status_id: :cancel_after_arrange_no_insurance)}
      let!(:arr_absence){create(:arrangement, display_status_id: :absence)}
      let!(:arr_absence_alt){create(:arrangement, display_status_id: :absence_has_alternative)}
      let!(:arr_finish){create(:arrangement, display_status_id: :finish_recruiting)}

      describe 'by_display_status' do
        it 'filters arrangements by display_status_id' do
          arrs = described_class.by_display_status([:recruiting_public, :has_apply, :arranged])
          expect(arrs).to include(arr_public)
          expect(arrs).to include(arr_has_apply)
          expect(arrs).to include(arr_arranged)
        end
      end

      describe 'is_cancel_statuses' do
        it 'filters canceled arrangements' do
          arrs = described_class.is_cancel_statuses
          expect(arrs).to include(arr_cancel)
          expect(arrs).to include(arr_cancel_has_ins)
          expect(arrs).to include(arr_cancel_no_ins)
        end
      end

      describe 'is_absence_statuses' do
        it 'filters absent arrangements' do
          arrs = described_class.is_absence_statuses
          expect(arrs).to include(arr_absence)
          expect(arrs).to include(arr_absence_alt)
        end
      end

      describe 'is_avalable_show_jobs' do
        it 'filters arrangement available to for display' do
          arrs = described_class.is_avalable_show_jobs
          expect(arrs).not_to include(arr_cancel)
          expect(arrs).not_to include(arr_cancel_has_ins)
          expect(arrs).not_to include(arr_cancel_no_ins)
          expect(arrs).not_to include(arr_absence)
          expect(arrs).not_to include(arr_absence_alt)
        end
      end

      describe 'is_available_temporary_arranged' do
        it 'filters arrangements which is available for temporary arrange' do
          arrs = described_class.is_available_temporary_arranged
          expect(arrs).to include(arr_public)
          expect(arrs).to include(arr_limited)
          expect(arrs).to include(arr_hidden)
          expect(arrs).to include(arr_has_apply)
        end
      end

      describe 'is_recruiting_jobs' do
        it 'filters recruiting jobs' do
          arrs = described_class.is_recruiting_jobs
          expect(arrs).to include(arr_public)
          expect(arrs).to include(arr_limited)
          expect(arrs).to include(arr_hidden)
          expect(arrs).to include(arr_has_apply)
          expect(arrs).to include(arr_temp_arrange)
        end
      end
    end

    context 'when scope filters by status_id of order_portion' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'temp_arrange'
      it_behaves_like 'scope returns ActiveRecord::Relation', 'temp_arrange_by_ids', 1
      it_behaves_like 'scope returns ActiveRecord::Relation', 'temporary_arrange_not_in_ids', 1
      it_behaves_like 'scope returns ActiveRecord::Relation', 'is_arranged'
      it_behaves_like 'scope returns ActiveRecord::Relation', 'comming_work'
      it_behaves_like 'scope returns ActiveRecord::Relation', 'not_arrange'
      it_behaves_like 'scope returns ActiveRecord::Relation', 'not_temporary_arrange'
      it_behaves_like 'scope returns ActiveRecord::Relation', 'not_arrange_portion'
      it_behaves_like 'scope returns ActiveRecord::Relation', 'is_alternative'
    end

    describe 'arrived' do
      it 'filters arrangements by is_arrived' do
        arr1 = create(:arrangement, is_arrived: true)
        arr2 = create(:arrangement, is_arrived: false)
        arrs = described_class.arrived

        expect(arrs).to include(arr1)
        expect(arrs).not_to include(arr2)
      end
    end

    describe 'by_ids' do
      it 'filters arrangements by ids' do
        arr1 = create(:arrangement)
        arr2 = create(:arrangement)
        arrs = described_class.by_ids(arr1.id)

        expect(arrs).to contain_exactly(arr1)
        expect(arrs).not_to include(arr2)
      end
    end

    describe 'of_staff' do
      it 'filters arrangements by staff_id' do
        staff = create(:staff)
        arr1 = create(:arrangement, staff: staff)
        arrs = described_class.of_staff(staff.id)
        expect(arrs).to contain_exactly(arr1)
      end
    end

    describe 'staff_work_experience' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'staff_work_experience', 1
    end

    describe 'by_order_segment' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_order_segment', [[1, 2]]
    end

    describe 'failed_or_not_yet_request' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'failed_or_not_yet_request'
    end

    describe 'current_arrange_by_time_range' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'current_arrange_by_time_range', [Time.now, Time.now]
    end

    describe 'by_month' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_month', :prev_month
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_month', :current_month
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_month', :else
    end

    describe 'not_fail_requests' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'not_fail_requests'
    end

    describe 'skip_payment_request_id' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'skip_payment_request_id', 1
    end

    describe 'need_confirm_start_go_work_at' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'need_confirm_start_go_work_at', [Time.now]
    end

    describe 'need_input_working_time_at' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'need_input_working_time_at', [Time.now]
    end

    describe 'interval_need_input_working_time_at' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'interval_need_input_working_time_at', [Time.now]
    end

    describe 'not_inputted' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'not_inputted'
    end

    describe 'work_achievement_absence' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'work_achievement_absence'
    end

    describe 'by_working_status' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_working_status', [[:staff_approved, :owner_approved]]
    end

    describe 'already_worked_in_date_range' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'already_worked_in_date_range', [Time.now, Time.now]
    end

    describe 'working_remind' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'working_remind'
    end

    describe 'by_order_case_id' do
      it 'filters arrangements by order_case_id' do
        oc = create(:order_case)
        arr = create(:arrangement, order_case: oc)
        arrs = described_class.by_order_case_id(oc.id)
        expect(arrs).to contain_exactly(arr)
      end
    end

    describe 'by_prefecture' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_prefecture', 1
    end

    describe 'by_corporation' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_corporation', 1
    end

    describe 'by_location' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_location', 1
    end

    describe 'without_corporation' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'without_corporation', 1
    end

    describe 'without_location' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'without_location', 1
    end

    describe 'great_than_time_now' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'great_than_time_now'
    end

    describe 'is_japanese' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'is_japanese'
    end

    describe 'is_nationality' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'is_nationality'
    end

    describe 'locked' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'locked'
    end

    describe 'payroll_locked, not_locked_payroll, not_locked' do
      let!(:locked_arr) do
        arr = create(:arrangement)
        arr.update_columns(is_payroll_locked: true, locked_started_at: Time.now - 10.days)
        arr
      end
      let!(:unlocked_arr) do
        create(:arrangement, is_payroll_locked: false)
      end

      it 'filters arrangements which is_payroll_locked' do
        arrs = described_class.payroll_locked
        expect(arrs).to include(locked_arr)
        expect(arrs).not_to include(unlocked_arr)
      end

      it 'filters arrangements which is not payroll_locked' do
        arrs = described_class.not_locked_payroll
        expect(arrs).not_to include(locked_arr)
        expect(arrs).to include(unlocked_arr)
      end

      it 'filters arrangements by locked_started_at, locked_ended_at' do
        arrs = described_class.not_locked
        expect(arrs).not_to include(locked_arr)
        expect(arrs).to include(unlocked_arr)
      end
    end

    describe 'locked_of_payment_requests' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'locked_of_payment_requests', 1
    end

    describe 'belongs_to_location' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'belongs_to_location', 1
    end

    describe 'worked_for_location' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'worked_for_location', 1
    end

    describe 'by_order_portions_ids' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'by_order_portions_ids', 1
    end

    describe 'order_working_started_at' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'order_working_started_at'
    end

    describe 'training_segment' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'training_segment'
    end

    describe 'not_training_segment' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'not_training_segment'
    end

    describe 'contract_segment' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'contract_segment'
    end

    describe 'haken_regular_segment' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'haken_regular_segment'
    end

    describe 'is_finish_recruiting' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'is_finish_recruiting'
    end

    describe 'not_worked' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'not_worked'
    end

    describe 'location_sum_billing_total_amount' do
      it 'summarizes location arrange_billings.billing_total_amount' do
        expect(described_class.location_sum_billing_total_amount).to eq({})
      end
    end

    describe 'location_sum_billing_tax_exemption' do
      it 'summarizes location arrange_billings.billing_tax_exemption' do
        expect(described_class.location_sum_billing_tax_exemption).to eq({})
      end
    end

    describe 'is_training_session' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'is_training_session'
    end

    describe 'training_not_applicable' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'training_not_applicable'
    end

    describe 'order_by_custom_ids' do
      it_behaves_like 'scope returns ActiveRecord::Relation', 'order_by_custom_ids', [[1, 2, 3]]
    end
  end
end
