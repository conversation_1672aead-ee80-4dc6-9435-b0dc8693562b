require 'rails_helper'

RSpec.describe Arrangement, type: :model do
  let(:order){create(:order)}
  let(:order_branch){create(:order_branch, order: order)}
  let(:order_case){create(:order_case, order: order, order_branch: order_branch)}
  let(:order_portion){create(:order_portion, order: order, order_branch: order_branch, order_case: order_case)}
  subject { build(:arrangement, order: order, order_branch: order_branch, order_case: order_case, order_portion: order_portion) }

  describe 'Callbacks' do
    describe 'before_validation' do
      describe 'set_work_time' do
        it 'does nothing if working_started_a is blank' do
          subject.working_started_at = nil
          subject.validate
          expect(subject.working_started_at).to be_nil
        end

        it 'does nothing if working_ended_at is blank' do
          subject.working_ended_at = nil
          subject.validate
          expect(subject.working_ended_at).to be_nil
        end

        it 'sets working_ended_at' do
          subject.working_started_at = "2025/01/01 08:00".in_time_zone
          subject.working_ended_at = "1990/01/01 12:00".in_time_zone
          subject.validate
          expect(subject.working_ended_at).to eq("2025/01/01 12:00".in_time_zone)
        end

        it 'sets overnight working_ended_at' do
          subject.working_started_at = "2025/01/01 20:00".in_time_zone
          subject.working_ended_at = "1990/01/01 01:00".in_time_zone
          subject.validate
          expect(subject.working_ended_at).to eq("2025/01/02 01:00".in_time_zone)
        end
      end

      describe 'set_rest_time_date' do
        shared_examples 'sets date for rest time' do |number|
          it "does nothing if rest#{number} does not exist" do
            subject["rest#{number}_started_at"] = nil
            subject["rest#{number}_ended_at"] = nil
            subject.validate
            expect(subject["rest#{number}_started_at"]).to be_nil
            expect(subject["rest#{number}_ended_at"]).to be_nil
          end

          it "sets date to rest#{number}" do
            subject.working_started_at = "2025/01/01 08:00".in_time_zone
            subject["rest#{number}_started_at"] = "1990/01/01 12:00".in_time_zone
            subject["rest#{number}_ended_at"] = "1990/01/01 13:00".in_time_zone
            subject.validate
            expect(subject["rest#{number}_started_at"]).to eq("2025/01/01 12:00".in_time_zone)
            expect(subject["rest#{number}_ended_at"]).to eq("2025/01/01 13:00".in_time_zone)
          end

          it "sets date to rest#{number} when rest_start is overnight" do
            subject.working_started_at = "2025/01/01 22:00".in_time_zone
            subject["rest#{number}_started_at"] = "1990/01/01 01:00".in_time_zone
            subject["rest#{number}_ended_at"] = "1990/01/01 02:00".in_time_zone
            subject.validate
            expect(subject["rest#{number}_started_at"]).to eq("2025/01/02 01:00".in_time_zone)
            expect(subject["rest#{number}_ended_at"]).to eq("2025/01/02 02:00".in_time_zone)
          end

          it "sets date to rest#{number} when rest_start is overnight (no rest end)" do
            subject.working_started_at = "2025/01/01 22:00".in_time_zone
            subject["rest#{number}_started_at"] = "1990/01/01 01:00".in_time_zone
            subject["rest#{number}_ended_at"] = nil
            subject.validate
            expect(subject["rest#{number}_started_at"]).to eq("2025/01/02 01:00".in_time_zone)
            expect(subject["rest#{number}_ended_at"]).to be_nil
          end

          it "sets date to rest#{number} when rest_end is overnight" do
            subject.working_started_at = "2025/01/01 20:00".in_time_zone
            subject["rest#{number}_started_at"] = "1990/01/01 23:30".in_time_zone
            subject["rest#{number}_ended_at"] = "1990/01/01 00:30".in_time_zone
            subject.validate
            expect(subject["rest#{number}_started_at"]).to eq("2025/01/01 23:30".in_time_zone)
            expect(subject["rest#{number}_ended_at"]).to eq("2025/01/02 00:30".in_time_zone)
          end
        end

        described_class::REST_TIMES.each do |n|
          it_behaves_like 'sets date for rest time', n
        end
      end

      describe 'set_break_time' do
        it 'does nothing if there is no rest time' do
          subject.rest1_started_at = nil
          subject.rest1_ended_at = nil
          subject.validate
          expect(subject.break_time).to eq(0)
        end

        it 'calculates and assigns break_time' do
          subject.working_started_at = "2025/01/01 08:00".in_time_zone
          subject.working_ended_at = "2025/01/01 19:00".in_time_zone
          subject.rest1_started_at = "1990/01/01 12:00".in_time_zone
          subject.rest1_ended_at = "1990/01/01 12:30".in_time_zone
          subject.rest2_started_at = "1990/01/01 14:00".in_time_zone
          subject.rest2_ended_at = "1990/01/01 14:30".in_time_zone
          subject.rest3_started_at = "1990/01/01 16:00".in_time_zone
          subject.rest3_ended_at = "1990/01/01 16:30".in_time_zone
          subject.validate
          expect(subject.break_time).to eq(90)
        end

        it 'calculates and assigns break_time overnight' do
          subject.working_started_at = "2025/01/01 20:00".in_time_zone
          subject.working_ended_at = "2025/01/01 05:00".in_time_zone
          subject.rest1_started_at = "1990/01/01 22:00".in_time_zone
          subject.rest1_ended_at = "1990/01/01 22:30".in_time_zone
          subject.rest2_started_at = "1990/01/01 23:45".in_time_zone
          subject.rest2_ended_at = "1990/01/01 00:15".in_time_zone
          subject.rest3_started_at = "1990/01/01 03:00".in_time_zone
          subject.rest3_ended_at = "1990/01/01 03:30".in_time_zone
          subject.validate
          expect(subject.break_time).to eq(90)
        end
      end
    end

    describe 'before_save' do
      describe 'add_current_location_type' do
        it 'sets current_location_type to :normal when portion is is_copy_portion' do
          subject.is_copy_portion = true
          subject.save!
          expect(subject.current_location_type).to eq("normal_location")
        end

        it 'sets current_location_type to :normal when is_update_break_time' do
          subject.is_update_break_time = true
          subject.save!
          expect(subject.current_location_type).to eq("normal_location")
        end

        it 'does not update current_location_type if arrangement is not a new_record' do
          allow(order).to receive(:current_location_type).and_return("old_location")
          allow(subject).to receive(:new_record?).and_return(false)
          subject.save!
          expect(subject.current_location_type).not_to eq("old_location")
        end

        it 'updates current_location_type to be order_current_location_type' do
          allow(order).to receive(:current_location_type).and_return("old_location")
          subject.save!
          expect(subject.current_location_type).to eq("old_location")
        end
      end

      describe 'update_current_staff_department_id' do
        it 'sets staff_department_id to nil when staff_id is blank' do
          subject.staff_department_id = 1
          subject.staff_id = nil
          subject.save!
          expect(subject.staff_department_id).to be_nil
        end

        it 'sets staff_department_id to nil when staff_id is blank' do
          allow(subject).to receive(:staff_id_changed?).and_return(false)
          subject.staff_department_id = 2
          subject.save!
          expect{subject.save!}.not_to change(subject, :staff_department_id)
        end

        it 'updates staff_department_id to nil if staff has no department' do
          allow(subject).to receive(:staff_id_changed?).and_return(true)
          staff = create(:staff)
          subject.staff = staff
          subject.save!
          expect(subject.staff_department_id).to be_nil
        end

        it 'updates staff_department_id' do
          allow(subject).to receive(:staff_id_changed?).and_return(true)
          staff = create(:staff)
          dep = create(:department)
          create(:staff_department, :skip_callback,
            staff_id: staff.id, department: dep,
            affiliation_date: subject.working_started_at - 1.month
          )
          subject.staff = staff
          subject.save!
          expect(subject.staff_department_id).to eq(dep.id)
        end
      end
    end
  end
end
