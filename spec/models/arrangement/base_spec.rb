require "rails_helper"

RSpec.describe Arrangement, type: :model do
  subject{build(:arrangement)}

  describe 'Soft delete' do
    it 'removes from default scope' do
      arr = create(:arrangement)
      arr.destroy
      expect(described_class.find_by(id: arr.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: arr.id)).to eq(arr)
    end
  end

  describe "Constants" do
    it { expect(described_class).to be_const_defined(:ARRANGE_PAYMENT_ATTRS) }
    it { expect(described_class).to be_const_defined(:ARRANGE_BILLING_ATTRS) }
    it { expect(described_class).to be_const_defined(:WORK_ACHIEVEMENT_REST_ATTRS) }
    it { expect(described_class).to be_const_defined(:REST_METHODS) }
    it { expect(described_class).to be_const_defined(:ADMIN_ARRANGEMENT_METHODS) }
    it { expect(described_class).to be_const_defined(:ADMIN_SUM_COLUMNS) }
    it { expect(described_class).to be_const_defined(:ADMIN_PAYMENT_OT_TIME_SUM_COLUMNS) }
    it { expect(described_class).to be_const_defined(:TRIGGER_ARRANGEMENT_METHODS) }
    it { expect(described_class).to be_const_defined(:STAFF_ARRANGEMENT_TB_FIELDS) }
    it { expect(described_class).to be_const_defined(:STAFF_ARRANGEMENT_TB_METHODS) }
    it { expect(described_class).to be_const_defined(:COUNTER_FIELDS) }
    it { expect(described_class).to be_const_defined(:UPDATABLE_ATTRS) }
    it { expect(described_class).to be_const_defined(:BREAK_TIME_ATTRS) }
    it { expect(described_class).to be_const_defined(:ONLY_ATTRS) }
    it { expect(described_class).to be_const_defined(:BEFORE_WORK_TIME) }
    it { expect(described_class).to be_const_defined(:REST_TIMES) }
    it { expect(described_class).to be_const_defined(:HOUR_IN_DAY) }
    it { expect(described_class).to be_const_defined(:SECOND_IN_HOUR) }
    it { expect(described_class).to be_const_defined(:SECOND_PER_MINUTE) }
    it { expect(described_class).to be_const_defined(:OP_CENTER_TARGET_STATUS) }
    it { expect(described_class).to be_const_defined(:DISABLE_DESTROY_STATUS) }
    it { expect(described_class).to be_const_defined(:NOT_FINISH_RECRUITING_STATUSES) }
    it { expect(described_class).to be_const_defined(:MAPPING_DISPLAY_STATUS) }
    it { expect(described_class).to be_const_defined(:AVAILABLE_TO_EDIT_STATUS) }
    it { expect(described_class).to be_const_defined(:NOT_ARRANGED_STATUS) }
    it { expect(described_class).to be_const_defined(:MAPPING_DISPLAY_STATUS_EXPORT) }
    it { expect(described_class).to be_const_defined(:PREPARE_HOUR) }
    it { expect(described_class).to be_const_defined(:WORK_TIME_RANGE) }
    it { expect(described_class).to be_const_defined(:ARRANGED_DISPLAY_STATUS) }
    it { expect(described_class).to be_const_defined(:MAX_WORKING_HOUR) }
    it { expect(described_class).to be_const_defined(:ARRANGE_TIME_VALID) }
    it { expect(described_class).to be_const_defined(:CANCEL_STATUSES) }
    it { expect(described_class).to be_const_defined(:ABSENCE_STATUSES) }
    it { expect(described_class).to be_const_defined(:RECRUITING_STATUSES) }
    it { expect(described_class).to be_const_defined(:AVAILABLE_TEMP_ARRANGED_STATUSES) }
    it { expect(described_class).to be_const_defined(:BATCH_ARRANGED_DISPLAY_STATUS) }
    it { expect(described_class).to be_const_defined(:BATCH_ARRANGED_DISPLAY_STATUS_TEXT) }
    it { expect(described_class).to be_const_defined(:UNAVAILABLE_TRAINING_STATUSES) }
  end

  describe 'Associations' do
    describe 'belongs_to' do
      it{should belong_to(:order)}
      it{should belong_to(:order_case)}
      it{should belong_to(:staff).optional}
      it{should belong_to(:staff_department).class_name('Department').with_foreign_key(:staff_department_id).optional}
      it{should belong_to(:order_portion)}
      it{should belong_to(:order_branch)}
      it{should belong_to(:parent_arrangement).class_name('Arrangement').with_foreign_key(:parent_id).optional}
      it{should belong_to(:admin).class_name('Admin').with_foreign_key(:approved_by_admin_id).optional}
      it{should belong_to(:billing_payment_template).optional}
    end

    describe 'has_one' do
      it{should have_one(:arrange_payment).dependent(:destroy)}
      it{should have_one(:work_achievement).dependent(:destroy)}
      it{should have_one(:staff_evaluation)}
      it{should have_one(:arrange_billing).dependent(:destroy)}
      it{should have_one(:location_evaluation)}
      it{should have_one(:migration_arrangement_history).dependent(:destroy)}
    end

    describe 'has_many' do
      it{should have_many(:arrange_payment_requests)}
      it{should have_many(:payment_requests)}
      it{should have_many(:staff_apply_order_cases)}
      it{should have_many(:arrange_logs)}
      it{should have_many(:arrange_payment_logs).dependent(:destroy)}
      it{should have_many(:arrange_billing_logs).dependent(:destroy)}
    end
  end

  describe 'Delegations' do
    shared_examples 'delegate from model' do |methods, model|
      methods.each do |method|
        it {should delegate_method(method).to(model)}
      end
    end

    describe 'methods from order' do
      [:is_migrated, :location_caution_to_staff_mail].each do |method|
        it {should delegate_method(method).to(:order).allow_nil}
      end

      [
        :corporation_full_name, :order_segment_id, :segment_name, :location_name,
        :location_short_name,:location_code, :location_stations_1_name,
        :location_stations_1_station_name, :location_pos_type_id,
        :location_is_store_parking_area_usable, :note, :corporation_id,
        :location_id, :business_name, :pic_department_name, :location_tel,
        :full_address, :location_full_address, :organization_full_name,
        :organization_position_name, :haken_destination_pic_position,
        :haken_destination_pic_name, :haken_destination_pic_id,
        :haken_destination_pic_tel, :mandator_name, :mandator_position, :mandator_tel,
        :claim_pic_position, :claim_pic_name, :claim_pic_tel, :corporation_address,
        :corporation_representative_name, :claim_process_pic_name, :haken_source_pic_position,
        :haken_source_pic_name, :order_pic_user_name, :location_postal_code,
        :mandator_id, :haken_source_pic_tel, :corporation_tel, :corporation_group_id,
        :real_pic_department_name, :current_location_type, :user_order_pic,
        :is_from_lawson?, :corporation_group_full_name, :location_job_categories_text,
        :location_is_export_timesheet
      ].each do |method|
        it {should delegate_method(method).to(:order).with_prefix.allow_nil}
      end
    end

    describe 'methods from order_branch' do
      %i(is_time_changable required_start_time required_end_time).each do |method|
        it{should delegate_method(method).to(:order_branch)}
      end
    end

    describe 'methods from arrange_billing' do
      Arrangement::ARRANGE_BILLING_ATTRS.each do |method|
        it{should delegate_method(method).to(:arrange_billing)}
      end

      Arrangement::REST_METHODS.each do |method|
        it{should delegate_method(method).to(:arrange_billing).with_prefix}
      end

      it{should delegate_method(:adjusment_type_id).to(:arrange_billing).with_prefix}
    end

    describe 'methods from arrange_payment' do
      Arrangement::ARRANGE_PAYMENT_ATTRS.each do |method|
        it{should delegate_method(method).to(:arrange_payment)}
      end
    end

    describe 'methods from work_achievement' do
      Arrangement::WORK_ACHIEVEMENT_REST_ATTRS.each do |method|
        it{should delegate_method(method).to(:work_achievement).with_prefix}
      end

      Arrangement::REST_METHODS.each do |method|
        it{should delegate_method(method).to(:work_achievement).with_prefix}
      end
    end

    describe 'methods from order_case' do
      [
        :is_urgent, :is_special_offer, :segment_id, :special_offer_note,
        :case_started_at, :case_ended_at, :work_time, :contract_time,
        :invoice_target, :training_session_text
      ].each do |method|
        it{should delegate_method(method).to(:order_case).with_prefix.allow_nil}
      end
    end

    describe 'methods from staff' do
      [
        :current_department_name, :account_name, :home_station_station_name,
        :age, :indefinite_employment_flag, :insurance_subsection_type, :basic_pension_number,
        :employment_insurance_number, :full_address, :name, :gender_id, :age_type,
        :contract_date, :employment_period_status, :health_insurance, :welfare_pension_insurance,
        :uniform_size, :pant_size, :shoes_size, :employment_insurance, :stable_employments_list,
        :training_or_career_list, :complaint_list, :account_name_kana, :total_work_experience,
        :home_tel, :tel, :account_email, :display_workable_time, :is_working_car,
        :level_up_training, :new_pos_training, :staff_number, :insurance_subsection_label
      ].each do |method|
        it{should delegate_method(method).to(:staff).with_prefix.allow_nil}
      end
    end
  end

  describe 'Enums' do
    it do
      should define_enum_for(:display_status_id).with_values(
        recruiting_public: 1, recruiting_limited: 2, recruiting_hidden: 3,
        has_apply: 4, cancel: 5, temporary_arrange: 6, arranged: 7,
        cancel_after_arrange_has_insurance: 8, absence: 9, finish_recruiting: 10,
        cancel_after_arrange_no_insurance: 11, absence_has_alternative: 12
      )
    end

    it do
      should define_enum_for(:current_location_type)
        .with_values(normal_location: 0, new_location: 1, old_location: 3)
    end
  end
end
