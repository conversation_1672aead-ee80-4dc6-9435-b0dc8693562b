require 'rails_helper'

RSpec.describe RailwayLine, type: :model do
  describe 'Associations' do
    it { should have_many(:stations) }
    it { should belong_to(:prefecture) }
  end
  describe 'Validations' do
    it { should validate_presence_of(:name) }
  end

  describe 'Scopes' do
    describe '.of_prefecture' do
      let!(:prefecture1) { create(:prefecture) }
      let!(:prefecture2) { create(:prefecture) }
      let!(:railway_line1) { create(:railway_line, prefecture: prefecture1) }
      let!(:railway_line2) { create(:railway_line, prefecture: prefecture2) }

      it 'returns railway lines of the specified prefecture' do
        expect(described_class.of_prefecture(prefecture1.id)).to include(railway_line1)
        expect(described_class.of_prefecture(prefecture1.id)).not_to include(railway_line2)
      end
    end

    describe '.of_adjacent_prefecture' do
      let!(:prefecture1) { create(:prefecture) }
      let!(:prefecture2) { create(:prefecture) }
      let!(:prefecture3) { create(:prefecture) }
      let!(:adjacent_prefecture) { create(:adjacent_prefecture, parent_id: prefecture1.id, child_id: prefecture2.id) }
      let!(:railway_line1) { create(:railway_line, prefecture: prefecture1) }
      let!(:railway_line2) { create(:railway_line, prefecture: prefecture2) }
      let!(:railway_line3) { create(:railway_line, prefecture: prefecture3) }

      it 'returns railway lines of the specified prefecture and its adjacent prefectures' do
        result = described_class.of_adjacent_prefecture(prefecture1.id)
        expect(result).to include(railway_line1, railway_line2)
        expect(result).not_to include(railway_line3)
      end
    end
  end

  describe 'Instance methods' do
    describe '#full_name' do
      let(:prefecture) { create(:prefecture, name: 'Tokyo') }
      let(:railway_line) { create(:railway_line, name: 'Yamanote Line', prefecture: prefecture) }

      it 'returns the full name of the railway line' do
        expect(railway_line.full_name).to eq('Yamanote Line (Tokyo)')
      end

      it 'returns the name of the railway line if prefecture is nil' do
        railway_line.prefecture = nil
        expect(railway_line.full_name).to eq('Yamanote Line ()')
      end
    end
  end

  describe 'Class methods' do
    describe '.options_for_select' do
      let!(:prefecture1) { create(:prefecture, name: 'Tokyo') }
      let!(:prefecture2) { create(:prefecture, name: 'Osaka') }
      let!(:railway_line1) { create(:railway_line, name: 'Yamanote Line', prefecture: prefecture1) }
      let!(:railway_line2) { create(:railway_line, name: 'Keihin-Tohoku Line', prefecture: prefecture2) }

      it 'returns an array of options for select' do
        expected_options = [
          ['Yamanote Line (Tokyo)', railway_line1.id],
          ['Keihin-Tohoku Line (Osaka)', railway_line2.id]
        ]
        expect(described_class.options_for_select).to match_array(expected_options)
      end
    end
  end

  describe 'Acts as paranoid' do
    let!(:railway_line) { create(:railway_line) }

    it 'soft deletes the railway line' do
      railway_line.destroy
      expect(described_class.find_by(id: railway_line.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: railway_line.id)).to eq(railway_line)
    end

    it 'restores the railway line' do
      railway_line.destroy
      railway_line.restore
      expect(described_class.find_by(id: railway_line.id)).to eq(railway_line)
    end
  end
end
