require 'rails_helper'

RSpec.describe AdminActionLog, type: :model do
  let(:admin){build(:admin)}

  describe 'Associations' do
    it { should belong_to(:admin) }
    it { should belong_to(:target_object).optional }
  end

  describe 'Delegations' do
    it { should delegate_method(:name).to(:admin).with_prefix.allow_nil }
  end

  describe 'Constants' do
    it { expect(described_class).to be_const_defined(:TARGET_DATA) }
    it { expect(described_class).to be_const_defined(:ACTION_LOG_METHODS) }
    it { expect(described_class).to be_const_defined(:SPECIAL_CONTENT_KEYS) }
  end

  describe 'Serialization' do
    it 'serializes content as a Hash via YAML' do
      log = create(:admin_action_log, content: [{"foo": "bar"}])
      expect(log.reload.content).to eq([{"foo": "bar"}])
    end
  end

  describe 'Scopes' do
    describe '#search_by_admin' do
      it 'filters by admin_id' do
        admin1 = create(:admin)
        admin2 = create(:admin)
        log1 = create(:admin_action_log, admin: admin1)
        log2 = create(:admin_action_log, admin: admin2)

        expect(described_class.search_by_admin(admin1.id)).to contain_exactly(log1)
        expect(described_class.search_by_admin(admin2.id)).to contain_exactly(log2)
      end
    end
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      log = create(:admin_action_log)
      log.destroy
      expect(described_class.find_by(id: log.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: log.id)).to eq(log)
    end
  end

  describe '#content_text' do
    shared_examples 'when action_type only affects target' do |action|
      it 'returns translate content_text' do
        target = "training_schedule"
        log = build(:admin_action_log, admin: admin, action_type: action)
        expect(log.content_text).to eq(
          [I18n.t("admin.admin_action_log.#{target}.#{action}", admin_name: admin.name)]
        )
      end
    end

    it_behaves_like 'when action_type only affects target', 'create'
    it_behaves_like 'when action_type only affects target', 'delete'
    it_behaves_like 'when action_type only affects target', 'restore'
    it_behaves_like 'when action_type only affects target', 'auto_delete'
    it_behaves_like 'when action_type only affects target', 'auto_restore'

    shared_examples 'when action_type is staff-related' do |action|
      it 'returns translated content_text with staff info' do
        staff_info = double("Staff", account_name_with_staff_number: "Staff 000001")
        allow_any_instance_of(Staff.const_get(:ActiveRecord_Relation)).to receive(:find_by).and_return(staff_info)
        target = "training_schedule"
        log = build(:admin_action_log, admin: admin, action_type: action, content: {"staff_id"=>"1"})
        expect(log.content_text).to eq(
          [
            I18n.t(
              "admin.admin_action_log.#{target}.#{action}",
              admin_name: admin.name,
              staff_info: staff_info.account_name_with_staff_number
            )
          ]
        )
      end
    end

    it_behaves_like 'when action_type is staff-related', 'admin_books_staff'
    it_behaves_like 'when action_type is staff-related', 'admin_set_staff_to_absent_with_notice'
    it_behaves_like 'when action_type is staff-related', 'admin_set_staff_to_absent_without_notice'
    it_behaves_like 'when action_type is staff-related', 'admin_set_staff_to_joined'

    context 'when action_type is not defined' do
      it 'returns empty array content_text' do
        log = build(:admin_action_log, admin: admin, action_type: "not_real", content: ["foo"])
        expect(log.content_text).to eq([])
      end
    end

    context 'when action_type is update with simple content' do
      it 'returns translated content_text' do
        target = "training_schedule"
        log = build(:admin_action_log, admin: admin, action_type: "update", content: ["foo"])
        expect(log.content_text).to eq(
          [
            I18n.t(
              "admin.admin_action_log.#{target}.update.updated",
              admin_name: admin.name
            )
          ]
        )
      end
    end

    context 'when action_type is update with hash content' do
      it 'returns translated content_text' do
        pic = double("Admin", name: "Ponos Tech")
        allow(Admin).to receive(:find_by).and_return(pic)
        target = "training_schedule"
        log = build(:admin_action_log, admin: admin, action_type: "update", content: [{"total_portion"=>"1", "person_in_charge_id"=>"1"} ])
        expect(log.content_text).to eq(
          [
            I18n.t(
              "admin.admin_action_log.#{target}.update.total_portion",
              admin_name: admin.name,
              new_value: 1
            ),
            I18n.t(
              "admin.admin_action_log.#{target}.update.person_in_charge_id",
              admin_name: admin.name,
              new_value: pic.name
            )
          ]
        )
      end
    end

    context 'when action_type is admin_set_staff_reason_cancel' do
      it 'returns content_text with admin_set_reason_translation' do
        staff_info = double("Staff", account_name_with_staff_number: "Staff 000001")
        allow_any_instance_of(Staff.const_get(:ActiveRecord_Relation)).to receive(:find_by).and_return(staff_info)
        target = "training_schedule"
        reason = "Reason for canceling staff"
        log = build(
          :admin_action_log, admin: admin, action_type: "admin_set_staff_reason_cancel",
          content: {"staff_id"=>"1", "staff_training_survey_answer"=>reason}
        )
        expect(log.content_text).to eq(
          [
            I18n.t(
              "admin.admin_action_log.#{target}.admin_set_staff_reason_cancel",
              admin_name: admin.name,
              staff_info: staff_info.account_name_with_staff_number,
              reason_text: reason
            )
          ]
        )
      end
    end
  end
end
