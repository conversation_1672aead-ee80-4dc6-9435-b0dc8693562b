require 'rails_helper'

RSpec.describe StaffStatusLog, type: :model do
  # Test setup
  let(:staff) { create(:staff) }
  let(:staff_status_log) { create(:staff_status_log, staff: staff) }

  describe 'Associations' do
    it { should belong_to(:staff) }
  end

  describe 'Validations' do
    it { should validate_presence_of(:staff_id) }
  end

  describe 'Acts as paranoid' do
    it 'soft deletes the record' do
      staff_status_log.destroy
      expect(described_class.find_by(id: staff_status_log.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: staff_status_log.id)).to eq(staff_status_log)
    end

    it 'restores the soft deleted record' do
      staff_status_log.destroy
      staff_status_log.restore
      expect(described_class.find_by(id: staff_status_log.id)).to eq(staff_status_log)
    end
  end
end
