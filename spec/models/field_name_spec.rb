require 'rails_helper'

RSpec.describe FieldName, type: :model do
  subject { create(:field_name) }

  let(:redis) { instance_double(Lawson::RedisConnector) }

  before(:example) do
    allow(Lawson::RedisConnector).to receive(:new).and_return(redis)
    allow(redis).to receive(:set)
  end

  describe 'Constants' do
    it { expect(described_class).to be_const_defined(:REDIS_KEY) }
  end

  describe 'Enums' do
    it { should define_enum_for(:type_id).with_values(t_payment_deduction: 1, t_billing: 2) }
    it { should define_enum_for(:subtype_id).with_values(st_payment: 1, st_deduction: 2, st_billing: 3) }
    it { should define_enum_for(:disposal).with_values(day: 1, month: 2) }
  end

  describe 'Soft delete' do
    it 'removes from default scope' do
      new_field_name = create(:field_name)
      new_field_name.destroy
      expect(described_class.find_by(id: new_field_name.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: new_field_name.id)).to eq(new_field_name)
    end
  end

  describe 'Callbacks' do
    describe 'after_commit' do
      before(:example) do
        allow(described_class).to receive(:option_for_show_labels).and_return({foo: "bar"})
      end

      it 'writes option_for_show_labels to redis after create' do
        create(:field_name)
        expect(redis).to have_received(:set).with(
          FieldName::REDIS_KEY,
          {foo: "bar"}.to_json
        )
      end

      it "writes option_for_show_labels to redis after update" do
        fn = create(:field_name)
        fn.update!(name: "New Field Name")

        expect(redis).to have_received(:set).with(
          FieldName::REDIS_KEY,
          {foo: "bar"}.to_json
        ).exactly(2).times
      end

      it "writes option_for_show_labels to redis after destroy" do
        fn = create(:field_name)
        allow(redis).to receive(:set)

        fn.destroy

        expect(redis).to have_received(:set).with(
          FieldName::REDIS_KEY,
          {foo: "bar"}.to_json
        ).exactly(2).times
      end
    end
  end

  describe 'Class methods' do
    describe '.option_for_show_labels' do
      it 'returns labels as hash with symbolized keys' do
        result = described_class.pluck(:field_key, :name).to_h.deep_symbolize_keys
        expect(described_class.option_for_show_labels).to eq(result)
      end
    end

    describe '.option_for_show_labels_from_redis' do
      it 'returns labels from redis cache' do
        allow(redis).to receive(:get).with(FieldName::REDIS_KEY).and_return("{\"foo\": \"bar\"}")
        expect(described_class.option_for_show_labels_from_redis).to eq({foo: "bar"})
      end

      it 'returns empty hash when cache misses' do
        allow(redis).to receive(:get).with(FieldName::REDIS_KEY).and_return(nil)
        expect(described_class.option_for_show_labels_from_redis).to eq({})
      end
    end
  end
end
