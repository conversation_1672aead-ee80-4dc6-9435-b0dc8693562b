require 'rails_helper'

RSpec.describe StaffExpectation, type: :model do
  describe 'Constants' do
    it 'defines DAY_OF_WEEK' do
      expect(described_class::DAY_OF_WEEK).to eq(%w(monday tuesday wednesday thursday friday saturday sunday))
    end

    it 'defines WORK_EXPECTATION' do
      expect(described_class::WORK_EXPECTATION).to eq([:is_sunday, :is_monday, :is_tuesday, :is_wednesday, :is_thursday, :is_friday, :is_saturday])
    end

    it 'defines REGISTERED_PROFILE_WORK_CONDITIONS' do
      expect(described_class::REGISTERED_PROFILE_WORK_CONDITIONS).to eq({bw_20_30_hours: 1, gte_30_hours: 2})
    end

    it 'defines EXCEPT_DISPLAY_ATTRS' do
      expect(described_class::EXCEPT_DISPLAY_ATTRS).to eq(%w(staff_id created_at deleted_at updated_at))
    end
  end

  describe 'Enums' do
    it 'defines expectation_employment_type' do
      expect(described_class.expectation_employment_types).to eq({"haken" => 1, "introduction" => 2, "both" => 3})
    end

    it 'defines work_condition' do
      expect(described_class.work_conditions).to eq({"lte_20hours" => 0, "bw_20_30_hours" => 1, "gte_30_hours" => 2})
    end
  end

  describe 'Associations' do
    it { is_expected.to belong_to(:staff) }
    it { is_expected.to belong_to(:transportation_type).class_name('Type').with_foreign_key(:nearest_station_transportation).optional }
  end

  describe 'Validations' do
    it { is_expected.to validate_presence_of(:expectation_employment_type) }
  end

  describe 'Acts as paranoid' do
    it 'soft deletes the record' do
      staff_expectation = create(:staff_expectation)
      staff_expectation.destroy
      expect(described_class.find_by(id: staff_expectation.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: staff_expectation.id)).to eq(staff_expectation)
    end

    it 'restores the record' do
      staff_expectation = create(:staff_expectation)
      staff_expectation.destroy
      staff_expectation.restore
      expect(described_class.find_by(id: staff_expectation.id)).to eq(staff_expectation)
    end
  end

  describe 'Class methods' do
    describe '.format_data' do
      let(:format_type) { Settings.time.formats }
      let(:hash_data) do
        {
          'is_monday' => true,
          'monday_start_time_1' => Time.zone.parse('09:00'),
          'monday_end_time_1' => Time.zone.parse('17:00'),
          'is_tuesday' => false,
          'tuesday_start_time_1' => nil,
          'tuesday_end_time_1' => nil
        }
      end

      it 'formats the data' do
        formatted_data = described_class.format_data(hash_data)
        expect(formatted_data['monday_start_time_1']).to eq('09:00')
        expect(formatted_data['monday_end_time_1']).to eq('17:00')
        expect(formatted_data['is_tuesday']).to be_falsey
      end

      it 'returns nil if hash_data is nil' do
        expect(described_class.format_data(nil)).to be_nil
      end
    end
  end
end
