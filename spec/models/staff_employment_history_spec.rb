require 'rails_helper'

RSpec.describe StaffEmploymentHistory, type: :model do
  describe 'Associations' do
    it { should belong_to(:staff) }
  end

  describe 'Enums' do
    it 'defines SERVICE_WORK_TYPES' do
      expect(described_class::SERVICE_WORK_TYPES).to eq({apparel: 1, tel: 2, food: 3, cash: 4, card: 5})
    end

    it 'defines OFFICE_WORK_TYPES' do
      expect(described_class::OFFICE_WORK_TYPES).to eq({affairs: 1, sale: 2, accounting: 3, call: 4})
    end

    it 'defines OTHER_WORK_TYPES' do
      expect(described_class::OTHER_WORK_TYPES).to eq({food_factory: 1, manufacturing: 2, GS: 3, warehouse: 4})
    end
  end

  describe 'Serializations' do
    let(:staff_employment_history) { create(:staff_employment_history) }

    it 'serializes service_work_type as an array' do
      expect(described_class.new.service_work_type).to be_an(Array)
      expect(staff_employment_history.service_work_type_before_type_cast).to be_a(String)
    end

    it 'serializes office_work_type as an array' do
      expect(described_class.new.office_work_type).to be_an(Array)
      expect(staff_employment_history.office_work_type_before_type_cast).to be_a(String)
    end

    it 'serializes other_work_type as an array' do
      expect(described_class.new.other_work_type).to be_an(Array)
      expect(staff_employment_history.other_work_type_before_type_cast).to be_a(String)
    end
  end

  describe 'Callbacks' do
    describe 'before_save' do
      let(:staff_employment_history) do
        build(:staff_employment_history,
          service_work_type: ['1', '2'], office_work_type: ['1', '2'], other_work_type: ['1', '2'])
      end

      it 'converts service_work_type to integers' do
        staff_employment_history.save
        expect(staff_employment_history.service_work_type).to eq([1, 2])
      end

      it 'converts office_work_type to integers' do
        staff_employment_history.save
        expect(staff_employment_history.office_work_type).to eq([1, 2])
      end

      it 'converts other_work_type to integers' do
        staff_employment_history.save
        expect(staff_employment_history.other_work_type).to eq([1, 2])
      end
    end
  end

  describe 'Acts as paranoid' do
    let(:staff_employment_history) { create(:staff_employment_history) }

    it 'destroys the record' do
      staff_employment_history.destroy
      expect(described_class.find_by(id: staff_employment_history.id)).to be_nil
    end

    it 'restores the record' do
      staff_employment_history.destroy
      staff_employment_history.restore
      expect(described_class.find_by(id: staff_employment_history.id)).to eq(staff_employment_history)
    end
  end
end
