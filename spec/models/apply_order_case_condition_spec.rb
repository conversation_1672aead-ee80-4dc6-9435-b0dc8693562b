require 'rails_helper'
require 'timecop'

RSpec.describe ApplyOrderCaseCondition, type: :model do
  let(:order) do
    instance_double('Order', id: 1, corporation_id: 1, corporation_group_id: 1)
  end

  let(:order_case) do
    instance_double(
      'OrderCase', id: 1, order: order, regular_order?: false,
      corporation_id: 1,
      location_id: 1,
      case_started_at: ServerTime.now + 3.days,
      case_ended_at: ServerTime.now + 3.days + 4.hours
    )
  end

  let(:current_level) do
    instance_double('StaffLevel', before_debut?: false)
  end

  let(:staff) do
    instance_double(
      'Staff', id: 1, current_level: current_level, age_by_time: 30
    )
  end

  let(:condition) do
    described_class.new(order_case, staff)
  end

  context 'based on order_case status' do
    describe 'oc_status_1' do
      it 'adds error when order_case is canceled' do
        allow(order_case).to receive(:cancel?).and_return(true)
        condition.oc_status_1
        expect(condition.errors.added?(:apply_condition, :oc_status_1)).to be true
      end

      it 'does not add error when order_case is not canceled' do
        allow(order_case).to receive(:cancel?).and_return(false)
        condition.oc_status_1
        expect(condition.errors.added?(:apply_condition, :oc_status_1)).to be false
      end
    end

    describe 'oc_status_3' do
      it 'adds error when staff already applied the order_case' do
        allow(staff).to receive(:applied_order_case?).with(order_case.id).and_return(true)
        condition.oc_status_3
        expect(condition.errors.added?(:apply_condition, :oc_status_3)).to be true
      end

      it 'does not add error when staff has not applied order_case' do
        allow(staff).to receive(:applied_order_case?).with(order_case.id).and_return(false)
        condition.oc_status_3
        expect(condition.errors.added?(:apply_condition, :oc_status_3)).to be false
      end

      it 'skips validation when skip_oc_status_3 option is true' do
        skip_cond = described_class.new(order_case, staff, skip_oc_status_3: true)
        skip_cond.oc_status_3
        expect(skip_cond.errors.added?(:apply_condition, :oc_status_3)).to be false
      end
    end

    describe 'oc_status_4' do
      it 'adds error when order_case is full_arranged and staff is not arranged for it' do
        allow(staff).to receive(:arranged?).with(order_case.id).and_return(false)
        allow(order_case).to receive(:full_arranged?).and_return(true)

        condition.oc_status_4
        expect(condition.errors.added?(:apply_condition, :oc_status_4)).to be true
      end

      it 'skips validation when staff is already arranged for the order_case' do
        allow(staff).to receive(:arranged?).with(order_case.id).and_return(true)
        allow(order_case).to receive(:full_arranged?).and_return(true)

        condition.oc_status_4
        expect(condition.errors.added?(:apply_condition, :oc_status_4)).to be false
      end

      it 'does not add error when order_case is not full_arranged' do
        allow(staff).to receive(:arranged?).with(order_case.id).and_return(false)
        allow(order_case).to receive(:full_arranged?).and_return(false)

        condition.oc_status_4
        expect(condition.errors.added?(:apply_condition, :oc_status_4)).to be false
      end
    end

    describe 'oc_status_5' do
      it 'adds error when staff is arranged for the order_case' do
        allow(staff).to receive(:arranged?).with(order_case.id).and_return(true)

        condition.oc_status_5
        expect(condition.errors.added?(:apply_condition, :oc_status_5)).to be true
      end

      it 'does not add error when staff is not arranged for the order_case' do
        allow(staff).to receive(:arranged?).with(order_case.id).and_return(false)

        condition.oc_status_5
        expect(condition.errors.added?(:apply_condition, :oc_status_5)).to be false
      end
    end
  end

  context 'based on order case working_time' do
    describe 'oc_working_time_1' do
      it 'skips validation when order_case is_urgent' do
        allow(order_case).to receive(:is_urgent?).and_return(true)
        allow(order_case).to receive(:over_normal_apply_deadline?).and_return(true)

        condition.oc_working_time_1
        expect(condition.errors.added?(:apply_condition, :oc_working_time_1)).to be false
      end

      it 'adds error when not urgent and over normal apply deadline' do
        allow(order_case).to receive(:is_urgent?).and_return(false)
        allow(order_case).to receive(:over_normal_apply_deadline?).and_return(true)

        condition.oc_working_time_1
        expect(condition.errors.added?(:apply_condition, :oc_working_time_1)).to be true
      end

      it 'adds error when over regular_order apply deadline' do
        allow(order_case).to receive(:is_urgent?).and_return(false)
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:regular_order_timeout_apply?).and_return(true)

        condition.oc_working_time_1
        expect(condition.errors.added?(:apply_condition, :oc_working_time_1)).to be true
      end

      it 'does not add error when order_case is not over apply timeout' do
        allow(order_case).to receive(:is_urgent?).and_return(false)
        allow(order_case).to receive(:over_normal_apply_deadline?).and_return(false)

        condition.oc_working_time_1
        expect(condition.errors.added?(:apply_condition, :oc_working_time_1)).to be false
      end

      it 'does not add error when order_case is not over apply timeout (regular)' do
        allow(order_case).to receive(:is_urgent?).and_return(false)
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:regular_order_timeout_apply?).and_return(false)

        condition.oc_working_time_1
        expect(condition.errors.added?(:apply_condition, :oc_working_time_1)).to be false
      end
    end

    describe 'oc_working_time_2' do
      it 'adds error if order_case is non-regular and urgent' do
        allow(order_case).to receive(:is_urgent?).and_return(true)
        allow(order_case).to receive(:over_urgent_apply_deadline?).and_return(true)

        condition.oc_working_time_2
        expect(condition.errors.added?(:apply_condition, :oc_working_time_2)).to be true
      end

      it 'skips if order_case is regular' do
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order_case).to receive(:is_urgent?).and_return(true)
        allow(order_case).to receive(:over_urgent_apply_deadline?).and_return(true)

        condition.oc_working_time_2
        expect(condition.errors.added?(:apply_condition, :oc_working_time_2)).to be false
      end

      it 'skips if order_case is not urgent' do
        allow(order_case).to receive(:is_urgent?).and_return(false)
        allow(order_case).to receive(:over_urgent_apply_deadline?).and_return(true)

        condition.oc_working_time_2
        expect(condition.errors.added?(:apply_condition, :oc_working_time_2)).to be false
      end

      it 'does not add error if non-regular, urgent order_case is not over apply timeout' do
        allow(order_case).to receive(:is_urgent?).and_return(true)
        allow(order_case).to receive(:over_urgent_apply_deadline?).and_return(false)

        condition.oc_working_time_2
        expect(condition.errors.added?(:apply_condition, :oc_working_time_2)).to be false
      end
    end
  end

  context 'based on order_case public status' do
    describe 'oc_public_1' do
      it 'adds error when order_case is type_hidden but staff does not have offer' do
        allow(order_case).to receive(:type_hidden?).and_return(true)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(false)

        condition.oc_public_1
        expect(condition.errors.added?(:apply_condition, :oc_public_1)).to be true
      end

      it 'does not add error if order_case is not type_hidden' do
        allow(order_case).to receive(:type_hidden?).and_return(false)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(false)

        condition.oc_public_1
        expect(condition.errors.added?(:apply_condition, :oc_public_1)).to be false
      end

      it 'does not add error if order_case is type_hidden and staff has offer' do
        allow(order_case).to receive(:type_hidden?).and_return(true)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(true)

        condition.oc_public_1
        expect(condition.errors.added?(:apply_condition, :oc_public_1)).to be false
      end
    end

    describe 'oc_public_2' do
      it 'adds error when order_case is type_limited staff is excluded by oc_puclic_staff' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff, id: staff.id),
          is_public: false
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_2
        expect(condition.errors.added?(:apply_condition, :oc_public_2)).to be true
      end

      it 'skips validation when order_case is not type_limited' do
        allow(order_case).to receive(:type_limited?).and_return(false)
        condition.oc_public_2
        expect(condition.errors.added?(:apply_condition, :oc_public_2)).to be false
      end

      it 'does not add error if oc_public_staff does not exist' do
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_2
        expect(condition.errors.added?(:apply_condition, :oc_public_2)).to be false
      end

      it 'does not add error if order_case is type_limited and staff is included by oc_public_staff' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff, id: staff.id),
          is_public: true
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_2
        expect(condition.errors.added?(:apply_condition, :oc_public_2)).to be false
      end

      it 'does not add error if order_case is type_limited and excluded staff is not this staff' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff),
          is_public: true
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_2
        expect(condition.errors.added?(:apply_condition, :oc_public_2)).to be false
      end
    end

    describe 'oc_public_3' do
      it 'adds error when order_case is type_limited, no oc_public_staff, and staff has no offer' do
        allow(order_case).to receive(:type_limited?).and_return(true)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(false)
        condition.oc_public_3
        expect(condition.errors.added?(:apply_condition, :oc_public_3)).to be true
      end

      it 'does not add error when order_case is not type_limited' do
        allow(order_case).to receive(:type_limited?).and_return(false)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(true)
        condition.oc_public_3
        expect(condition.errors.added?(:apply_condition, :oc_public_3)).to be false
      end

      it 'does not add error when oc_public_staff exist' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff)
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(false)
        condition.oc_public_3
        expect(condition.errors.added?(:apply_condition, :oc_public_3)).to be false
      end

      it 'does not add error when staff has offer' do
        allow(order_case).to receive(:type_limited?).and_return(true)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(true)
        condition.oc_public_3
        expect(condition.errors.added?(:apply_condition, :oc_public_3)).to be false
      end
    end

    describe 'oc_public_4' do
      it 'adds error when order_case is type_hidden and staff has offer' do
        allow(order_case).to receive(:type_hidden?).and_return(true)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(true)
        condition.oc_public_4
        expect(condition.errors.added?(:apply_condition, :oc_public_4)).to be true
      end

      it 'does not add error when order_case is not type_hidden' do
        allow(order_case).to receive(:type_hidden?).and_return(false)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(true)
        condition.oc_public_4
        expect(condition.errors.added?(:apply_condition, :oc_public_4)).to be false
      end

      it 'does not add error when staff has no offer' do
        allow(order_case).to receive(:type_hidden?).and_return(true)
        allow(staff).to receive(:has_offer?).with(order_case.id).and_return(false)
        condition.oc_public_4
        expect(condition.errors.added?(:apply_condition, :oc_public_4)).to be false
      end
    end

    describe 'oc_public_5' do
      it 'skips validation when order_case is not type_limited' do
        allow(order_case).to receive(:type_limited?).and_return(false)
        condition.oc_public_5
        expect(condition.errors.added?(:apply_condition, :oc_public_5)).to be false
      end

      it 'adds error when order_case is type_limited and oc_public_staff is not public for staff' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff),
          is_public: true
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_5
        expect(condition.errors.added?(:apply_condition, :oc_public_5)).to be true
      end

      it 'does not add error when order_case is not oc_public_staff does not exist' do
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_5
        expect(condition.errors.added?(:apply_condition, :oc_public_5)).to be false
      end

      it 'does not add error when order_case is not oc_public_staff is not public' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff),
          is_public: false
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_5
        expect(condition.errors.added?(:apply_condition, :oc_public_5)).to be false
      end

      it 'does not add error when order_case is not oc_public_staff includes staff' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff, id: staff.id),
          is_public: true
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        condition.oc_public_5
        expect(condition.errors.added?(:apply_condition, :oc_public_5)).to be false
      end
    end

    describe 'oc_public_6' do
      it 'adds error when order_case is type_limited, staff has offer but no oc_public_staff' do
        allow(order_case).to receive(:type_limited?).and_return(true)
        allow(staff).to receive(:has_offer?).and_return(true)

        condition.oc_public_6
        expect(condition.errors.added?(:apply_condition, :oc_public_6)).to be true
      end

      it 'does not add error when order_case is not type_limited' do
        allow(order_case).to receive(:type_limited?).and_return(false)
        allow(staff).to receive(:has_offer?).and_return(true)

        condition.oc_public_6
        expect(condition.errors.added?(:apply_condition, :oc_public_6)).to be false
      end

      it 'does not add error when staff does not have offer' do
        allow(order_case).to receive(:type_limited?).and_return(true)
        allow(staff).to receive(:has_offer?).and_return(false)

        condition.oc_public_6
        expect(condition.errors.added?(:apply_condition, :oc_public_6)).to be false
      end

      it 'does not add error when oc_public_staff exists' do
        create(
          :order_case_public_staff,
          order_case: build_stubbed(:order_case, id: order_case.id),
          staff: build_stubbed(:staff, id: staff.id),
        )
        allow(order_case).to receive(:type_limited?).and_return(true)
        allow(staff).to receive(:has_offer?).and_return(true)

        condition.oc_public_6
        expect(condition.errors.added?(:apply_condition, :oc_public_6)).to be false
      end
    end
  end

  context 'based on staff status' do
    describe 'staff_status_1' do
      it 'skips validation when order is segment contract' do
        allow(order).to receive(:contract?).and_return(true)

        condition.staff_status_1
        expect(condition.errors.added?(:apply_condition, :staff_status_1_exp)).to be false
        expect(condition.errors.added?(:apply_condition, :staff_status_1_non_exp)).to be false
      end

      it 'skips validation when skip_staff_status_1 is true' do
        allow(order).to receive(:contract?).and_return(false)

        skip_cond = described_class.new(order_case, staff, skip_staff_status_1: true)
        skip_cond.staff_status_1
        expect(skip_cond.errors.added?(:apply_condition, :staff_status_1_exp)).to be false
        expect(skip_cond.errors.added?(:apply_condition, :staff_status_1_non_exp)).to be false
      end

      it 'skips validation when staff current_level is not before_debut' do
        allow(order).to receive(:contract?).and_return(false)
        allow(current_level).to receive(:before_debut?).and_return(false)

        condition.staff_status_1
        expect(condition.errors.added?(:apply_condition, :staff_status_1_exp)).to be false
        expect(condition.errors.added?(:apply_condition, :staff_status_1_non_exp)).to be false
      end

      it 'adds error for experienced staff when meet criteria' do
        allow(order).to receive(:contract?).and_return(false)
        allow(staff).to receive(:has_experience?).and_return(true)
        allow(current_level).to receive(:before_debut?).and_return(true)

        condition.staff_status_1
        expect(condition.errors.added?(:apply_condition, :staff_status_1_exp)).to be true
        expect(condition.errors.added?(:apply_condition, :staff_status_1_non_exp)).to be false
      end

      it 'adds error for non-experienced staff when meet criteria' do
        allow(order).to receive(:contract?).and_return(false)
        allow(staff).to receive(:has_experience?).and_return(false)
        allow(current_level).to receive(:before_debut?).and_return(true)

        condition.staff_status_1
        expect(condition.errors.added?(:apply_condition, :staff_status_1_exp)).to be false
        expect(condition.errors.added?(:apply_condition, :staff_status_1_non_exp)).to be true
      end

      it 'add error for experienced staff without current_level' do
        allow(order).to receive(:contract?).and_return(false)
        allow(staff).to receive(:current_level).and_return(nil)
        allow(staff).to receive(:has_experience?).and_return(true)

        condition.staff_status_1
        expect(condition.errors.added?(:apply_condition, :staff_status_1_exp)).to be true
        expect(condition.errors.added?(:apply_condition, :staff_status_1_non_exp)).to be false
      end

      it 'add error for non-experienced staff without current_level' do
        allow(order).to receive(:contract?).and_return(false)
        allow(staff).to receive(:current_level).and_return(nil)
        allow(staff).to receive(:has_experience?).and_return(false)

        condition.staff_status_1
        expect(condition.errors.added?(:apply_condition, :staff_status_1_exp)).to be false
        expect(condition.errors.added?(:apply_condition, :staff_status_1_non_exp)).to be true
      end
    end

    describe 'staff_status_4' do
      it 'skips validation when staff is exactly 18' do
        allow(staff).to receive(:age_by_time).with(order_case.case_started_at).and_return(18)

        condition.staff_status_4
        expect(condition.errors.added?(:apply_condition, :staff_status_4)).to be false
      end

      it 'skips validation when staff over 18' do
        condition.staff_status_4
        expect(condition.errors.added?(:apply_condition, :staff_status_4)).to be false
      end

      it 'skips validation when staff is under 18 but order_case is_time_changable' do
        allow(staff).to receive(:age_by_time).with(order_case.case_started_at).and_return(16)
        allow(order_case).to receive(:is_time_changable).and_return(true)

        condition.staff_status_4
        expect(condition.errors.added?(:apply_condition, :staff_status_4)).to be false
      end

      it 'skips validation when staff is under 18 but order_case is not night shift' do
        allow(staff).to receive(:age_by_time).with(order_case.case_started_at).and_return(16)
        allow(order_case).to receive(:is_time_changable).and_return(false)
        allow(order_case).to receive(:is_student_night_working?).and_return(false)

        condition.staff_status_4
        expect(condition.errors.added?(:apply_condition, :staff_status_4)).to be false
      end

      it 'adds error when staff is under 18, order_case night shift and is not changeable' do
        allow(staff).to receive(:age_by_time).with(order_case.case_started_at).and_return(16)
        allow(order_case).to receive(:is_time_changable).and_return(false)
        allow(order_case).to receive(:is_student_night_working?).and_return(true)

        condition.staff_status_4
        expect(condition.errors.added?(:apply_condition, :staff_status_4)).to be true
      end
    end

    describe 'staff_status_8' do
      it 'skips validation when order is not from lawson' do
        allow(order).to receive(:is_from_lawson?).and_return(false)

        condition.staff_status_8
        expect(condition.errors.added?(:apply_condition_contact_op, :staff_status_8)).to be false
      end

      it 'skips validation when order is from lawson but staff current_level is nil' do
        allow(order).to receive(:is_from_lawson?).and_return(true)
        allow(staff).to receive(:current_level).and_return(nil)

        condition.staff_status_8
        expect(condition.errors.added?(:apply_condition_contact_op, :staff_status_8)).to be false
      end

      it 'skips validation when order is from lawson but staff level is not not_required_training' do
        allow(order).to receive(:is_from_lawson?).and_return(true)
        allow(current_level).to receive(:not_require_training?).and_return(false)

        condition.staff_status_8
        expect(condition.errors.added?(:apply_condition_contact_op, :staff_status_8)).to be false
      end

      it 'skips validation when order is from lawson, staff level meets condition, but order is contract' do
        allow(order).to receive(:is_from_lawson?).and_return(true)
        allow(current_level).to receive(:not_require_training?).and_return(true)
        allow(order).to receive(:contract?).and_return(true)

        condition.staff_status_8
        expect(condition.errors.added?(:apply_condition_contact_op, :staff_status_8)).to be false
      end

      it 'skips validation when order and staff level meet conditions, but staff is not op_confirm' do
        allow(order).to receive(:is_from_lawson?).and_return(true)
        allow(order).to receive(:contract?).and_return(false)
        allow(current_level).to receive(:not_require_training?).and_return(true)
        allow(staff).to receive(:op_confirm?).and_return(false)

        condition.staff_status_8
        expect(condition.errors.added?(:apply_condition_contact_op, :staff_status_8)).to be false
      end

      it 'adds error when order is from lawson and staff meets conditions and order is not contract' do
        allow(order).to receive(:is_from_lawson?).and_return(true)
        allow(order).to receive(:contract?).and_return(false)
        allow(current_level).to receive(:not_require_training?).and_return(true)
        allow(staff).to receive(:op_confirm?).and_return(true)

        condition.staff_status_8
        expect(condition.errors.added?(:apply_condition_contact_op, :staff_status_8)).to be true
      end
    end

    describe 'staff_status_9' do
      it 'adds error when staff is type matchbox' do
        allow(staff).to receive(:matchbox?).and_return(true)

        condition.staff_status_9
        expect(condition.errors.added?(:apply_condition, :staff_status_9)).to be true
      end

      it 'does not add error when staff is not type matchbox' do
        allow(staff).to receive(:matchbox?).and_return(false)

        condition.staff_status_9
        expect(condition.errors.added?(:apply_condition, :staff_status_9)).to be false
      end
    end
  end

  context 'when staff has message from corporation_group/corporation' do
    let(:msg_by_corp) do
      double('StaffMessage', forbidden: nil, warning: nil)
    end

    let(:msg_by_corp_group) do
      double('StaffMessage', forbidden: nil, warning: nil)
    end

    before(:example) do
      by_corp_query = double('ByCorpQuery', perform: msg_by_corp)
      by_corp_group_query = double('ByCorpGroupQuery', perform: msg_by_corp_group)

      allow(StaffMessages::MessagesByTypeQuery).to receive(:new)
        .with(staff.id, :from_corporation, {corporation_ids: 1, working_date: order_case.case_started_at.to_date})
        .and_return(by_corp_query)

      allow(StaffMessages::MessagesByTypeQuery).to receive(:new)
        .with(staff.id, :from_corporation_group, {corporation_group_ids: 1, working_date: order_case.case_started_at.to_date})
        .and_return(by_corp_group_query)
    end

    describe 'oc_staff_1' do
      it 'skips validation when there is not forbidden by corporation or corporation_group' do
        condition.oc_staff_1
        expect(condition.errors.added?(:apply_condition, :oc_staff_1)).to be false
      end

      it 'adds error when staff is forbidden by the corporation' do
        allow(msg_by_corp).to receive(:forbidden).and_return(double)
        condition.oc_staff_1
        expect(condition.errors.added?(:apply_condition, :oc_staff_1)).to be true
      end

      it 'adds error when staff is forbidden by the corporation_group' do
        allow(msg_by_corp_group).to receive(:forbidden).and_return(double)
        condition.oc_staff_1
        expect(condition.errors.added?(:apply_condition, :oc_staff_1)).to be true
      end
    end

    describe 'oc_staff_2' do
      it 'skips validation when staff is not warned by corporation or corporation_group' do
        condition.oc_staff_2
        expect(condition.errors.added?(:apply_condition, :oc_staff_2)).to be false
      end

      it 'adds error when staff is warned by the corporation' do
        allow(msg_by_corp).to receive(:warning).and_return(double)
        condition.oc_staff_2
        expect(condition.errors.added?(:apply_condition, :oc_staff_2)).to be true
      end

      it 'adds error when staff is warned by the corporation_group' do
        allow(msg_by_corp_group).to receive(:warning).and_return(double)
        condition.oc_staff_2
        expect(condition.errors.added?(:apply_condition, :oc_staff_2)).to be true
      end
    end
  end

  context 'when staff has message from location' do
    let(:msg_by_loc) do
      double('StaffMessage', forbidden: nil, warning: nil)
    end

    before(:example) do
      by_loc_query = double('ByLocQuery', perform: msg_by_loc)

      allow(StaffMessages::MessagesByTypeQuery).to receive(:new)
        .with(staff.id, :from_location, {location_ids: 1, working_date: order_case.case_started_at.to_date})
        .and_return(by_loc_query)
    end

    describe 'oc_staff_3' do
      it 'adds error when staff is forbidden to work at the location' do
        allow(msg_by_loc).to receive(:forbidden).and_return(double)
        condition.oc_staff_3
        expect(condition.errors.added?(:apply_condition, :oc_staff_3)).to be true
      end

      it 'does not add error when staff is not forbidden to work at the location' do
        condition.oc_staff_3
        expect(condition.errors.added?(:apply_condition, :oc_staff_3)).to be false
      end
    end

    describe 'oc_staff_4' do
      it 'adds error when staff is warned by the location' do
        allow(msg_by_loc).to receive(:warning).and_return(double)
        condition.oc_staff_4
        expect(condition.errors.added?(:apply_condition, :oc_staff_4)).to be true
      end

      it 'does not add error when staff is not warned by location' do
        condition.oc_staff_4
        expect(condition.errors.added?(:apply_condition, :oc_staff_4)).to be false
      end
    end
  end

  context 'based on staff info and order_case working time' do
    describe 'oc_staff_6' do
      it 'adds error when staff is expired during working_time' do
        allow(staff).to receive(:[]).with("expired_start_date").and_return(order_case.case_started_at - 1.day)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(order_case.case_started_at + 1.day)
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be true
      end

      it 'adds error when staff is expired during working_time (only start_date)' do
        allow(staff).to receive(:[]).with("expired_start_date").and_return(order_case.case_started_at - 1.day)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(nil)
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be true
      end

      it 'adds error when staff is expired during working_time (only end_date)' do
        allow(staff).to receive(:[]).with("expired_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(order_case.case_started_at + 1.day)
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be true
      end

      it 'adds error when staff is expired during working_time (regular order)' do
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(order_case.case_started_at)
        allow(staff).to receive(:[]).with("expired_start_date").and_return(order_case.case_started_at - 1.day)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(order_case.case_started_at + 1.day)
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be true
      end

      it 'does not add error when staff does not have expired date' do
        allow(staff).to receive(:[]).with("expired_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(nil)
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be false
      end

      it 'does not add error when staff working date is before expired_start_date' do
        allow(staff).to receive(:[]).with("expired_start_date").and_return(order_case.case_started_at + 1.day)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(nil)
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be false
      end

      it 'does not add error when staff working date is after expired_end_date' do
        allow(staff).to receive(:[]).with("expired_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(order_case.case_started_at - 1.day)
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be false
      end

      it 'does not add error when working_date is undetermined' do
        allow(staff).to receive(:[]).with("expired_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("expired_end_date").and_return(nil)
        allow(order_case).to receive(:case_started_at).and_return("")
        condition.oc_staff_6
        expect(condition.errors.added?(:apply_condition, :oc_staff_6)).to be false
      end
    end

    describe 'oc_staff_7' do
      it 'adds error when staff is absent during working_time' do
        allow(staff).to receive(:[]).with("absence_start_date").and_return(order_case.case_started_at - 1.day)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(order_case.case_started_at + 1.day)
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be true
      end

      it 'adds error when staff is absent during working_time (only start_date)' do
        allow(staff).to receive(:[]).with("absence_start_date").and_return(order_case.case_started_at - 1.day)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(nil)
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be true
      end

      it 'adds error when staff is absent during working_time (only end_date)' do
        allow(staff).to receive(:[]).with("absence_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(order_case.case_started_at + 1.day)
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be true
      end

      it 'adds error when staff is absent during working_time (regular order)' do
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(order_case.case_started_at)
        allow(staff).to receive(:[]).with("absence_start_date").and_return(order_case.case_started_at - 1.day)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(order_case.case_started_at + 1.day)
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be true
      end

      it 'does not add error when staff does not have absence date' do
        allow(staff).to receive(:[]).with("absence_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(nil)
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be false
      end

      it 'does not add error when staff working date is before absence_start_date' do
        allow(staff).to receive(:[]).with("absence_start_date").and_return(order_case.case_started_at + 1.day)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(nil)
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be false
      end

      it 'does not add error when staff working date is after absence_end_date' do
        allow(staff).to receive(:[]).with("absence_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(order_case.case_started_at - 1.day)
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be false
      end

      it 'does not add error when working_date is undetermined' do
        allow(staff).to receive(:[]).with("absence_start_date").and_return(nil)
        allow(staff).to receive(:[]).with("absence_end_date").and_return(nil)
        allow(order_case).to receive(:case_started_at).and_return("")
        condition.oc_staff_7
        expect(condition.errors.added?(:apply_condition, :oc_staff_7)).to be false
      end
    end

    describe 'oc_staff_9' do
      it 'skips validation when staff is japanese nationality' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(false)
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be false
      end

      it 'skips validation when working_date is undetermined' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(true)
        allow(order_case).to receive(:case_started_at).and_return("")
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be false
      end

      it 'skips validation when working_date is undetermined (regular)' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(true)
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(nil)
        allow(staff).to receive(:residence_expiration_date).and_return(order_case.case_started_at - 1.day)
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be false
      end

      it 'skips validation when staff is foreigner and has no residence_expiration_date' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(true)
        allow(staff).to receive(:residence_expiration_date).and_return(nil)
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be false
      end

      it 'adds error when staff is foreigner and residence_exiration_date is before working_date' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(true)
        allow(staff).to receive(:residence_expiration_date).and_return(order_case.case_started_at - 1.day)
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be true
      end

      it 'adds error when staff is foreigner and residence_exiration_date is before working_date (regular)' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(true)
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(order_case.case_started_at)
        allow(staff).to receive(:residence_expiration_date).and_return(order_case.case_started_at - 1.day)
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be true
      end

      it 'does not add error when staff is foreigner and residence_exiration_date is same as working_date' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(true)
        allow(staff).to receive(:residence_expiration_date).and_return(order_case.case_started_at)
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be false
      end

      it 'does not add error when staff is foreigner and residence_exiration_date is after working_date' do
        allow(staff).to receive(:nationality_other_than_japan?).and_return(true)
        allow(staff).to receive(:residence_expiration_date).and_return(order_case.case_started_at + 12.months)
        condition.oc_staff_9
        expect(condition.errors.added?(:apply_condition, :oc_staff_9)).to be false
      end
    end

    describe 'oc_staff_10' do
      it 'skips validation when staff has no retirement_date' do
        allow(staff).to receive(:retirement_date).and_return(nil)
        condition.oc_staff_10
        expect(condition.errors.added?(:apply_condition, :oc_staff_10)).to be false
      end

      it 'skips validation when working_date is undetermined' do
        allow(staff).to receive(:retirement_date).and_return(order_case.case_started_at - 1.day)
        allow(order_case).to receive(:case_started_at).and_return("")
        condition.oc_staff_10
        expect(condition.errors.added?(:apply_condition, :oc_staff_10)).to be false
      end

      it 'skips validation when working_date is undetermined (regular)' do
        allow(staff).to receive(:retirement_date).and_return(order_case.case_started_at - 1.day)
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(nil)
        condition.oc_staff_10
        expect(condition.errors.added?(:apply_condition, :oc_staff_10)).to be false
      end

      it 'adds error when staff has retired' do
        allow(staff).to receive(:retirement_date).and_return(order_case.case_started_at - 1.day)
        condition.oc_staff_10
        expect(condition.errors.added?(:apply_condition, :oc_staff_10)).to be true
      end

      it 'adds error when staff has retired (regular)' do
        allow(staff).to receive(:retirement_date).and_return(order_case.case_started_at - 1.day)
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(order_case.case_started_at)
        condition.oc_staff_10
        expect(condition.errors.added?(:apply_condition, :oc_staff_10)).to be true
      end

      it 'does not add error when staff has not retired' do
        allow(staff).to receive(:retirement_date).and_return(order_case.case_started_at + 1.day)
        condition.oc_staff_10
        expect(condition.errors.added?(:apply_condition, :oc_staff_10)).to be false
      end
    end

    describe 'oc_staff_11' do
      context 'during auto-matching hours' do
        before(:example) do
          Timecop.freeze(Time.zone.local(2025, 5, 5, 21, 00, 00))
          allow(order_case).to receive(:case_started_at).and_return(Time.zone.local(2025, 5, 5, 23, 00, 00))
        end

        it 'skips validation when staff is nil' do
          cond = described_class.new(order_case, nil)
          cond.oc_staff_11
          expect(cond.errors.added?(:apply_condition, :oc_staff_11)).to be false
        end

        it 'skips validation when staff is not required_re_training' do
          allow(staff).to receive(:required_re_training?).and_return(false)
          condition.oc_staff_11
          expect(condition.errors.added?(:apply_condition, :oc_staff_11)).to be false
        end

        it 'adds error when staff is required_re_training' do
          allow(staff).to receive(:required_re_training?).and_return(true)
          condition.oc_staff_11
          expect(condition.errors.added?(:apply_condition, :oc_staff_11)).to be true
        end

        after(:example) do
          Timecop.return
        end
      end

      context 'not during auto-matching hours' do
        it 'skips validation' do
          allow(staff).to receive(:required_re_training?).and_return(true)
          condition.oc_staff_11
          expect(condition.errors.added?(:apply_condition, :oc_staff_11)).to be false
        end
      end
    end
  end

  context 'based on staff contract' do
    describe 'out_of_apply_contract' do
      context 'when not enough conditions to validate' do
        it 'skips validation if staff is not op_confirm' do
          allow(staff).to receive(:op_confirm?).and_return(false)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation if staff is not haken' do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(false)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation if staff has no current_level' do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(true)
          allow(staff).to receive(:current_level).and_return(nil)
          allow(order).to receive(:contract?).and_return(true)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation if staff is oboj' do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(true)
          allow(current_level).to receive(:oboj?).and_return(true)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation if order is contract' do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(true)
          allow(current_level).to receive(:oboj?).and_return(false)
          allow(order).to receive(:contract?).and_return(true)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation if order_case is segment_training' do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(true)
          allow(current_level).to receive(:oboj?).and_return(false)
          allow(order).to receive(:contract?).and_return(false)
          allow(order_case).to receive(:segment_trainning).and_return(true)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation if staff has valid last_contract' do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(true)
          allow(current_level).to receive(:oboj?).and_return(false)
          allow(order).to receive(:contract?).and_return(false)
          allow(order_case).to receive(:segment_trainning).and_return(false)
          allow(staff).to receive(:last_contract).and_return(double("Contract", is_day_contract: true))
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end
      end

      context 'when working_date is undetermined' do
        before(:example) do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(true)
          allow(current_level).to receive(:oboj?).and_return(false)
          allow(order).to receive(:contract?).and_return(false)
          allow(order_case).to receive(:segment_trainning).and_return(false)
          allow(staff).to receive(:last_contract).and_return(double("Contract", is_day_contract: false))
        end

        it 'skips validation (non-regular)' do
          allow(order_case).to receive(:case_started_at).and_return("")
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation (regular)' do
          allow(order_case).to receive(:regular_order?).and_return(true)
          allow(order).to receive(:last_started_at).and_return(nil)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end
      end

      context 'when working_date can be checked against contract' do
        before(:example) do
          allow(staff).to receive(:op_confirm?).and_return(true)
          allow(staff).to receive(:haken?).and_return(true)
          allow(current_level).to receive(:oboj?).and_return(false)
          allow(order).to receive(:contract?).and_return(false)
          allow(order_case).to receive(:segment_trainning).and_return(false)
          allow(staff).to receive(:last_contract).and_return(nil)
        end

        it 'skips validation when staff has contract during working_date' do
          allow(staff).to receive(:contract_history_by_date).with(order_case.case_started_at.to_date)
            .and_return(double)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'skips validation when staff has_contract during apply time' do
          allow(staff).to receive(:contract_history_by_date).with(order_case.case_started_at.to_date)
            .and_return(nil)
          allow(staff).to receive(:contract_history_by_date).with(ServerTime.now.to_date)
            .and_return(double)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be false
        end

        it 'adds error when staff has no contract' do
          allow(staff).to receive(:contract_history_by_date).with(order_case.case_started_at.to_date)
            .and_return(nil)
          allow(staff).to receive(:contract_history_by_date).with(ServerTime.now.to_date)
            .and_return(nil)
          condition.out_of_apply_contract
          expect(condition.errors.added?(:apply_condition_contact_op, :out_of_apply_contract)).to be true
        end
      end
    end
  end

  context 'based on apply time/deadline' do
    describe 'two_hours_before_deadline' do
      before(:context) do
        Timecop.freeze
      end

      it 'skips validation when working_date is undetermined' do
        allow(order_case).to receive(:case_started_at).and_return(nil)
        condition.two_hours_before_deadline
        expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline)).to be false
      end

      it 'skips validation when working_date is undetermined (regular)' do
        allow(order_case).to receive(:regular_order?).and_return(true)
        allow(order).to receive(:last_started_at).and_return(nil)
        condition.two_hours_before_deadline
        expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline)).to be false
      end

      it 'does not add error when now < deadline' do
        allow(order_case).to receive(:case_started_at).and_return(ServerTime.now + 1.day)
        condition.two_hours_before_deadline
        expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline)).to be false
      end

      it 'does not add error when now = deadline' do
        allow(order_case).to receive(:case_started_at).and_return(ServerTime.now + 2.hours)
        condition.two_hours_before_deadline
        expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline)).to be false
      end

      it 'does not add error when now > started_at' do
        allow(order_case).to receive(:case_started_at).and_return(ServerTime.now - 1.day)
        condition.two_hours_before_deadline
        expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline)).to be false
      end

      it 'does not add error when now = started_at' do
        allow(order_case).to receive(:case_started_at).and_return(ServerTime.now)
        condition.two_hours_before_deadline
        expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline)).to be false
      end

      it 'adds error when apply within two hour of order_case' do
        allow(order_case).to receive(:case_started_at).and_return(ServerTime.now + 1.hour)
        condition.two_hours_before_deadline
        expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline)).to be true
      end

      after(:context) do
        Timecop.return
      end
    end

    describe 'two_hours_before_deadline_with_urget_job_in_auto_matching' do
      context 'during auto-matching hours' do
        before(:example) do
          Timecop.freeze(Time.zone.local(2025, 5, 5, 21, 00, 00))
          allow(order_case).to receive(:case_started_at).and_return(Time.zone.local(2025, 5, 5, 23, 00, 00))
        end

        it 'skips validation when order_case is not urgent' do
          allow(order_case).to receive(:is_urgent?).and_return(false)

          condition.two_hours_before_deadline_with_urget_job_in_auto_matching
          expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline))
            .to be false
        end

        it 'adds error when order_case is_urgent and over timeout limit (non-regular)' do
          allow(order_case).to receive(:is_urgent?).and_return(true)
          allow(order_case).to receive(:over_normal_apply_deadline?).and_return(true)

          condition.two_hours_before_deadline_with_urget_job_in_auto_matching
          expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline))
            .to be true
        end

        it 'adds error when order_case is_urgent and over timeout limit (regular)' do
          allow(order_case).to receive(:is_urgent?).and_return(true)
          allow(order_case).to receive(:regular_order?).and_return(true)
          allow(order).to receive(:regular_order_timeout_apply?).and_return(true)

          condition.two_hours_before_deadline_with_urget_job_in_auto_matching
          expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline))
            .to be true
        end

        it 'does not add error when order_case is not over timeout limit (non-regular)' do
          allow(order_case).to receive(:is_urgent?).and_return(true)
          allow(order_case).to receive(:over_normal_apply_deadline?).and_return(false)

          condition.two_hours_before_deadline_with_urget_job_in_auto_matching
          expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline))
            .to be false
        end

        it 'does not add error when order_case is not over timeout limit (regular)' do
          allow(order_case).to receive(:is_urgent?).and_return(true)
          allow(order_case).to receive(:regular_order?).and_return(true)
          allow(order).to receive(:regular_order_timeout_apply?).and_return(false)

          condition.two_hours_before_deadline_with_urget_job_in_auto_matching
          expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline))
            .to be false
        end

        after(:example) do
          Timecop.return
        end
      end

      context 'not during auto-matching hours' do
        it 'skips validation because condition not met' do
          allow(order_case).to receive(:is_urgent?).and_return(true)

          condition.two_hours_before_deadline_with_urget_job_in_auto_matching
          expect(condition.errors.added?(:apply_condition_contact_op, :two_hours_before_deadline))
            .to be false
        end
      end
    end

    describe 'reject_auto_matching_if_time_changed' do
      it 'skips validation when skip_reject_auto_matching_if_time_changed is set to true' do
        skip_cond = described_class.new(order_case, staff, skip_reject_auto_matching_if_time_changed: true)
        skip_cond.reject_auto_matching_if_time_changed
        expect(skip_cond.errors.added?(:apply_condition, :reject_auto_matching_with_oc_is_time_changeable))
          .to be false
      end

      context 'not during auto-matching hours' do
        it 'skips validation when it is not during auto-matching time' do
          condition.reject_auto_matching_if_time_changed
          expect(condition.errors.added?(:apply_condition, :reject_auto_matching_with_oc_is_time_changeable))
            .to be false
        end
      end

      context 'during auto-matching hours (before midnight)' do
        before(:example) do
          Timecop.freeze(Time.zone.local(2025, 5, 5, 21, 00, 00))
          allow(order_case).to receive(:case_started_at).and_return(Time.zone.local(2025, 5, 5, 23, 00, 00).in_time_zone)
        end

        it 'skips validation when order_case is not changeable' do
          allow(order_case).to receive(:is_time_changable).and_return(false)
          condition.reject_auto_matching_if_time_changed
          expect(condition.errors.added?(:apply_condition, :reject_auto_matching_with_oc_is_time_changeable))
            .to be false
        end

        it 'adds error when order_case is_time_changable' do
          allow(order_case).to receive(:is_time_changable).and_return(true)

          condition.reject_auto_matching_if_time_changed
          expect(condition.errors.added?(:apply_condition, :reject_auto_matching_with_oc_is_time_changeable))
            .to be true
        end

        after(:example) do
          Timecop.return
        end
      end

      context 'during auto-matching hours (after midnight)' do
        before(:example) do
          Timecop.freeze(Time.zone.local(2025, 5, 5, 1, 0, 0))
          allow(order_case).to receive(:case_started_at).and_return(Time.zone.local(2025, 5, 5, 6, 0, 0).in_time_zone)
        end

        it 'skips validation when order_case is not changeable' do
          allow(order_case).to receive(:is_time_changable).and_return(false)

          condition.reject_auto_matching_if_time_changed
          expect(condition.errors.added?(:apply_condition, :reject_auto_matching_with_oc_is_time_changeable))
            .to be false
        end

        it 'adds error when order_case is_time_changable' do
          allow(order_case).to receive(:is_time_changable).and_return(true)

          condition.reject_auto_matching_if_time_changed
          expect(condition.errors.added?(:apply_condition, :reject_auto_matching_with_oc_is_time_changeable))
            .to be true
        end

        after(:example) do
          Timecop.return
        end
      end
    end
  end

  context 'based on staff work experience' do
    describe 'staff_work_exp_2' do
      let(:arranged_staff) do
        build(:staff)
      end

      it 'add error when staff is arranged during order_case working time' do
        oc_scope = double()
        allow(OrderCase).to receive(:by_ids).and_return(oc_scope)
        allow(oc_scope).to receive(:not_cancel).and_return([double('OrderCase', id: 1)])

        cond = described_class.new(order_case, arranged_staff)
        cond.staff_work_exp_2
        expect(cond.errors.added?(:apply_condition, :staff_work_exp_2))
          .to be true
      end

      it 'when staff is not arranged' do
        allow_any_instance_of(described_class).to receive(:is_arranged_in_time?)
          .and_return(false)
        condition.staff_work_exp_2
        expect(condition.errors.added?(:apply_condition, :staff_work_exp_2))
          .to be false
      end
    end
  end

  context 'when apply is valid' do
    let(:valid_order_case) do
      valid_order = instance_double(
        'Order', id: 1,
        corporation_id: 1,
        corporation_group_id: 1,
        is_from_lawson?: true,
        contract?: false
      )
      instance_double(
        'OrderCase', id: 1,
        order: valid_order,
        cancel?: false,
        full_arranged?: false,
        is_urgent?: false,
        regular_order?: false,
        over_normal_apply_deadline?: false,
        type_hidden?: false,
        type_limited?: false,
        corporation_id: 1,
        location_id: 1,
        case_started_at: ServerTime.now + 3.days,
        case_ended_at: ServerTime.now + 3.days + 4.hours,
        segment_trainning: false
      )
    end

    let(:valid_staff) do
      staff_level = instance_double(
        'StaffLevel',
        before_debut?: false,
        not_require_training?: false,
        oboj?: false
      )
      instance_double(
        'Staff', id: 1,
        current_level: staff_level,
        age_by_time: 21,
        op_confirm?: true,
        matchbox?: false,
        nationality_other_than_japan?: false,
        retirement_date: nil,
        required_re_training?: false,
        haken?: true,
        last_contract: double('StaffContract', is_day_contract: true)
      )
    end

    before(:example) do
      allow(valid_staff).to receive(:applied_order_case?).with(valid_order_case.id).and_return(false)
      allow(valid_staff).to receive(:arranged?).with(valid_order_case.id).and_return(false)
      allow(valid_staff).to receive(:age_by_time).with(valid_order_case.case_started_at).and_return(21)
      allow(valid_staff).to receive(:[]).with("expired_start_date").and_return(nil)
      allow(valid_staff).to receive(:[]).with("expired_end_date").and_return(nil)
      allow(valid_staff).to receive(:[]).with("absence_start_date").and_return(nil)
      allow(valid_staff).to receive(:[]).with("absence_end_date").and_return(nil)
      allow_any_instance_of(described_class).to receive(:is_arranged_in_time?).and_return(false)
    end

    describe 'valid?' do
      it 'returns true when pass all apply conditions' do
        cond = described_class.new(valid_order_case, valid_staff)
        expect(cond).to be_valid
        expect(cond.errors).to be_empty
      end
    end
  end
end
