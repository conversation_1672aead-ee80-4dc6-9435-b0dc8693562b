require 'rails_helper'

RSpec.describe ApplicationRecord, type: :model do
  let(:test_record) { Class.new(ApplicationRecord) { self.table_name = 'accounts' }.new }

  describe '#rollback_log' do
    before do
      allow(test_record).to receive(:logger).and_return(double('Logger', info: nil))
    end

    context 'when in development environment' do
      before do
        allow(Rails.env).to receive(:development?).and_return(true)
      end

      it 'prints errors using ap' do
        test_record.errors.add(:name, "can't be blank")
        expect(test_record).to receive(:puts).with(test_record.errors)
        test_record.rollback_log
      end
    end

    context 'when not in development environment' do
      before do
        allow(Rails.env).to receive(:development?).and_return(false)
      end

      it 'does not print errors using ap' do
        test_record.errors.add(:name, "can't be blank")
        expect(test_record).not_to receive(:puts)
        test_record.rollback_log
      end
    end

    it 'logs error messages' do
      test_record.errors.add(:name, "can't be blank")
      test_record.errors.add(:email, 'is invalid')

      expected_message = "ROLLBACK_LOG: #{test_record.errors.messages}"
      expect(test_record.logger).to receive(:info).with(expected_message)
      test_record.rollback_log
    end

    it 'handles nil errors gracefully' do
      allow(test_record).to receive(:errors).and_return(nil)
      expect { test_record.rollback_log }.not_to raise_error
    end
  end

  describe 'callbacks' do
    it 'calls rollback_log after rollback' do
      expect(test_record).to receive(:rollback_log)
      test_record.run_callbacks(:rollback)
    end
  end
end
