require 'rails_helper'

RSpec.describe PrefectureSalaryRange, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:prefecture_id) }
    it { should validate_presence_of(:min) }
    it { should validate_presence_of(:max) }
  end

  describe 'Scopes' do
    let!(:prefecture_salary_range1) { create(:prefecture_salary_range, prefecture_id: 1) }
    let!(:prefecture_salary_range2) { create(:prefecture_salary_range, prefecture_id: 2) }

    describe '.by_prefecture_id' do
      it 'returns salary ranges for the specified prefecture_id' do
        expect(described_class.by_prefecture_id(1)).to include(prefecture_salary_range1)
        expect(described_class.by_prefecture_id(1)).not_to include(prefecture_salary_range2)
      end
    end
  end
end
