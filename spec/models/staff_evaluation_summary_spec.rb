require 'rails_helper'

RSpec.describe StaffEvaluationSummary, type: :model do
  describe 'Associations' do
    it { should belong_to(:staff) }
    it { should belong_to(:chain) }
  end

  describe 'Acts as paranoid' do
    let(:staff_evaluation_summary) { create(:staff_evaluation_summary) }

    it 'destroys the record' do
      staff_evaluation_summary.destroy
      expect(StaffEvaluationSummary.find_by(id: staff_evaluation_summary.id)).to be_nil
      expect(StaffEvaluationSummary.with_deleted.find_by(id: staff_evaluation_summary.id)).to eq(staff_evaluation_summary)
    end

    it 'restores the record' do
      staff_evaluation_summary.destroy
      staff_evaluation_summary.restore
      expect(StaffEvaluationSummary.find_by(id: staff_evaluation_summary.id)).to eq(staff_evaluation_summary)
    end
  end

  describe 'Scopes' do
    let!(:older_summary) { create(:staff_evaluation_summary, updated_at: 1.day.ago) }
    let!(:newer_summary) { create(:staff_evaluation_summary, updated_at: Time.current) }

    it 'orders by updated_at in descending order' do
      expect(StaffEvaluationSummary.last_updated).to eq([newer_summary, older_summary])
    end
  end
end
