require 'rails_helper'

RSpec.describe TrainingScheduleApplicant, type: :model do
  describe 'Associations' do
    it { should belong_to(:staff) }
    it { expect(described_class.joins(:staff).to_sql)
      .not_to include('`staffs`.`deleted_at`') }

    it { should belong_to(:training_schedule) }
    it { expect(described_class.joins(:training_schedule).to_sql)
      .not_to include('`training_schedules`.`deleted_at`') }
  end

  describe 'Validations' do
    it { should validate_presence_of(:staff) }
  end

  describe 'Enums' do
    it { should define_enum_for(:schedule_status_code)
      .with_values(booked: 0, joined: 1, absent_with_notice: 2, absent_without_notice: 3) }
  end

  describe 'Delegations' do
    let(:training_schedule) { create(:training_schedule) }
    let(:training_schedule_applicant) { create(:training_schedule_applicant, training_schedule: training_schedule) }

    it { should delegate_method(:formatted_datetime).to(:training_schedule) }
    it { should delegate_method(:training_date_weekday).to(:training_schedule) }
    it { should delegate_method(:location).to(:training_schedule) }
    it { should delegate_method(:formatted_absent_deadline).to(:training_schedule) }
    it { should delegate_method(:able_to_absent?).to(:training_schedule) }
    it { should delegate_method(:location_name).to(:training_schedule) }
    it { should delegate_method(:location_id).to(:training_schedule) }
    it { should delegate_method(:training_session_code).to(:training_schedule) }
    it { should delegate_method(:working_date).to(:training_schedule) }
    it { should delegate_method(:start_time_text).to(:training_schedule) }
    it { should delegate_method(:end_time_text).to(:training_schedule) }
    it { should delegate_method(:remove_other_schedules_and_notify_trainer).to(:training_schedule) }
    it { should delegate_method(:person_in_charge_email).to(:training_schedule) }
    it { should delegate_method(:person_in_charge_name).to(:training_schedule) }
    it { should delegate_method(:location_prefecture_name).to(:training_schedule) }
    it { should delegate_method(:name).to(:staff).with_prefix }
    it { should delegate_method(:account_email).to(:staff).with_prefix }
    it { should delegate_method(:account_name_kana).to(:staff).with_prefix }
    it { should delegate_method(:total_work_experience).to(:staff).with_prefix }
    it { should delegate_method(:evaluation).to(:staff).with_prefix }
    it { should delegate_method(:home_tel).to(:staff).with_prefix }
    it { should delegate_method(:tel).to(:staff).with_prefix }
    it { should delegate_method(:email).to(:staff).with_prefix }
    it { should delegate_method(:age).to(:staff).with_prefix }
    it { should delegate_method(:uniform_size).to(:staff).with_prefix }
    it { should delegate_method(:gender_id).to(:staff).with_prefix }
    it { should delegate_method(:display_workable_time).to(:staff).with_prefix }
    it { should delegate_method(:is_working_car).to(:staff).with_prefix }
    it { should delegate_method(:level_up_training).to(:staff).with_prefix }
    it { should delegate_method(:account_name_kana_to_half_size).to(:staff).with_prefix }
    it { should delegate_method(:staff_number).to(:staff) }
  end

  describe 'Callbacks' do
    describe 'before_update' do
      let(:training_schedule_applicant) { create(:training_schedule_applicant, schedule_status_code: :booked, cancel_reason: 'Some reason') }

      it 'does not clear cancel_reason if schedule_status_code is not changed' do
        training_schedule_applicant.update!(schedule_status_code: :booked)

        expect(training_schedule_applicant.cancel_reason).to eq('Some reason')
      end

      it 'clears cancel_reason if schedule_status_code changes' do
        training_schedule_applicant.update!(schedule_status_code: :joined)

        expect(training_schedule_applicant.cancel_reason).to be_nil
      end

      it 'does not clear cancel_reason when schedule_status_code is absent_with_notice' do
        training_schedule_applicant.update!(schedule_status_code: :absent_with_notice)

        expect(training_schedule_applicant.cancel_reason).to eq('Some reason')
      end

      it 'does not clear cancel_reason when schedule_status_code is absent_without_notice' do
        training_schedule_applicant.update!(schedule_status_code: :absent_without_notice)

        expect(training_schedule_applicant.cancel_reason).to eq('Some reason')
      end
    end

    describe 'after_save' do
      let(:training_schedule) { create(:training_schedule, total_portion: 1, is_full: false) }
      let(:training_schedule_applicant) { build(:training_schedule_applicant, training_schedule: training_schedule) }

      it 'update is_full on schedule to true when total portion equal to valid applicants count' do
        training_schedule_applicant.schedule_status_code = [:booked, :joined].sample
        training_schedule_applicant.save

        expect(training_schedule.is_full).to be_truthy
      end

      it 'update is_full on schedule is false when total portion is greater than valid applicants count' do
        training_schedule_applicant.schedule_status_code = :absent_with_notice
        training_schedule_applicant.save

        expect(training_schedule.is_full).to be_falsey
      end

      it 'raises error when total valid applicants count is greater than total portion' do
        training_schedule.update_columns(total_portion: 0)

        training_schedule_applicant.schedule_status_code = :booked
        expect { training_schedule_applicant.save! }.to raise_error(ActiveRecord::RecordInvalid)
      end

      it 'raises error when training schedule is nil' do
        training_schedule_applicant = create(:training_schedule_applicant, training_schedule: training_schedule)

        allow(training_schedule_applicant).to receive(:training_schedule).and_return(nil)

        expect { training_schedule_applicant.run_callbacks(:save) }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end
  end

  describe 'Scopes' do
    describe '.by_staff_id' do
      it 'returns applicants for given staff ids' do
        staff1 = create(:staff)
        staff2 = create(:staff)
        applicant1 = create(:training_schedule_applicant, staff: staff1)
        applicant2 = create(:training_schedule_applicant, staff: staff2)
        expect(described_class.by_staff_id([staff1.id])).to include(applicant1)
        expect(described_class.by_staff_id([staff1.id])).not_to include(applicant2)
      end
    end

    describe '.by_schedule_id' do
      it 'returns applicants for given schedule ids' do
        schedule1 = create(:training_schedule)
        schedule2 = create(:training_schedule)
        applicant1 = create(:training_schedule_applicant, training_schedule: schedule1)
        applicant2 = create(:training_schedule_applicant, training_schedule: schedule2)
        expect(described_class.by_schedule_id([schedule1.id])).to include(applicant1)
        expect(described_class.by_schedule_id([schedule1.id])).not_to include(applicant2)
      end
    end

    describe '.first_sessions' do
      it 'returns applicants for first sessions' do
        schedule1 = create(:training_schedule, training_session_code: :training_first_round)
        schedule2 = create(:training_schedule, training_session_code: :training_second_round)
        applicant1 = create(:training_schedule_applicant, training_schedule: schedule1)
        applicant2 = create(:training_schedule_applicant, training_schedule: schedule2)
        expect(described_class.first_sessions).to include(applicant1)
        expect(described_class.first_sessions).not_to include(applicant2)
      end
    end

    describe '.second_sessions' do
      it 'returns applicants for second sessions' do
        schedule1 = create(:training_schedule, training_session_code: :training_first_round)
        schedule2 = create(:training_schedule, training_session_code: :training_second_round)
        applicant1 = create(:training_schedule_applicant, training_schedule: schedule1)
        applicant2 = create(:training_schedule_applicant, training_schedule: schedule2)
        expect(described_class.second_sessions).to include(applicant2)
        expect(described_class.second_sessions).not_to include(applicant1)
      end
    end

    describe '.single_sessions' do
      it 'returns applicants for single sessions' do
        schedule1 = create(:training_schedule, training_session_code: :training_first_round)
        schedule2 = create(:training_schedule, training_session_code: :single_session)
        applicant1 = create(:training_schedule_applicant, training_schedule: schedule1)
        applicant2 = create(:training_schedule_applicant, training_schedule: schedule2)
        expect(described_class.single_sessions).to include(applicant2)
        expect(described_class.single_sessions).not_to include(applicant1)
      end
    end

    describe '.upcoming_trainings' do
      it 'returns upcoming trainings' do
        schedule1 = create(:training_schedule, start_time: 1.day.from_now)
        schedule2 = create(:training_schedule, start_time: 1.day.ago)
        applicant1 = create(:training_schedule_applicant, training_schedule: schedule1, schedule_status_code: :booked)
        applicant2 = create(:training_schedule_applicant, training_schedule: schedule2, schedule_status_code: :booked)
        expect(described_class.upcoming_trainings(1.hour.from_now, 2.days.from_now)).to include(applicant1)
        expect(described_class.upcoming_trainings(1.hour.from_now, 2.days.from_now)).not_to include(applicant2)
      end
    end

    describe '.not_absent' do
      it 'returns applicants who are not absent' do
        applicant1 = create(:training_schedule_applicant, schedule_status_code: :booked)
        applicant2 = create(:training_schedule_applicant, schedule_status_code: :absent_with_notice)
        expect(described_class.not_absent).to include(applicant1)
        expect(described_class.not_absent).not_to include(applicant2)
      end
    end
  end

  describe 'Instance methods' do
    let(:staff) { create(:staff) }

    describe '#is_unavailable?' do
      it 'returns true if schedule_status_code is absent_with_notice or absent_without_notice' do
        applicant1 = create(:training_schedule_applicant, schedule_status_code: :absent_with_notice)
        applicant2 = create(:training_schedule_applicant, schedule_status_code: :absent_without_notice)
        applicant3 = create(:training_schedule_applicant, schedule_status_code: :booked)
        expect(applicant1.is_unavailable?).to be_truthy
        expect(applicant2.is_unavailable?).to be_truthy
        expect(applicant3.is_unavailable?).to be_falsey
      end
    end

    describe '#send_new_schedule_email' do
      let(:applicant) { create(:training_schedule_applicant, staff: staff) }

      it 'sends new schedule email to staff' do
        expect(StaffMailer).to receive(:notify_staff_new_training_schedule)
          .with(
            staff,
            applicant.training_schedule.training_time_with_day,
            staff.account_name,
            applicant.location_name,
            applicant.training_schedule.location_formatted_full_address
          ).and_call_original

        applicant.send_new_schedule_email
      end
    end

    describe '#send_new_schedule_sms' do
      let(:applicant) { create(:training_schedule_applicant, staff: staff) }

      it 'sends new schedule sms to staff' do
        expect(MessageSenderService).to receive(:notify_staff_new_training_schedule)
          .with(
            staff.account.tel,
            datetime: applicant.training_schedule.training_time_with_day,
            user_name: staff.account_name,
            location_name: applicant.location_name,
            location_full_address: applicant.training_schedule.location.formatted_full_address
          ).and_call_original

        applicant.send_new_schedule_sms
      end

      it 'does not raise error when account is nil' do
        allow(staff).to receive(:account).and_return(nil)

        expect { applicant.send_new_schedule_sms }.not_to raise_error
      end

      it 'does not raise error when location is nil' do
        allow(applicant.training_schedule).to receive(:location).and_return(nil)

        expect { applicant.send_new_schedule_sms }.not_to raise_error
      end
    end

    describe '#send_fcm_to_staff' do
      let(:applicant) { create(:training_schedule_applicant, staff: staff) }

      it 'sends fcm notification to staff' do
        expect(AppSendNotificationWorker).to receive(:perform_async)
          .with(
            [{
              staff_id: staff.id,
              creator_type: :admin,
              notification_type: 'notification_type',
              creator_id: 1,
              params: {
                location_name: applicant.location_name,
                job_date_time: applicant.formatted_datetime
              }
            }.as_json]
          ).and_call_original

        applicant.send_fcm_to_staff(1, 'notification_type')
      end

      it 'sends fcm notification to staff by system when admin_id is nil' do
        expect(AppSendNotificationWorker).to receive(:perform_async)
          .with(
            [{
              staff_id: staff.id,
              creator_type: :by_system,
              notification_type: 'notification_type',
              params: {
                location_name: applicant.location_name,
                job_date_time: applicant.formatted_datetime
              }
            }.as_json]
          ).and_call_original

        applicant.send_fcm_to_staff(nil, 'notification_type')
      end
    end
  end

  describe 'Class methods' do
    describe '.not_absent_by_schedule_ids' do
      it 'returns applicants who are not absent by schedule ids' do
        schedule1 = create(:training_schedule)
        schedule2 = create(:training_schedule)
        applicant1 = create(:training_schedule_applicant, training_schedule: schedule1, schedule_status_code: :booked)
        applicant2 = create(:training_schedule_applicant, training_schedule: schedule1, schedule_status_code: :absent_with_notice)
        applicant3 = create(:training_schedule_applicant, training_schedule: schedule2, schedule_status_code: :booked)
        result = described_class.not_absent_by_schedule_ids([schedule1.id, schedule2.id])

        expect(result[schedule1.id]).to include(applicant1)
        expect(result[schedule1.id]).not_to include(applicant2)
        expect(result[schedule2.id]).to include(applicant3)
      end

      it 'returns empty hash when schedule ids is blank' do
        result = described_class.not_absent_by_schedule_ids([])

        expect(result).to eq({})
      end
    end

    describe '.get_booked_applicant_by_staff_id' do
      let(:time) { Time.zone.now }
      let(:staff1) { create(:staff) }
      let(:staff2) { create(:staff) }
      let(:schedule1) { create(:training_schedule, training_session_code: :training_first_round, start_time: time + 1.day) }
      let(:schedule2) { create(:training_schedule, training_session_code: :training_second_round, start_time: time + 2.days) }

      before do
        create(:training_schedule_applicant, staff: staff1, training_schedule: schedule1, schedule_status_code: :booked)
        create(:training_schedule_applicant, staff: staff2, training_schedule: schedule2, schedule_status_code: :booked)
      end

      it 'returns booked applicants by staff ids' do
        result = described_class.get_booked_applicant_by_staff_id([staff1.id, staff2.id])

        expect(result[:first_session][staff1.id]).to eq(schedule1.start_time.strftime(Settings.datetime.formats))
        expect(result[:second_session][staff2.id]).to eq(schedule2.start_time.strftime(Settings.datetime.formats))
      end
    end
  end

  describe 'Acts as paranoid' do
    it 'soft deletes the record' do
      applicant = create(:training_schedule_applicant)
      applicant.destroy
      expect(described_class.find_by(id: applicant.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: applicant.id)).to eq(applicant)
    end

    it 'restores the record' do
      applicant = create(:training_schedule_applicant)
      applicant.destroy
      applicant.restore
      expect(described_class.find_by(id: applicant.id)).to eq(applicant)
    end
  end

  describe 'Concerns' do
    it 'includes TrainingScheduleDecorator' do
      expect(described_class.included_modules).to include(TrainingScheduleApplicantDecorator)
    end

    it_behaves_like 'TrainingScheduleApplicantDecorator'
  end
end
