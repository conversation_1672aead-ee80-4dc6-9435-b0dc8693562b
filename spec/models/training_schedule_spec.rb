require 'rails_helper'

RSpec.describe TrainingSchedule, type: :model do
  subject { FactoryBot.build(:training_schedule) }

  describe 'Constants' do
    it 'has UNSPECIFIED_OPTION constant' do
      expect(described_class::UNSPECIFIED_OPTION).to eq('-1')
    end

    it 'has INCLUDE_MODELS constant' do
      expect(described_class::INCLUDE_MODELS).to eq([:location, { person_in_charge: :account }])
    end

    it 'has RESTORE_PARAMS constant' do
      expect(described_class::RESTORE_PARAMS).to eq({
        deleted_by: nil,
        deleted_at: nil,
        is_full: false
      })
    end

    it 'has UNAVAILABLE_RESTORE_SESSION_CODE constant' do
      expect(described_class::UNAVAILABLE_RESTORE_SESSION_CODE).to eq(%w(training_first_round training_second_round))
    end
  end

  describe 'Associations' do
    it { is_expected.to belong_to(:location) }
    it { is_expected.to belong_to(:creator).class_name('Admin').optional }
    it { is_expected.to belong_to(:person_in_charge).class_name('Admin').optional }
    it { is_expected.to belong_to(:training_schedule).class_name('TrainingSchedule').optional }
    it { is_expected.to have_many(:training_schedule_applicants).dependent(:destroy) }
  end

  describe 'Enums' do
    it { is_expected.to define_enum_for(:training_session_code)
      .with_values(training_first_round: 1, training_second_round: 2, single_session: 3) }
  end

  describe 'Validations' do
    let(:admin) { create(:admin, id: subject.person_in_charge_id) }
    let(:location) { create(:location, id: subject.location_id) }
    let(:same_trainer_schedule) do
      create(:training_schedule, start_time: subject.start_time, person_in_charge_id: admin.id)
    end

    let(:same_location_and_session_schedule) do
      create(:training_schedule, start_time: subject.start_time, location_id: location.id,
        training_session_code: subject.training_session_code)
    end

    it { is_expected.to be_valid }

    it 'should validate person_in_charge_id presence on create' do
      subject.person_in_charge_id = nil
      expect(subject).not_to be_valid
      expect(subject.errors[:person_in_charge_id])
        .to include(I18n.t('admin.training_schedule.errors.invalid_person_in_charge'))
    end

    it 'should validate start_time presence on create' do
      subject.start_time = nil
      expect(subject).not_to be_valid
      expect(subject.errors.details[:start_time]).to include(a_hash_including(error: :blank))
    end

    it 'should validate end_time presence on create' do
      subject.end_time = nil
      expect(subject).not_to be_valid
      expect(subject.errors.details[:end_time]).to include(a_hash_including(error: :blank))
    end

    it 'should validate start_time less than end_time on create' do
      subject.start_time = subject.end_time

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.start_time_less_than_end_time'))
    end

    it 'should validate trainer has another schedule that has applicants' do
      allow(TrainingScheduleApplicant).to receive(:not_absent_by_schedule_ids)
        .with([same_trainer_schedule.id])
        .and_return({same_trainer_schedule.id => [build(:training_schedule_applicant)]})

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.same_date_and_trainer'))
    end

    it 'should validate same location and session has applicants' do
      allow(TrainingScheduleApplicant).to receive(:not_absent_by_schedule_ids)
        .with([same_location_and_session_schedule.id])
        .and_return({same_location_and_session_schedule.id => [build(:training_schedule_applicant)]})

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.same_date_location_and_session'))
    end

    context 'when updating person in charge' do
      let(:other_schedule) do
        create(:training_schedule, person_in_charge: create(:admin), start_time: subject.start_time)
      end

      before do
        subject.save!
      end

      it 'should validate trainer has another schedule that has applicants' do
        allow(TrainingScheduleApplicant).to receive(:not_absent_by_schedule_ids)
          .with([other_schedule.id])
          .and_return({other_schedule.id => [build(:training_schedule_applicant)]})

        expect(subject.update(person_in_charge_id: other_schedule.person_in_charge_id)).to be_falsey
        expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.same_date_and_trainer'))
      end
    end
  end

  describe 'Delegations' do
    it { should delegate_method(:name).to(:location).with_prefix.allow_nil }
    it { should delegate_method(:formatted_full_address).to(:location).with_prefix.allow_nil }
    it { should delegate_method(:prefecture_name).to(:location).with_prefix.allow_nil }
    it { should delegate_method(:prefecture_id).to(:location).with_prefix.allow_nil }
    it { should delegate_method(:stations_1_name).to(:location).with_prefix.allow_nil }

    it { should delegate_method(:name).to(:person_in_charge).with_prefix.allow_nil }
    it { should delegate_method(:email).to(:person_in_charge).with_prefix.allow_nil }
  end

  describe 'Callbacks' do
    describe 'after_create' do
      it 'creates admin action log' do
        expect(TrainingScheduleLoggingWorker)
          .to receive(:perform_async)
          .with('admin', {'admin_id' => subject.creator_id, 'target_id' => anything, 'action_type' => 'create'})
          .and_call_original

        subject.save!
      end

      it 'does not create admin action log if creator_id is blank' do
        subject.creator_id = nil
        expect(TrainingScheduleLoggingWorker).not_to receive(:perform_async)

        subject.save!
      end
    end
  end

  describe 'Scopes' do
    let!(:training_schedule1) do
      create(:training_schedule, start_time: Time.zone.parse('2025-01-01 10:00:00'), is_full: false)
    end

    let!(:training_schedule2) do
      create(:training_schedule, start_time: Time.zone.parse('2025-01-02 12:00:00'), is_full: true)
    end

    describe '.by_location' do
      it { expect(described_class.by_location(training_schedule1.location_id))
        .to contain_exactly(training_schedule1) }
    end

    describe '.by_person_in_charge' do
      let!(:training_schedule3) do
        create(:training_schedule, :skip_callback, start_time: Time.zone.parse('2025-01-02 12:00:00'),
          is_full: true, person_in_charge_id: nil)
      end

      it { expect(described_class.by_person_in_charge(training_schedule1.person_in_charge_id))
        .to contain_exactly(training_schedule1) }

      it { expect(described_class.by_person_in_charge("-1")).to contain_exactly(training_schedule3) }
    end

    describe '.start_after' do
      it { expect(described_class.start_after(Time.zone.parse('2025-01-01 12:00:00')))
        .to contain_exactly(training_schedule2) }
    end

    describe '.start_before' do
      it { expect(described_class.start_before(Time.zone.parse('2025-01-01 12:00:00')))
        .to contain_exactly(training_schedule1) }
    end

    describe '.available' do
      it { expect(described_class.available).to contain_exactly(training_schedule1) }
    end

    describe '.by_start_time' do
      it { expect(described_class.by_start_time(Time.zone.parse('2025-01-01 12:00:00')))
        .to contain_exactly(training_schedule2) }
    end

    describe '.between_dates' do
      it { expect(described_class.between_dates(Time.zone.parse('2025-01-01 10:00:00'),
        Time.zone.parse('2025-01-02 11:59:59'))).to contain_exactly(training_schedule1) }
    end

    describe '.deleted_by_schedule' do
      let!(:deleted_schedule) { create(:training_schedule, deleted_by: training_schedule1.id, deleted_at: Time.current) }

      it { expect(described_class.deleted_by_schedule(training_schedule1.id)).to contain_exactly(deleted_schedule) }
      it { expect(described_class.deleted_by_schedule(training_schedule2.id)).to be_empty }
    end
  end

  describe 'Instance methods' do
    let(:training_schedule) { build(:training_schedule) }

    describe '#has_training_time_passed?' do
      it 'returns true if training time has passed' do
        training_schedule.end_time = 1.second.ago
        expect(training_schedule.has_training_time_passed?).to be true
      end

      it 'returns false if training time has not passed' do
        training_schedule.end_time = 1.second.from_now
        expect(training_schedule.has_training_time_passed?).to be false
      end
    end

    describe '#absent_deadline' do
      context 'when the start time is before 12:00' do
        it 'returns the absent deadline is 12:00 of 2 days before the start time' do
          training_schedule.start_time = Time.zone.parse('2025-01-03 10:00:00')

          expect(training_schedule.absent_deadline)
            .to eq(Time.zone.parse('2025-01-01 12:00:00'))
        end
      end

      context 'when the start time is after 12:00' do
        it 'returns the absent deadline is 12:00 of 2 days before the start time' do
          training_schedule.start_time = Time.zone.parse('2025-01-03 16:00:00')

          expect(training_schedule.absent_deadline)
            .to eq(Time.zone.parse('2025-01-01 12:00:00'))
        end
      end
    end

    describe '#able_to_absent?' do
      before do
        allow(ServerTime).to receive(:now).and_return(Time.zone.parse('2025-01-01 12:00:00'))
      end

      it 'should returns true when within absent deadline' do
        allow(training_schedule).to receive(:absent_deadline).and_return(Time.zone.parse('2025-01-01 11:59:59'))

        expect(training_schedule.able_to_absent?).to be false
      end

      it 'should returns false when out of absent deadline' do
        allow(training_schedule).to receive(:absent_deadline).and_return(Time.zone.parse('2025-01-01 12:00:01'))

        expect(training_schedule.able_to_absent?).to be true
      end
    end

    describe '#can_change_person_in_charge?' do
      it 'returns true if person in charge is blank' do
        training_schedule.person_in_charge_id = nil

        expect(training_schedule.can_change_person_in_charge?(anything)).to be true
      end

      it 'returns true if person in charge is current admin' do
        training_schedule.person_in_charge_id = 1

        expect(training_schedule.can_change_person_in_charge?(1)).to be true
      end

      it 'returns false if person in charge is not current admin' do
        training_schedule.person_in_charge_id = 1

        expect(training_schedule.can_change_person_in_charge?(2)).to be false
      end
    end

    describe '#assign_is_full' do
      let(:training_schedule) { build(:training_schedule, is_full: false) }
      let(:training_schedule_applicants) { build_list(:training_schedule_applicant, 2) }

      before do
        allow(training_schedule).to receive_message_chain(:training_schedule_applicants, :not_absent)
          .and_return(training_schedule_applicants)
      end

      it 'assigns is_full to true if valid applicants count is greater than or equal to total portion' do
        training_schedule.total_portion = 2

        expect { training_schedule.assign_is_full }.to change(training_schedule, :is_full).from(false).to(true)
      end

      it 'assigns is_full to false if valid applicants count is less than total portion' do
        training_schedule.total_portion = 3

        expect { training_schedule.assign_is_full }.not_to change(training_schedule, :is_full)
        expect(training_schedule.is_full).to be false
      end
    end

    describe '#applied_staff_ids' do
      let(:training_schedule_applicant) { build(:training_schedule_applicant, staff_id: 1) }

      before do
        allow(training_schedule).to receive_message_chain(:training_schedule_applicants, :booked)
          .and_return([training_schedule_applicant])
      end

      it 'returns booked staff ids through training schedule applicants' do
        expect(training_schedule.applied_staff_ids).to contain_exactly(1)
      end
    end

    describe '#remove_other_schedules_and_notify_trainer' do
      let(:training_schedule) { create(:training_schedule) }
      let(:related_training_schedule) { create(:training_schedule) }

      before do
        allow(training_schedule).to receive(:get_related_schedules)
          .and_return(described_class.where(id: related_training_schedule.id))
      end

      it 'should soft removes related schedules' do
        training_schedule.remove_other_schedules_and_notify_trainer

        expect(described_class.find_by(id: related_training_schedule.id)).to be_nil
        expect(described_class.with_deleted.find_by(id: related_training_schedule.id)).to eq(related_training_schedule)
      end

      it 'should notify trainer about staff booked' do
        expect(TrainingSchedules::SendNotificationToTrainersWorker).to receive(:perform_async)
          .with(training_schedule.id, "booked", anything)
          .and_call_original

        training_schedule.remove_other_schedules_and_notify_trainer
      end

      it 'should create action log' do
        expect(TrainingScheduleLoggingWorker).to receive(:perform_async)
          .with("admin", {"admin_id" => nil, "target_id" => related_training_schedule.id, "action_type" => "auto_delete"})
          .and_call_original

        training_schedule.remove_other_schedules_and_notify_trainer
      end

      it 'does not delete related schedules if there are no any related schedules' do
        training_schedule = create(:training_schedule)
        training_schedule.remove_other_schedules_and_notify_trainer

        expect(TrainingScheduleLoggingWorker).not_to receive(:perform_async)
      end
    end

    describe '#get_related_schedules' do
      let(:location) { create(:location) }
      let!(:training_schedule) do
        create(:training_schedule, start_time: Time.zone.parse('2025-01-01 10:00:00'), location_id: location.id)
      end
      let!(:same_traininer_schedule) do
        create(:training_schedule,
          start_time: Time.zone.parse('2025-01-01 08:00:00'),
          person_in_charge_id: training_schedule.person_in_charge_id)
      end

      let!(:same_location_and_session_schedule) do
        create(:training_schedule,
          start_time: Time.zone.parse('2025-01-01 12:00:00'),
          location_id: location.id,
          training_session_code: training_schedule.training_session_code)
      end

      let!(:same_traininer_schedule_diff_date) do
        create(:training_schedule,
          start_time: Time.zone.parse('2025-01-02 08:00:00'),
          person_in_charge_id: training_schedule.person_in_charge_id,
          location_id: location.id,
          training_session_code: training_schedule.training_session_code)
      end

      it 'should return schedules with same trainer or location and session in same date, exclude itself' do
        expect(training_schedule.get_related_schedules)
          .to contain_exactly(same_traininer_schedule, same_location_and_session_schedule)
      end
    end

    describe '#get_histories' do
      let!(:training_schedule) { create(:training_schedule) }
      let!(:admin_log) do
        create(:admin_action_log,
          target_object_type: 'TrainingSchedule',
          target_object_id: training_schedule.id)
      end

      let!(:staff_log) do
        create(:staff_action_log,
          target_object_type: 'TrainingSchedule',
          target_object_id: training_schedule.id)
      end

      it 'returns histories' do
        expect(training_schedule.get_histories).to contain_exactly(admin_log, staff_log)
      end
    end
  end

  describe 'Class methods' do
    describe '.get_by_search_params' do
      let(:location) { create(:location) }

      before do
        create_list(:training_schedule, 2,
          start_time: Time.zone.parse('2025-01-01 10:00:00'),
          training_session_code: 3, person_in_charge_id: 2)

        create(:training_schedule, start_time: Time.zone.parse('2025-01-05 10:00:00'), training_session_code: 2,
          location_id: location.id)
      end

      it 'returns training schedules group by time and date' do
        results = described_class.get_by_search_params(
          Date.parse('2025-01-01').beginning_of_day..Date.parse('2025-01-05').end_of_day,
          nil,
          nil,
          nil
        )

        expect(results).to match(
          "10:00" => {
            "01/01" => [a_kind_of(Hash), a_kind_of(Hash)],
            "01/05" => [a_kind_of(Hash)]
          }
        )
      end

      it 'should return specific training with provided location IDs' do
        results = described_class.get_by_search_params(
          Date.parse('2025-01-01').beginning_of_day..Date.parse('2025-01-05').end_of_day,
          "#{location.id}",
          nil,
          nil
        )

        expect(results).to match(
          "10:00" => {
            "01/05" => [a_kind_of(Hash)]
          }
        )
      end

      it 'should return specific training with provided training session code' do
        results = described_class.get_by_search_params(
          Date.parse('2025-01-01').beginning_of_day..Date.parse('2025-01-05').end_of_day,
          nil,
          "single_session",
          nil
        )

        expect(results).to match(
          "10:00" => {
            "01/01" => [a_kind_of(Hash), a_kind_of(Hash)]
          }
        )
      end

      it 'should return specific training with provided person in charge ID' do
        results = described_class.get_by_search_params(
          Date.parse('2025-01-01').beginning_of_day..Date.parse('2025-01-05').end_of_day,
          nil,
          nil,
          2
        )

        expect(results).to match(
          "10:00" => {
            "01/01" => [a_kind_of(Hash), a_kind_of(Hash)]
          }
        )
      end
    end

    describe '.available_schedules_by_location' do
      let(:location) { create(:location) }
      let!(:training_schedule) do
        create(:training_schedule,
          location_id: location.id,
          start_time: 25.hours.from_now,
          is_full: false,
          training_session_code: "single_session")
      end

      it 'returns available schedules by location' do
        result = described_class.available_schedules_by_location(location.id)

        expect(result[:single_session_schedules]).to contain_exactly({
          'id' => training_schedule.id,
          'datetime' => training_schedule.formatted_datetime
        })
      end

      it 'does not include full training' do
        create(:training_schedule,
          location_id: location.id,
          start_time: 25.hours.from_now,
          is_full: true,
          training_session_code: "single_session")

        result = described_class.available_schedules_by_location(location.id)
        expect(result[:single_session_schedules]).to contain_exactly({
          'id' => training_schedule.id,
          'datetime' => training_schedule.formatted_datetime
        })
      end

      it 'should include full training if it is current training' do
        full_training = create(:training_schedule,
          location_id: location.id,
          start_time: 25.hours.from_now,
          is_full: true,
          training_session_code: "single_session")

        result = described_class.available_schedules_by_location(location.id, full_training.id)

        expect(result[:single_session_schedules]).to contain_exactly(
          {
            'id' => training_schedule.id,
            'datetime' => training_schedule.formatted_datetime
          },
          {
            'id' => full_training.id,
            'datetime' => full_training.formatted_datetime
          }
        )
      end
    end
  end

  describe 'Acts as paranoid' do
    it 'destroys the record' do
      training_schedule = create(:training_schedule)
      training_schedule.destroy
      expect(described_class.find_by(id: training_schedule.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: training_schedule.id)).to eq(training_schedule)
    end

    it 'restores the record' do
      training_schedule = create(:training_schedule)
      training_schedule.destroy
      training_schedule.restore
      expect(described_class.find_by(id: training_schedule.id)).to eq(training_schedule)
    end
  end

  describe 'Concerns' do
    it 'includes TrainingScheduleDecorator' do
      expect(described_class.included_modules).to include(TrainingScheduleDecorator)
    end

    it_behaves_like 'TrainingScheduleDecorator'
  end
end
