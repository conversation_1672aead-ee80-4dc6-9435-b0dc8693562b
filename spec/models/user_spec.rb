require 'rails_helper'

RSpec.describe User, type: :model do
  let(:user) { create(:user) }
  let(:corporation) { create(:corporation) }
  let(:account) { create(:account) }

  describe 'Constants' do
    it { expect(described_class::USER_DEFAULT_START_DATE).to eq(Date.current) }
    it { expect(described_class::USER_ATTRIBUTES).to include(:corporation_id, :role_id, :started_at, :avatar, :remove_avatar) }
    it { expect(described_class::OWNER_ATTRIBUTES).to include(:corporation_id, :role_id, :started_at, :avatar, :remove_avatar) }
    it { expect(described_class::CHANGE_FIELDS).to eq([:role_id, :corporation_id]) }
    it { expect(described_class::ROLE_CAN_APPROVE).to eq([1, 2, 3]) }
    it { expect(described_class::CAN_SURVEY_ROLES).to eq(%w(owner system_admin approver)) }
    it { expect(described_class::ADMINISTRATOR_ROLES).to eq(%w(owner system_admin)) }
  end

  describe 'Enums' do
    it { should define_enum_for(:role_id).with_values(owner: 1, system_admin: 2, approver: 3, normal: 4) }
  end

  describe 'Associations' do
    it { should belong_to(:account).optional }
    it { should belong_to(:corporation) }
    it { should have_many(:user_corporation_groups).dependent(:destroy) }
    it { should have_many(:corporation_groups).through(:user_corporation_groups) }
    it { should have_many(:user_locations).dependent(:destroy) }
    it { should have_many(:locations).through(:user_locations) }
    it { should have_many(:location_surveys) }
    it { should have_many(:user_order_search_conditions).dependent(:destroy) }
    it { should have_many(:user_search_conditions).dependent(:destroy) }
    it { should have_many(:user_order_case_search_conditions).dependent(:destroy) }
    it { should have_many(:user_arrange_billing_search_conditions).dependent(:destroy) }
    it { should have_many(:organizations).through(:corporation_groups) }
    it { should have_many(:owner_notification_users).dependent(:destroy) }
    it { should have_many(:user_groups).dependent(:destroy) }
    it { should have_many(:corporation_group_tags).through(:user_groups) }
    it { should have_many(:user_haken_destination_search_conditions).dependent(:destroy) }
  end

  describe 'Validations' do
    subject { FactoryBot.build(:user) }

    it { should validate_presence_of(:corporation_id) }
    it { should validate_presence_of(:role_id) }
    it { should validate_presence_of(:account_id) }
    it { should validate_presence_of(:started_at) }
    it { should validate_uniqueness_of(:account_id) }

    it 'should reject invalid date for started_at' do
      subject.started_at = 'invalid_date'

      expect(subject).not_to be_valid
      expect(subject.errors.details[:started_at]).to include(a_hash_including(error: :invalid_date_format))
    end

    context 'when option require_avatar is true' do
      subject { build(:user, require_avatar: true) }

      it { should validate_presence_of(:avatar) }
    end

    context 'when option on_corporation_user is true' do
      subject { build(:user, on_corporation_user: true) }

      it { should validate_presence_of(:user_locations) }
    end

    describe '#change_corporation_group' do
      subject { user }

      before do
        allow(Order).to receive(:updated_by_user)
          .with(subject.id)
          .and_return(double('Order', exists?: true))
      end

      it 'should reject if any order was updated by this user' do
        subject.corporation_id = create(:corporation).id

        expect(subject).not_to be_valid
        expect(subject.errors.details[:corporation_id])
          .to include(a_hash_including(error: :can_not_change_corporation_group))
      end
    end

    describe '#check_rule_system_administrator' do
      subject { FactoryBot.build(:user, role_id: [:approver, :normal].sample, corporation: corporation) }

      it 'should reject when corporation does not have administrator' do
        expect(subject).not_to be_valid
        expect(subject.errors.details[:role_id])
          .to include(a_hash_including(error: :not_have_administrator))
      end

      it 'should accept when corporation already has administrator' do
        create(:user, role_id: described_class::ADMINISTRATOR_ROLES.sample, corporation: corporation)

        expect(subject).to be_valid
      end

      it 'does not validate if role is not approver or normal' do
        described_class.role_ids.except(:approver, :normal).keys.each do |role_id|
          subject.role_id = role_id
          expect(subject).to be_valid
        end
      end
    end
  end

  describe 'Callbacks' do
    describe 'after_save' do
      let(:user) { build(:user, corporation: corporation, role_id: :owner) }

      describe '#add_all_location_for_owner' do
        let!(:locations) do
          create_list(:location, 2, corporation_group: create(:corporation_group, corporation: corporation))
        end

        context 'when user role is owner' do
          it 'adds locations data by corporation locations on create' do
            expect{ user.save! }.to change { user.locations.ids }.from([]).to(locations.map(&:id))
          end

          it 'adds new locations data if corporation ID is changed' do
            user.save!

            other_corporation = create(:corporation)
            new_location = create(:location,
              corporation_group: create(:corporation_group, corporation: other_corporation))
            user.corporation_id = other_corporation.id
            user.save!

            expect(user.locations).to include(new_location)
          end
        end

        context 'when user role is not owner' do
          let(:user) { build(:user, corporation: corporation, role_id: :system_admin) }

          it 'does not add locations data' do
            expect { user.save! }.not_to change { user.locations.ids }
          end
        end
      end

      describe '#add_all_group_tag_for_owner' do
        let!(:corporation_groups) do
          create_list(:corporation_group, 2,
            corporation: corporation, corporation_group_tag: create(:corporation_group_tag, corporation: corporation))
        end

        context 'when user role is owner' do
          it 'adds corporation group tags data by corporation group tags on create' do
            expect{ user.save! }.to change { user.corporation_group_tags.ids }.from([])
              .to(corporation_groups.map(&:corporation_group_tag_id))
          end

          it 'adds new corporation group tags data if corporation ID is changed' do
            user.save!

            other_corporation = create(:corporation)
            new_corporation_group_tag = create(:corporation_group_tag, corporation: other_corporation)
            FactoryBot.create(:corporation_group,
              corporation: other_corporation, corporation_group_tag: new_corporation_group_tag)
            user.corporation_id = other_corporation.id
            user.save!

            expect(user.corporation_group_tags).to include(new_corporation_group_tag)
          end

          it 'does not add corporation group tags data if corporation group is not found' do
            user.save!

            other_corporation = create(:corporation)
            new_corporation_group_tag = create(:corporation_group_tag, corporation: other_corporation)

            FactoryBot.create(:corporation_group, corporation: other_corporation,
              corporation_group_tag: create(:corporation_group_tag, corporation: corporation))

            user.corporation_id = other_corporation.id
            user.save!

            expect(user.corporation_group_tags).not_to include(new_corporation_group_tag)
          end
        end

        context 'when user role is not owner' do
          let(:user) { build(:user, corporation: corporation, role_id: :system_admin) }

          it 'does not add locations data' do
            expect { user.save! }.not_to change { user.corporation_group_tags.ids }
          end
        end
      end

      describe '#add_all_corporation_group_for_owner' do
        let!(:corporation_groups) { create_list(:corporation_group, 2, corporation: corporation) }

        context 'when user role is owner' do
          it 'adds corporation group data on create' do
            expect{ user.save! }.to change { user.corporation_groups.ids }.from([]).to(corporation_groups.map(&:id))
          end

          it 'adds new corporation group data if corporation ID is changed' do
            user.save!

            other_corporation = create(:corporation)
            new_corporation_group = create(:corporation_group, corporation: other_corporation)

            user.corporation_id = other_corporation.id
            user.save!

            expect(user.corporation_groups).to include(new_corporation_group)
          end
        end

        context 'when user role is not owner' do
          let(:user) { build(:user, corporation: corporation, role_id: :system_admin) }

          it 'does not add locations data' do
            expect { user.save! }.not_to change { user.corporation_groups.ids }
          end
        end
      end
    end

    describe 'after_update' do
      describe '#delete_all_association_user' do
        let(:user) { create(:user, corporation: corporation) }
        let(:new_corporation) { create(:corporation) }

        before do
          FactoryBot.create(:location, corporation_group: create(:corporation_group, corporation: corporation,
            corporation_group_tag: create(:corporation_group_tag, corporation: corporation)))
        end

        context 'when corporation_id changes and no orders exist' do
          before do
            allow(Order).to receive_message_chain(:updated_by_user, :exists?).and_return(false)
          end

          it 'deletes user associations' do
            expect { user.update(corporation_id: new_corporation.id) }
              .to change { user.locations.count }.from(1).to(0)
              .and change { user.corporation_groups.count }.from(1).to(0)
              .and change { user.corporation_group_tags.count }.from(1).to(0)
          end
        end

        context 'when orders exist for user' do
          before do
            allow(Order).to receive_message_chain(:updated_by_user, :exists?).and_return(true)
          end

          it 'does not delete user associations' do
            expect{ user.update(corporation_id: create(:corporation).id) }
              .not_to change { [user.locations, user.corporation_groups, user.corporation_group_tags] }
          end
        end

        context 'when no orders exist but corporation_id is not changed' do
          before do
            allow(Order).to receive_message_chain(:updated_by_user, :exists?).and_return(false)
          end

          it 'does not delete user associations' do
            expect{ user.update(started_at: 1.day.from_now) }
              .not_to change { [user.locations, user.corporation_groups, user.corporation_group_tags] }
          end
        end
      end
    end
  end

  describe 'Delegations' do
    it { should delegate_method(:full_name).to(:corporation).with_prefix(true) }
    it { should delegate_method(:email).to(:account).allow_nil }
    it { should delegate_method(:name).to(:account).allow_nil }
    it { should delegate_method(:name_kana).to(:account).allow_nil }
  end

  describe 'Scopes' do
    let(:corporation) { create(:corporation) }
    let(:corporation_2) { create(:corporation) }
    let(:corporation_group) { create(:corporation_group, corporation: corporation) }
    let(:corporation_group_2) { create(:corporation_group, corporation: corporation_2) }
    let(:location) { create(:location, corporation_group: corporation_group) }
    let(:location_2) { create(:location, corporation_group: corporation_group_2) }

    describe '.by_corporation_id' do
      let!(:user) { create(:user, corporation: corporation) }
      let!(:user_2) { create(:user, corporation: corporation_2) }

      it 'returns users by corporation_id' do
        expect(described_class.by_corporation_id(corporation.id)).to contain_exactly(user)
      end
    end

    describe '.by_email' do
      let!(:user) { create(:user, account: create(:account, email: '<EMAIL>')) }
      let!(:user_2) { create(:user, account: create(:account, email: '<EMAIL>')) }

      it 'returns users by email' do
        expect(described_class.by_email('<EMAIL>')).to contain_exactly(user)
      end
    end

    describe '.by_location_id' do
      let!(:user) { create(:user, corporation: location.corporation_group.corporation) }
      let!(:user_2) { create(:user, corporation: location_2.corporation_group.corporation) }

      it 'returns users by location_id' do
        expect(described_class.by_location_id(location.id)).to contain_exactly(user)
      end
    end

    describe '.of_lawson' do
      before do
        %i(is_lawson is_not_lawson).each do |is_lawson|
          create(:user, corporation: create(:corporation, is_lawson))
        end
      end

      it 'returns users of lawson' do
        expect(described_class.of_lawson)
          .to all(satisfy("is lawson corporation") { |user| user.corporation.is_lawson? })
      end
    end

    describe '.outside_lawson' do
      before do
        %i(is_lawson is_not_lawson).each do |is_lawson|
          create(:user, corporation: create(:corporation, is_lawson))
        end
      end

      it 'returns users outside lawson' do
        expect(described_class.outside_lawson)
          .to all(satisfy("is not lawson corporation") { |user| !user.corporation.is_lawson? })
      end
    end

    describe '.can_approve_notification' do
      before do
        described_class.role_ids.keys.each do |role_id|
          create(:user, :skip_callback, role_id: role_id, corporation: location.corporation_group.corporation)
          create(:user, :skip_callback, role_id: role_id, corporation: location_2.corporation_group.corporation)
        end
      end

      it 'returns users can approve notification on specific location' do
        results = described_class.can_approve_notification(location.id)

        expect(results).to all(have_attributes(role_id: be_in(%w(owner system_admin approver))))
        expect(results).to all(have_attributes(locations: [location]))
      end
    end

    describe '.by_role_can_approve' do
      before do
        described_class.role_ids.keys.each do |role_id|
          create(:user, :skip_callback, role_id: role_id)
        end
      end

      it 'returns users by role_can_approve' do
        results = described_class.by_role_can_approve

        expect(results).to all(have_attributes(role_id: be_in(%w(owner system_admin approver))))
      end
    end

    describe '.by_role_can_survey' do
      before do
        create(:user, corporation: location.corporation_group.corporation)
        create(:user, corporation: location_2.corporation_group.corporation)
      end

      it 'returns users by specific location' do
        results = described_class.by_role_can_survey(location.id)

        expect(results).to all(have_attributes(locations: [location]))
      end
    end

    describe '.sort_by_created_at' do
      before do
        create(:user, id: 1, created_at: 1.day.ago)
        create(:user, id: 2, created_at: 1.hour.ago)
      end

      it 'returns users sorted by created_at in descending order' do
        expect(described_class.sort_by_created_at).to eq([described_class.find(2), described_class.find(1)])
      end
    end

    describe '.user_search_by_name' do
      let(:user) { create(:user, account: create(:account, name: 'name')) }
      let(:user_2) { create(:user, account: create(:account, name: 'name_2')) }
      let(:user_3) { create(:user, account: create(:account, name_kana: 'name_kana_1', name: 'name_3')) }

      it 'returns users that have account name is like the keyword' do
        expect(described_class.user_search_by_name({name: 'name'})).to contain_exactly(user, user_2, user_3)
        expect(described_class.user_search_by_name({name: '2'})).to contain_exactly(user_2)
        expect(described_class.user_search_by_name({name: '1'})).to be_empty
      end

      it 'returns users that have account name_kana is like the keyword' do
        expect(described_class.user_search_by_name({name_kana: 'name'})).to contain_exactly(user_3)
      end

      it 'returns users that have account name and name_kana are like the keyword if both are present' do
        expect(described_class.user_search_by_name({name: 'name', name_kana: 'name'})).to contain_exactly(user_3)
      end

      it 'does not apply filter when params is not have name or name_kana' do
        expect(described_class.user_search_by_name({corporation_id: 1})).to contain_exactly(user, user_2, user_3)
      end
    end

    describe '.search_by_name_or_name_kana' do
      let(:user) { create(:user, account: create(:account, name: 'name')) }
      let(:user_2) { create(:user, account: create(:account, name: 'name_2')) }
      let(:user_3) { create(:user, account: create(:account, name_kana: 'name_kana_1')) }

      it 'returns users that have account name is like the keyword' do
        expect(described_class.search_by_name_or_name_kana('name')).to contain_exactly(user, user_2, user_3)
        expect(described_class.search_by_name_or_name_kana('2')).to contain_exactly(user_2)
        expect(described_class.search_by_name_or_name_kana('name_kana_2')).to be_empty
      end
    end

    describe '.by_ids' do
      let(:user) { create(:user) }
      let!(:user_2) { create(:user) }


      it { expect(described_class.by_ids(user.id)).to contain_exactly(user) }
      it { expect(described_class.by_ids([user.id, user_2.id])).to contain_exactly(user, user_2) }
      it { expect(described_class.by_ids(['invalid_id'])).to be_empty }
    end

    describe '.by_corporation' do
      let!(:user) { create(:user, corporation: corporation) }
      let!(:user_2) { create(:user, corporation: corporation_2) }

      it { expect(described_class.by_corporation(corporation.id)).to contain_exactly(user) }
    end

    describe '.by_not_corporations' do
      let!(:user) { create(:user, corporation: corporation) }
      let!(:user_2) { create(:user, corporation: corporation_2) }
      let!(:user_3) { create(:user, corporation: create(:corporation)) }

      it { expect(described_class.by_not_corporations(corporation.id)).to contain_exactly(user_2, user_3) }
    end

    describe '.without_current_user' do
      let!(:user) { create(:user) }
      let!(:user_2) { create(:user) }
      let!(:user_3) { create(:user) }

      it { expect(described_class.without_current_user(user.id)).to contain_exactly(user_2, user_3) }
    end

    describe '.by_departments' do
      let!(:user) { create(:user, corporation: corporation) }
      let!(:user_2) { create(:user, corporation: corporation_2) }

      it { expect(described_class.by_departments([corporation.pic_department_id])).to contain_exactly(user) }
    end

    describe '.batch_daily_job' do
      before do
        category_ids = [106, 107]
        type_no = [20, 60, 80]

        category_ids.each do |category_id|
          type_no.each do |type_no|
            category = FactoryBot.build_stubbed(:type_category, id: category_id)
            type = FactoryBot.create(:type, category: category, type_no: type_no)
            FactoryBot.create(:user, corporation: create(:corporation, transaction_status_id: type.id))
          end
        end
      end

      it 'returns users that are not have   category 107 and type_no 20 or 60' do
        results = described_class.batch_daily_job

        expect(results).to be_present
        expect(results).to all(satisfy("is not have category 107 and type_no 20 or 60") { |user|
          Type.where(category_id: 107, type_no: [20, 60]).pluck(:id).exclude?(user.corporation.transaction_status_id)
        })
      end
    end

    describe '.user_corporation_groups_not_owner' do
      before do
        described_class.role_ids.keys.each do |role_id|
          create(:user, :skip_callback, role_id: role_id, corporation: corporation_group.corporation)
          create(:user, :skip_callback, role_id: role_id, corporation: corporation_group_2.corporation)
        end
      end

      it 'returns users that are not owner by corporation group id' do
        results = described_class.user_corporation_groups_not_owner(corporation_group_2.id)

        expect(results).to all(have_attributes(corporation_groups: [corporation_group_2]))
        expect(results).to all(satisfy("is not owner") { |user| !user.owner?})
      end
    end

    describe '.by_corporation_group' do
      let!(:user) { create(:user, corporation: corporation_group.corporation) }
      let!(:user_2) { create(:user, corporation: corporation_group_2.corporation) }

      it 'returns users by corporation group id' do
        results = described_class.by_corporation_group(corporation_group.id)
        expect(results).to contain_exactly(user)
      end
    end

    describe '.has_owner_code' do
      let!(:user) { create(:user, owner_code_id: 1) }
      let!(:user_2) { create(:user, owner_code_id: nil) }

      it 'returns users that have owner code id' do
        results = described_class.has_owner_code

        expect(results).to contain_exactly(user)
      end
    end

    describe '.active_by_date' do
      let!(:user) { create(:user, started_at: 1.day.ago) }
      let!(:user_2) { create(:user, started_at: 1.day.from_now) }

      it 'returns users that have started_at is less than or equal to date' do
        date = Time.current

        expect(described_class.active_by_date(date)).to contain_exactly(user)
      end
    end

    describe '.have_location_not_closed' do
      let(:location) { create(:location, closed_at: nil, corporation_group: corporation_group) }
      let(:closed_location) { create(:location, closed_at: 1.day.ago, corporation_group: corporation_group_2) }
      let(:future_closed_location) { create(:location, closed_at: 1.day.from_now,
        corporation_group: create(:corporation_group, corporation: create(:corporation))) }

      let!(:user) { create(:user, corporation: location.corporation_group.corporation) }
      let!(:user_2) { create(:user, corporation: closed_location.corporation_group.corporation) }
      let!(:user_3) { create(:user, corporation: future_closed_location.corporation_group.corporation) }

      it 'returns users that have location not closed' do
        results = described_class.have_location_not_closed

        expect(results).to contain_exactly(user, user_3)
      end
    end
  end

  describe 'Instance Methods' do
    describe '#role_name' do
      context 'when role_id is present' do
        let(:user) { build(:user, role_id: :owner) }

        it 'returns translated role name' do
          expect(user.role_name).to eq(I18n.t("admin.users.roles.owner"))
        end
      end

      context 'when role_id is blank' do
        let(:user) { build(:user, role_id: nil) }

        it 'returns empty string' do
          expect(user.role_name).to eq("")
        end
      end
    end

    describe '#is_started?' do
      context 'when started_at is nil' do
        let(:user) { build(:user, started_at: nil) }

        it 'returns nil' do
          expect(user.is_started?).to be_nil
        end
      end

      context 'when started_at is in the past' do
        let(:user) { build(:user, started_at: 1.month.ago) }

        it 'returns true' do
          expect(user.is_started?).to be true
        end
      end

      context 'when started_at is in the future' do
        let(:user) { build(:user, started_at: 1.month.from_now) }

        it 'returns false' do
          expect(user.is_started?).to be false
        end
      end

      context 'when started_at is today' do
        let(:user) { build(:user, started_at: Date.current) }

        it 'returns true' do
          expect(user.is_started?).to be true
        end
      end
    end

    describe '#active_for_authentication?' do
      let(:user) { build(:user, started_at: 1.month.ago) }

      before do
        allow(user).to receive(:skip_confirm?).and_return(true)
      end

      it 'returns true when user is started and confirmed' do
        expect(user.active_for_authentication?).to be true
      end

      context 'when user is not started' do
        let(:user) { build(:user, started_at: 1.month.from_now) }

        it 'returns false' do
          expect(user.active_for_authentication?).to be false
        end
      end

      context 'when user is not confirmed' do
        before do
          allow(user).to receive(:skip_confirm?).and_return(false)
        end

        it 'returns false' do
          expect(user.active_for_authentication?).to be false
        end
      end
    end

    describe '#picked_locations' do
      let!(:locations) { create_list(:location, 2, corporation_group: create(:corporation_group, corporation: corporation)) }
      let(:user) { create(:user, corporation: corporation) }

      it 'returns locations with name and id' do
        expect(user.picked_locations).to be_an(ActiveRecord::Relation)
        expect(user.picked_locations.as_json).to eq(locations.as_json(only: [:id, :name]))
      end
    end

    describe '#skip_confirm?' do
      context 'when confirmation_token is blank' do
        let(:user) { build(:user, confirmation_token: nil) }

        it 'returns true' do
          expect(user.skip_confirm?).to be true
        end
      end

      context 'when confirmation_token is present and confirmed_at is present' do
        let(:user) { build(:user, confirmation_token: 'token', confirmed_at: Time.current) }

        it 'returns true' do
          expect(user.skip_confirm?).to be true
        end
      end

      context 'when confirmation_token is present and confirmed_at is nil' do
        let(:user) { build(:user, confirmation_token: 'token', confirmed_at: nil) }

        it 'returns false' do
          expect(user.skip_confirm?).to be false
        end
      end
    end

    describe '#not_have_to_confirm?' do
      context 'when user is new record' do
        let(:user) { build(:user) }

        it 'returns true' do
          expect(user.not_have_to_confirm?).to be true
        end
      end

      context 'when user is owner' do
        let(:user) { create(:user, role_id: :owner) }

        it 'returns true' do
          expect(user.not_have_to_confirm?).to be true
        end
      end

      context 'when user is not new record and not owner' do
        let(:user) { create(:user, :skip_callback, role_id: :normal) }

        it 'returns false' do
          expect(user.not_have_to_confirm?).to be false
        end
      end
    end

    describe '#can_change_pic?' do
      let(:location) { create(:location) }

      context 'when user is owner' do
        let(:user) { create(:user, role_id: :owner) }

        it 'returns true' do
          expect(user.can_change_pic?(location.id)).to be true
        end
      end

      context 'when user is system_admin and has access to location' do
        let(:user) { create(:user, role_id: :system_admin) }

        before do
          allow(user).to receive(:location_ids).and_return([location.id])
        end

        it 'returns true' do
          expect(user.can_change_pic?(location.id)).to be true
        end
      end

      context 'when user is system_admin but does not have access to location' do
        let(:user) { create(:user, role_id: :system_admin) }

        before do
          allow(user).to receive(:location_ids).and_return([])
        end

        it 'returns false' do
          expect(user.can_change_pic?(location.id)).to be false
        end
      end

      context 'when user is not owner or system_admin' do
        let(:user) { create(:user, :skip_callback, role_id: :normal) }

        it 'returns false' do
          expect(user.can_change_pic?(location.id)).to be false
        end
      end
    end

    describe '#can_create_or_edit_user?' do
      context 'when user is owner' do
        let(:user) { create(:user, role_id: :owner) }

        it 'returns true for any role' do
          expect(user.can_create_or_edit_user?('normal')).to be true
          expect(user.can_create_or_edit_user?('approver')).to be true
          expect(user.can_create_or_edit_user?('system_admin')).to be true
        end
      end

      context 'when user is normal' do
        let(:user) { create(:user, :skip_callback, role_id: :normal) }

        it 'returns false for any role' do
          expect(user.can_create_or_edit_user?('normal')).to be false
          expect(user.can_create_or_edit_user?('approver')).to be false
        end
      end

      context 'when user is system_admin' do
        let(:user) { create(:user, role_id: :system_admin) }

        it 'returns true for approver and normal roles' do
          expect(user.can_create_or_edit_user?('approver')).to be true
          expect(user.can_create_or_edit_user?('normal')).to be true
        end

        it 'returns false for owner and system_admin roles' do
          expect(user.can_create_or_edit_user?('owner')).to be false
          expect(user.can_create_or_edit_user?('system_admin')).to be false
        end
      end

      context 'when user is approver' do
        let(:user) { create(:user, :skip_callback, role_id: :approver) }

        it 'returns true only for normal role' do
          expect(user.can_create_or_edit_user?('normal')).to be true
        end

        it 'returns false for other roles' do
          expect(user.can_create_or_edit_user?('approver')).to be false
          expect(user.can_create_or_edit_user?('system_admin')).to be false
          expect(user.can_create_or_edit_user?('owner')).to be false
        end
      end
    end

    describe '#update_last_read_notification_at' do
      let(:user) { create(:user) }

      it 'updates last_read_notification_at to current time' do
        freeze_time do
          expect { user.update_last_read_notification_at }
            .to change { user.reload.last_read_notification_at }
            .to(ServerTime.now)
        end
      end
    end

    describe '#is_role_can_survey_location?' do
      let(:user) { create(:user, corporation: corporation) }

      before do
        allow(corporation).to receive(:labor?).and_return(false)
      end

      context 'when corporation haken type is labor' do
        before do
          allow(corporation).to receive(:labor?).and_return(true)
        end

        it 'returns false' do
          described_class.role_ids.keys.each do |role_id|
            user.role_id = role_id

            expect(user.is_role_can_survey_location?).to be false
          end
        end
      end

      context 'when role id is normal' do
        it 'returns false' do
          user.role_id = :normal

          expect(user.is_role_can_survey_location?).to be false
        end
      end

      context 'when role id is owner' do
        it 'returns true' do
          user.role_id = :owner

          expect(user.is_role_can_survey_location?).to be true
        end
      end

      context 'when role id is system_admin' do
        it 'returns true' do
          user.role_id = :system_admin

          expect(user.is_role_can_survey_location?).to be true
        end
      end

      context 'when role id is approver' do
        it 'returns true if that is store computer' do
          allow(user.account).to receive(:is_store_computer_account?).and_return(true)
          user.role_id = :approver

          expect(user.is_role_can_survey_location?).to be true
        end

        it 'returns false if that is not store computer' do
          allow(user.account).to receive(:is_store_computer_account?).and_return(false)
          user.role_id = :approver

          expect(user.is_role_can_survey_location?).to be false
        end

        it 'returns false if that is not found account' do
          allow(user).to receive(:account).and_return(nil)
          user.role_id = :approver

          expect(user.is_role_can_survey_location?).to be false
        end
      end
    end

    describe '#validation_location_validate_create_order_type' do
      let(:user) { create(:user, corporation: corporation) }
      let(:location) { create(:location) }

      before do
        allow(user.account).to receive(:is_store_computer_account?).and_return(true)
        allow(user.locations).to receive(:is_active).and_return([location])
        allow(location).to receive(:valid_order_step_1_info?).and_return(true)
      end

      it 'returns :valid if that is store computer and all active locations have valid' do
        expect(user.validation_location_validate_create_order_type).to eq(:valid)
      end

      it 'returns :valid if that is not store computer' do
        allow(user.account).to receive(:is_store_computer_account?).and_return(false)

        expect(user.validation_location_validate_create_order_type).to eq(:valid)
      end

      it 'returns :inactive_locations if that is not have active locations' do
        allow(user.locations).to receive(:is_active).and_return([])

        expect(user.validation_location_validate_create_order_type).to eq(:inactive_locations)
      end

      it 'returns :invalid_step_1 if any active locations have invalid order step 1' do
        allow(user.locations).to receive(:is_active).and_return([location])
        allow(location).to receive(:valid_order_step_1_info?).and_return(false)

        expect(user.validation_location_validate_create_order_type).to eq(:invalid_step_1)
      end
    end
  end

  describe 'Class Methods' do
    describe '.ransackable_attributes' do
      it 'returns allowed ransackable attributes' do
        expect(described_class.ransackable_attributes).to eq(%w(account_id))
      end
    end

    describe '.ransackable_associations' do
      it 'returns allowed ransackable associations' do
        expect(described_class.ransackable_associations).to eq(%w(account))
      end
    end
  end

  describe 'Nested Attributes' do
    let(:corporation_group) { create(:corporation_group, corporation: corporation) }
    let(:location) { create(:location, corporation_group: corporation_group) }
    let(:corporation_group_tag) { create(:corporation_group_tag, corporation: corporation) }
    let(:params) do
      {
        corporation_id: corporation.id,
        role_id: 1,
        started_at: Time.current,
        account_id: 1
      }
    end

    describe 'user_locations_attributes' do
      it 'accepts nested attributes for user_locations' do
        params[:user_locations_attributes] = [
          { location_id: location.id, corporation_group_id: corporation_group.id }
        ]

        user = described_class.new(params)
        expect { user.save! }.to change { user.user_locations.count }.from(0).to(1)
      end

      it 'destroys nested attributes for user_locations' do
        user = create(:user, corporation: location.corporation_group.corporation)

        user.assign_attributes({
          user_locations_attributes: [{
            id: user.user_locations.first.id,
            _destroy: true
          }]
        })

        expect { user.save! }.to change { user.user_locations.count }.from(1).to(0)
      end

      it 'does not create user_locations if location_id is blank' do
        params[:user_locations_attributes] = [
          { corporation_group_id: corporation_group.id }
        ]

        user = described_class.new(params)
        expect { user.save! }.not_to change { user.user_locations.count }
      end
    end

    describe 'user_corporation_groups_attributes' do
      it 'accepts nested attributes for user_corporation_groups' do
        params[:user_corporation_groups_attributes] = [
          { corporation_group_id: corporation_group.id }
        ]

        user = described_class.new(params)
        expect { user.save! }.to change { user.corporation_groups.count }.from(0).to(1)
      end

      it 'destroys nested attributes for user_corporation_groups' do
        user = create(:user, corporation: corporation_group.corporation)

        user.assign_attributes({
          user_corporation_groups_attributes: [{
            id: user.user_corporation_groups.first.id,
            _destroy: true
          }]
        })

        expect { user.save! }.to change { user.user_corporation_groups.count }.from(1).to(0)
      end

      it 'does not create user_corporation_groups if corporation_group_id is blank' do
        params[:user_corporation_groups_attributes] = [
          { corporation_group_id: '' }
        ]

        user = described_class.new(params)
        expect { user.save! }.not_to change { user.user_corporation_groups.count }
      end
    end

    describe 'user_groups_attributes' do
      it 'accepts nested attributes for user_groups' do
        params[:user_groups_attributes] = [
          { corporation_group_id: corporation_group.id }
        ]

        user = described_class.new(params)
        expect { user.save! }.to change { user.corporation_groups.count }.from(0).to(1)
      end

      it 'destroys nested attributes for user_corporation_groups' do
        user = create(:user, corporation: corporation_group.corporation)

        user.assign_attributes({
          user_corporation_groups_attributes: [{
            id: user.user_corporation_groups.first.id,
            _destroy: true
          }]
        })

        expect { user.save! }.to change { user.user_corporation_groups.count }.from(1).to(0)
      end

      it 'does not create user_corporation_groups if corporation_group_id is blank' do
        params[:user_corporation_groups_attributes] = [
          { corporation_group_id: '' }
        ]

        user = described_class.new(params)
        expect { user.save! }.not_to change { user.user_corporation_groups.count }
      end
    end
  end

  describe 'Protected Methods' do
    describe '#confirmation_required?' do
      it 'returns false' do
        user = described_class.new

        expect(user.send(:confirmation_required?)).to be false
      end
    end
  end

  describe 'Uploaders' do
    it 'mounts AdminAvatarUploader on :avatar' do
      expect(described_class.new.avatar).to be_an_instance_of(AdminAvatarUploader)
    end

    context 'when handling file uploads' do
      let(:file_path) { Rails.root.join('spec/fixtures/files/sample.png') }
      let(:uploaded_file) { Rack::Test::UploadedFile.new(file_path) }
      let!(:user) { create(:user, :skip_callback, avatar: uploaded_file) }

      it 'stores the uploaded file' do
        expect(user.avatar.file).to exist
        expect(File.basename(user.avatar.path)).to be_present
      end

      it 'allows removing the attachment via remove_avatar' do
        user.remove_avatar = true
        user.save(validate: false)
        user.reload
        expect(user.avatar.file).to be_nil
      end
    end
  end
end
