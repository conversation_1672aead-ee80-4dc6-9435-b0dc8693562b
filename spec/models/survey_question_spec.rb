require 'rails_helper'

RSpec.describe SurveyQuestion, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:content) }
  end

  describe 'Associations' do
    it { should have_many(:survey_answers) }
  end

  describe 'Enums' do
    it { should define_enum_for(:survey_question_type).with_values(staff_cancel_training: 0, staff_reschedule_training: 1) }
  end

  describe 'Scopes' do
    describe '.active' do
      let!(:active_question) { create(:survey_question, is_active: true) }
      let!(:inactive_question) { create(:survey_question, is_active: false) }

      it 'returns active questions' do
        expect(described_class.active).to include(active_question)
        expect(described_class.active).not_to include(inactive_question)
      end
    end
  end

  describe 'Instance methods' do
    describe '#answers' do
      let(:survey_question) { create(:survey_question) }
      let!(:answer_to_all) { create(:survey_answer, survey_question: survey_question, to_audience: :to_all) }
      let!(:answer_to_staff) { create(:survey_answer, survey_question: survey_question, to_audience: :to_staff) }

      context 'when to_audience is :to_all' do
        it 'returns answers to all' do
          expect(survey_question.answers(:to_all)).to include(
            hash_including(
              id: answer_to_all.id,
              content: answer_to_all.content,
              type: answer_to_all.answer_type,
              to_audience: answer_to_all.to_audience,
              survey_question_id: survey_question.id
            )
          )
        end
      end

      context 'when to_audience is :to_staff' do
        it 'returns answers to staff' do
          expect(survey_question.answers(:to_staff)).to include(
            hash_including(
              id: answer_to_staff.id,
              content: answer_to_staff.content,
              type: answer_to_staff.answer_type,
              to_audience: answer_to_staff.to_audience,
              survey_question_id: survey_question.id
            )
          )
        end
      end

      context 'when there are no answers' do
        let(:empty_question) { create(:survey_question) }

        it 'returns an empty array' do
          expect(empty_question.answers(:to_all)).to eq([])
        end
      end
    end
  end
end
