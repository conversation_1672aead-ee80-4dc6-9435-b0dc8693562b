require 'rails_helper'

RSpec.describe BatchArrangeCondition, type: :model do
  let(:order) do
    instance_double('Order', id: 1, corporation_id: 1, corporation_group_id: 1)
  end

  let(:order_case) do
    instance_double(
      'OrderCase', id: 1, order: order, regular_order?: false,
      corporation_id: 1,
      location_id: 1,
      case_started_at: ServerTime.now + 3.days,
      case_ended_at: ServerTime.now + 3.days + 4.hours,
      staff_apply_order_cases: saocs
    )
  end

  let(:current_level) do
    instance_double('StaffLevel', before_debut?: false)
  end

  let(:staff) do
    instance_double(
      'Staff', id: 1, current_level: current_level, age_by_time: 30
    )
  end

  let(:arrangement_id) { 1 }

  let(:saocs) do
    double(:staff_apply_order_cases)
  end

  let(:temporary_arrange) { false }

  subject(:batch_condition) do
    described_class.new(order_case, staff, arrangement_id, temporary_arrange, [])
  end

  before(:example) do
    allow(staff).to receive(:arranged?).with(order_case.id).and_return(false)
    allow(saocs).to receive(:by_staff_id).with(staff.id)
      .and_return([instance_double(StaffApplyOrderCase)])
  end

  it "inherits from ArrangeCondition" do
    expect(batch_condition).to be_a(ArrangeCondition)
  end
end
