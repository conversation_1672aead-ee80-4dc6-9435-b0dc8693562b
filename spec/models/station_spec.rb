require 'rails_helper'

RSpec.describe Station, type: :model do
  describe 'Associations' do
    it { should belong_to(:railway_line).optional }
  end

  describe 'Validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:latitude) }
    it { should validate_presence_of(:longitude) }
  end

  describe 'Scopes' do
    describe '.with_name_like' do
      let!(:station1) { create(:station, name: 'Tokyo') }
      let!(:station2) { create(:station, name: 'Osaka') }

      it 'returns stations with names containing the given string' do
        expect(described_class.with_name_like('Tokyo')).to include(station1)
        expect(described_class.with_name_like('Tokyo')).not_to include(station2)
      end
    end

    describe '.of_railway_line' do
      let(:railway_line) { create(:railway_line) }
      let!(:station1) { create(:station, railway_line: railway_line) }
      let!(:station2) { create(:station) }

      it 'returns stations belonging to the given railway line' do
        expect(described_class.of_railway_line(railway_line.id)).to include(station1)
        expect(described_class.of_railway_line(railway_line.id)).not_to include(station2)
      end
    end

    describe '.of_prefecture' do
      let!(:station1) { create(:station, prefecture_id: '1') }
      let!(:station2) { create(:station, prefecture_id: '2') }

      it 'returns stations belonging to the given prefecture' do
        expect(described_class.of_prefecture('1')).to include(station1)
        expect(described_class.of_prefecture('1')).not_to include(station2)
      end
    end
  end

  describe 'Instance methods' do
    describe '#full_name' do
      let(:railway_line) { build(:railway_line, name: 'Yamanote Line') }
      let(:station) { build(:station, name: 'Tokyo', railway_line: railway_line) }

      it 'returns the full name of the station' do
        expect(station.full_name).to eq('Tokyo (Yamanote Line)')
      end

      it 'does not raise error if railway line is nil' do
        station.railway_line = nil
        expect{ station.full_name }.not_to raise_error
      end
    end

    describe '#name_with_railway_line' do
      let(:railway_line) { build(:railway_line, name: 'Yamanote Line') }
      let(:station_with_line) { build(:station, name: 'Tokyo', railway_line: railway_line) }
      let(:station_without_line) { build(:station, name: 'Tokyo') }

      it 'returns the name with railway line if railway line exists' do
        expect(station_with_line.name_with_railway_line)
          .to eq("Yamanote LineTokyo#{I18n.t('staff.order_cases.station')}")
      end

      it 'returns the name without railway line if railway line does not exist' do
        station_without_line.railway_line = nil

        expect(station_without_line.name_with_railway_line)
          .to eq("#{I18n.t('staff.order_cases.line')}Tokyo#{I18n.t('staff.order_cases.station')}")
      end
    end

    describe '#station_name' do
      let(:station) { build(:station, name: 'Tokyo') }

      it 'returns the station name with suffix' do
        expect(station.station_name).to eq('Tokyo駅')
      end

      it 'returns nil if name is nil' do
        station.name = nil

        expect(station.station_name).to be_nil
      end
    end
  end

  describe 'Acts as paranoid' do
    let(:station) { create(:station) }

    describe '#destroy' do
      it 'marks the station as deleted' do
        station.destroy
        expect(described_class.find_by(id: station.id)).to be_nil
        expect(described_class.with_deleted.find_by(id: station.id)).to eq(station)
      end
    end

    describe '#restore' do
      it 'restores the station' do
        station.destroy
        station.restore
        expect(described_class.find_by(id: station.id)).to eq(station)
      end
    end
  end
end
