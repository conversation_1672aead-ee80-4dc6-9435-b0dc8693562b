class NotifyCancelInsuranceToAdminWorker
  include Sidekiq::Worker
  include Sidekiq::Status::Worker

  MAIL_TYPES = %w(reject_employment_insurance_to_admin reject_subsection_insurance_to_admin
    reject_subsection_and_employment_insurance_to_admin)

  REJECT_EMPLOYMENT_INSURANCE = "reject_employment_insurance_to_admin"
  REJECT_SUBSECTION_INSURANCE = "reject_subsection_insurance_to_admin"
  REJECT_SUBSECTION_AND_EMPLOYMENT_INSURANCE = "reject_subsection_and_employment_insurance_to_admin"

  def perform staff_id, mail_type
    staff = Staff.find_by(id: staff_id)
    admins = Admin.is_insurance_mail_receiver
    return unless admins.present? && staff.present? && MAIL_TYPES.include?(mail_type.to_s)

    create_insurance_logs mail_type, staff
  end

  private
  def create_insurance_logs mail_type, staff
    case mail_type.to_s
    when REJECT_EMPLOYMENT_INSURANCE
      EmploymentInsuranceLog.create_log(staff.account_name, staff.staff_number, :reject_insurance)
    when REJECT_SUBSECTION_INSURANCE
      SubsectionInsuranceLog.create_log(staff.account_name, staff.staff_number, :reject_insurance)
    when REJECT_SUBSECTION_AND_EMPLOYMENT_INSURANCE
      EmploymentInsuranceLog.create_log(staff.account_name, staff.staff_number, :reject_insurance)
      SubsectionInsuranceLog.create_log(staff.account_name, staff.staff_number, :reject_insurance)
    end
  end
end
