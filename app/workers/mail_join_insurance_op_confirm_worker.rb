class MailJoinInsuranceOpConfirmWorker
  include Sidekiq::Worker
  include Sidekiq::Status::Worker

  def perform staff_name, staff_number
    admins = Admin.includes(:account).is_insurance_mail_receiver
    admins.each do |admin|
      arguments = [staff_name, staff_number, admin.name, admin.email]
      StaffMailer.join_employment_insurance(*arguments).deliver_now
    end
    EmploymentInsuranceLog.create_log(staff_name, staff_number, :join_insurance)
  end
end
