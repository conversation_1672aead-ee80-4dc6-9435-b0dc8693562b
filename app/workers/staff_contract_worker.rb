class StaffContractWorker
  include Sidekiq::Worker
  include Sidekiq::Status::Worker

  MAIL_TYPES = %w(reject_employment_insurance_to_admin reject_subsection_insurance_to_admin
    reject_subsection_and_employment_insurance_to_admin)

  def perform staff_contract_ids, status, is_send_mail = false
    @staff_contracts = StaffContract.by_ids staff_contract_ids
    log_content = "[StaffContractWorker] Start update at #{ServerTime.now}: " \
      "#{status} staff_contracts ids:#{staff_contract_ids}, jid: #{self.jid} \n"

    perform_approved @staff_contracts if status == "approved"
    perform_denied @staff_contracts if status == "denied" && is_send_mail

    log_content << "[StaffContractWorker] Finished update staff_contract at #{ServerTime.now}, " \
      "jid: #{self.jid}\n"
    Logger.new(Rails.root.join("log", "staff_contract_log.log")).info log_content
  end

  private
  def perform_denied staff_contracts
    staff_contracts.each do |staff_contract|
      send_edit_contract_email staff_contract.staff
    end
  end

  def perform_approved staff_contracts
    admins = Admin.is_insurance_mail_receiver
    staff_contracts.each do |staff_contract|
      staff = staff_contract.staff
      last_contract_type = staff.last_contract&.contract_type || :blank_column
      staff_contract_history_count = staff.staff_contract_histories.count

      reject_work_condition_mail_type = ""

      if staff_contract_history_count > 1
        contract_condition = ThirdContractWarningService.new(staff, staff_contract.work_condition)
        reject_work_condition_mail_type = contract_condition.reject_work_condition_mail_type if
          contract_condition.warning?
      end

      if staff_contract_history_count == 1
        contract_condition = SecondContractWarningService.new(staff, staff_contract.work_condition)
        reject_work_condition_mail_type = contract_condition.reject_insurance_mail_type if
          contract_condition.warning?
      end

      staff.staff_contract_histories.create(
        contract_start_date: staff_contract.contract_start_date,
        contract_end_date: staff_contract.contract_end_date,
        contract_type: last_contract_type,
        indefinite_employment_flag: :term
      )

      if reject_work_condition_mail_type.present? && MAIL_TYPES.include?(reject_work_condition_mail_type)
        admins.each do |admin|
          StaffMailer.send(reject_work_condition_mail_type.to_s, staff, admin).deliver_now
        end
      end

      staff.update_columns(staff.last_contract_attrs.merge(contract_send_mail_status: 0))

      if staff_contract.is_request_update
        attrs = staff_contract.slice(StaffContract::EXPECTATION_ATTRS)
        staff.staff_expectation.update_columns(attrs)
      end
    end
  end

  def send_edit_contract_email staff
    return unless staff.is_active_mail_setting?(:update_contract)

    AdminMailer.contract_update_ok(staff).deliver_now
    staff.update_columns contract_send_mail_status: 1
    app_notification_option = {
      staff_id: staff.id,
      creator_type: :by_system,
      notification_type: :update_contract,
      params: {}
    }
    AppSendNotificationWorker.perform_async([app_notification_option])
  end
end
