class ApplyOrderCaseCondition
  include ActiveModel::Model
  include ActiveModel::Validations

  attr_reader :order_case, :staff, :apply_condition, :order, :apply_condition_contact_op,
    :app_version, :skip_staff_status_1, :skip_oc_status_3, :skip_reject_auto_matching_if_time_changed

  AGE_18 = 18

  validate :oc_status_1, :oc_status_3, :oc_status_4, :oc_status_5,
    :oc_working_time_1, :oc_working_time_2,
    :oc_public_1, :oc_public_2, :oc_public_3, :oc_public_4, :oc_public_5, :oc_public_6,
    :staff_status_1, :staff_status_4, :staff_status_8, :staff_status_9,
    :oc_staff_1, :oc_staff_2, :oc_staff_3, :oc_staff_4, :oc_staff_6,
    :oc_staff_7, :oc_staff_9, :oc_staff_10, :oc_staff_11, :two_hours_before_deadline,
    :staff_work_exp_2, :out_of_apply_contract, :reject_auto_matching_if_time_changed,
    :two_hours_before_deadline_with_urget_job_in_auto_matching

  def initialize order_case, staff, options = {}
    @order_case = order_case
    @staff = staff
    @order = @order_case.order
    @app_version = options[:app_version]
    @skip_staff_status_1 = options[:skip_staff_status_1]
    @skip_oc_status_3 = options[:skip_oc_status_3]
    @skip_reject_auto_matching_if_time_changed = options[:skip_reject_auto_matching_if_time_changed]
  end

  def oc_status_1
    errors.add :apply_condition, :oc_status_1 if order_case.cancel?
  end

  def oc_status_3
    return if skip_oc_status_3

    errors.add :apply_condition, :oc_status_3 if staff.applied_order_case?(order_case.id)
  end

  def oc_status_4
    return if staff.arranged? order_case.id

    errors.add :apply_condition, :oc_status_4 if order_case.full_arranged?
  end

  def oc_status_5
    errors.add :apply_condition, :oc_status_5 if staff.arranged? order_case.id
  end

  def oc_working_time_1
    return if order_case.is_urgent?

    is_timeout = order_case.over_normal_apply_deadline? unless order_case.regular_order?
    is_timeout = order.regular_order_timeout_apply? if order_case.regular_order?
    errors.add :apply_condition, :oc_working_time_1 if is_timeout
  end

  def oc_working_time_2
    return if order_case.regular_order? || !order_case.is_urgent?

    errors.add :apply_condition, :oc_working_time_2 if order_case.over_urgent_apply_deadline?
  end

  def oc_public_1
    errors.add :apply_condition, :oc_public_1 if order_case.type_hidden? && !has_offer?
  end

  def oc_public_2
    return unless order_case.type_limited?

    errors.add :apply_condition, :oc_public_2 if oc_public_staff && !oc_public_staff.is_public &&
      oc_public_staff.staff_id == staff.id
  end

  def oc_public_3
    errors.add :apply_condition, :oc_public_3 if order_case.type_limited? && !oc_public_staff &&
      !has_offer?
  end

  def oc_public_4
    errors.add :apply_condition, :oc_public_4 if order_case.type_hidden? && has_offer?
  end

  def oc_public_5
    return unless order_case.type_limited?

    errors.add :apply_condition, :oc_public_5 if oc_public_staff&.is_public &&
      oc_public_staff.staff_id != staff.id
  end

  def oc_public_6
    errors.add :apply_condition, :oc_public_6 if order_case.type_limited? && !oc_public_staff &&
      has_offer?
  end

  def staff_status_1
    return if order.contract? || skip_staff_status_1 || not_before_debut?
    return errors.add :apply_condition, :staff_status_1_exp if staff.has_experience?

    errors.add :apply_condition, :staff_status_1_non_exp
  end

  def staff_status_4
    return if staff_age >= AGE_18
    return unless !order_case.is_time_changable && order_case.is_student_night_working?

    errors.add :apply_condition, :staff_status_4
  end

  # def staff_status_5
  #   errors.add :apply_condition, :staff_status_5 if staff.invalid_contract_condition?
  # end

  def staff_status_8
    return unless order.is_from_lawson? && staff.current_level&.not_require_training? && !order.contract? &&
      staff.op_confirm?

    errors.add :apply_condition_contact_op, :staff_status_8
  end

  def staff_status_9
    errors.add :apply_condition, :staff_status_9 if staff.matchbox?
  end

  def oc_staff_1
    staff_mess_corporation = staff_messages_by_corporation.forbidden
    staff_mess_corporation_group = staff_messages_by_corporation_group.forbidden
    return if staff_mess_corporation.blank? && staff_mess_corporation_group.blank?

    errors.add :apply_condition, :oc_staff_1
  end

  def oc_staff_2
    staff_mess_corporation = staff_messages_by_corporation.warning
    staff_mess_corporation_group = staff_messages_by_corporation_group.warning
    return if staff_mess_corporation.blank? && staff_mess_corporation_group.blank?

    errors.add :apply_condition, :oc_staff_2
  end

  def oc_staff_3
    errors.add :apply_condition, :oc_staff_3 if staff_messages_by_location.forbidden.present?
  end

  def oc_staff_4
    errors.add :apply_condition, :oc_staff_4 if staff_messages_by_location.warning.present?
  end

  def oc_staff_6
    errors.add :apply_condition, :oc_staff_6 if work_between_date?("expired")
  end

  def oc_staff_7
    errors.add :apply_condition, :oc_staff_7 if work_between_date?("absence")
  end

  def oc_staff_9
    return unless staff.nationality_other_than_japan?

    check_date = order_case.regular_order? ? last_working_date : working_date
    return if check_date.blank?

    errors.add :apply_condition, :oc_staff_9 if staff.residence_expiration_date &&
      check_date > staff.residence_expiration_date
  end

  def oc_staff_10
    return unless staff.retirement_date

    check_date = order_case.regular_order? ? last_working_date : working_date
    return if check_date.blank?

    errors.add :apply_condition, :oc_staff_10 if check_date > staff.retirement_date
  end

  def oc_staff_11
    return unless staff&.required_re_training? && is_auto_matching?

    errors.add :apply_condition, :oc_staff_11
  end

  def out_of_apply_contract
    return if !staff.op_confirm? || !staff.haken? || oboj? ||
      order.contract? || order_case.segment_trainning
    return if staff.last_contract.present? && staff.last_contract.is_day_contract

    check_date = order_case.regular_order? ? last_working_date : working_date
    return if check_date.blank?
    return if staff.contract_history_by_date(check_date).present? ||
      staff.contract_history_by_date(ServerTime.now.to_date).present?

    errors.add :apply_condition_contact_op, :out_of_apply_contract
  end

  def two_hours_before_deadline
    started_at = order_case.regular_order? ? order.last_started_at : order_case.case_started_at
    return if started_at.blank?

    deadline = started_at - OrderCase::DEADLINE_APPLY
    return if ServerTime.now <= deadline || ServerTime.now >= started_at

    errors.add :apply_condition_contact_op, :two_hours_before_deadline
  end

  def two_hours_before_deadline_with_urget_job_in_auto_matching
    return unless order_case.is_urgent? && is_auto_matching?

    is_timeout = order_case.over_normal_apply_deadline? unless order_case.regular_order?
    is_timeout = order.regular_order_timeout_apply? if order_case.regular_order?
    errors.add :apply_condition_contact_op, :two_hours_before_deadline if is_timeout
  end

  def staff_work_exp_2
    errors.add :apply_condition, :staff_work_exp_2 if
      is_arranged_in_time?(order_case.case_started_at, order_case.case_ended_at)

    # ! Deprecated
    # if order_case.regular_order?
    #   working_times = OrderCase.by_order_id(order_case.order_id).pluck(:case_started_at,
    #     :case_ended_at)
    #   working_times.each do |wk_time|
    #     return errors.add :apply_condition, :staff_work_exp_2 if is_arranged_in_time?(wk_time[0],
    #       wk_time[1])
    #   end
    # elsif is_arranged_in_time?(order_case.case_started_at, order_case.case_ended_at)
    #   errors.add :apply_condition, :staff_work_exp_2
    # end
  end

  def reject_auto_matching_if_time_changed
    return if skip_reject_auto_matching_if_time_changed
    return if !is_auto_matching? || !order_case.is_time_changable

    errors.add :apply_condition, :reject_auto_matching_with_oc_is_time_changeable
  end

  private
  def is_arranged_in_time? start_time, end_time
    current_arranged = arranged_by_staff.current_arrange_by_time_range(start_time, end_time)
    OrderCase.by_ids(current_arranged.pluck(:order_case_id).uniq).not_cancel.present?
  end

  def staff_messages_by_corporation_group
    StaffMessages::MessagesByTypeQuery.new(
      staff.id, :from_corporation_group,
      {
        corporation_group_ids: order_case.order.corporation_group_id,
        working_date: working_date
      }
    ).perform
  end

  def staff_messages_by_corporation
    StaffMessages::MessagesByTypeQuery.new(
      staff.id, :from_corporation,
      {
        corporation_ids: order_case.corporation_id,
        working_date: working_date
      }
    ).perform
  end

  def staff_messages_by_location
    StaffMessages::MessagesByTypeQuery.new(
      staff.id, :from_location,
      {
        location_ids: order_case.location_id,
        working_date: working_date
      }
    ).perform
  end

  def oc_public_staff
    order_case_by_staffs = OrderCasePublicStaff.where(order_case_id: order_case.id)
    order_case_by_staff = order_case_by_staffs.group_by(&:staff_id)

    return order_case_by_staff[staff.id].first if order_case_by_staff.try(:[], staff.id)

    order_case_by_staffs&.first
  end

  def working_date
    order_case.case_started_at.to_date
  end

  def last_working_date
    order.last_started_at&.to_date
  end

  def work_between_date? type
    start_date = staff["#{type}_start_date"]
    end_date = staff["#{type}_end_date"]
    check_date = order_case.regular_order? ? last_working_date : working_date
    return if check_date.blank?
    return check_date.between?(start_date, end_date) if start_date && end_date

    (start_date && start_date <= check_date) || (end_date && end_date >= check_date)
  end

  def arranged_by_staff
    staff.arrangements.is_arranged
  end

  def has_offer?
    @has_offer ||= staff.has_offer? order_case.id
  end

  def staff_age
    @staff_age ||= staff.age_by_time order_case.case_started_at
  end

  def is_auto_matching?
    # TODO(Phuong): move this to an external service, there is duplication in staff_apply_order_case.rb model
    if ServerTime.now.hour <= StaffApplyOrderCase::HOUR_END_MATCHING
      start_matching = ServerTime.now.beginning_of_day.yesterday + StaffApplyOrderCase::HOUR_START_MATCHING.hours
      end_matching = ServerTime.now.beginning_of_day + StaffApplyOrderCase::HOUR_END_MATCHING.hours
    else
      start_matching = ServerTime.now.beginning_of_day + StaffApplyOrderCase::HOUR_START_MATCHING.hours
      end_matching = ServerTime.now.beginning_of_day.tomorrow + StaffApplyOrderCase::HOUR_END_MATCHING.hours
    end
    [order_case.case_started_at, ServerTime.now].all? do |time|
      time >= start_matching && time <= end_matching
    end
  end

  def oboj?
    return false if staff.current_level.blank?

    staff.current_level.oboj?
  end

  def not_before_debut?
    return false if staff.current_level.blank?

    !staff.current_level.before_debut?
  end
end
