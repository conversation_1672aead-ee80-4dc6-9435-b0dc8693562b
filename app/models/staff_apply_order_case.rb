class StaffApplyOrderCase < ApplicationRecord
  acts_as_paranoid

  default_scope{where(apply_type_id: :staff_apply)}
  BLOCK_TIME_1 = 360
  BLOCK_TIME_2 = 405
  BLOCK_TIME_3 = 525
  BLOCK_TIME_4 = 540
  MIN_PRICE = 0
  MAX_PRICE = 999_999
  AUTO_MATCHING_NG = 5
  ARRANGEMENT_STOP = 4
  HOUR_START_MATCHING = 20
  HOUR_END_MATCHING = 10
  NIGHT_TIME = {start: "22:00", end: "05:00"}
  AGE_18 = 18
  APPLY_PARAMS = %i(staff_id order_id order_branch_id order_case_id
    location_id is_used_original_condition requested_started_at requested_ended_at comment
    requested_transportation_fee status_id)
  BATCH_ARRANGE_METHODS = [:staff_account_name, :request_time, :staff_home_station_station_name,
    :can_delete, :over_transportation_expenses_limit, :arrangement_status, :sum_evaluation,
    :staff_absence_ratio, :staff_inlate_ratio, :arrangement_store_note, :location_caution_to_staff_mail,
    :arrangement_arrange_comment, :staff_before_debut, :staff_matching_rate, :staff_special_status,
    :staff_insurance_subsection_label, :staff_is_registration]
  IMPORT_ATTRS = [:staff_id, :order_id, :order_branch_id, :order_case_id, :location_id,
    :is_used_original_condition, :requested_started_at, :requested_ended_at, :comment,
    :requested_transportation_fee, :status_id, :apply_type_id]
  belongs_to :staff
  belongs_to :location
  belongs_to :order
  belongs_to :order_branch
  belongs_to :order_case
  belongs_to :arrangement, optional: true

  enum :status_id, {not_processed: 1, arranged: 2, rejected: 3}
  enum :apply_type_id, {staff_apply: 1, admin_arrange: 2}

  validates :requested_started_at, :requested_ended_at, presence: true,
    unless: :is_used_original_condition
  validates :requested_transportation_fee, numericality: {
    greater_than_or_equal_to: MIN_PRICE,
    less_than_or_equal_to: MAX_PRICE,
    only_integer: true
  }, allow_blank: true
  validates :staff_id, uniqueness: {scope: :order_case_id}
  validates :requested_ended_at, value_greater_than: :requested_started_at, unless: :is_used_original_condition
  validate :order_case_time_rule, :validate_student_night_job

  before_validation :update_requested_date
  after_create :update_order_case_staff_apply_count, :update_remained_apply_for_order_case,
    :update_arrange_logs, :auto_matching_with_order_portion
  before_save :cal_requested_working_time, if: :is_used_original_condition_changed?
  after_destroy :update_order_case_staff_apply_count, unless: :rejected?
  before_save :set_transportation_fee, :check_request_time_changable
  delegate :caution_to_staff_mail, :name, to: :location, prefix: true, allow_nil: true

  scope :not_in_status_work_achievement, ->statuses do
    where("work_achievements.working_time_status_id
      not in (?) OR staff_apply_order_cases.arrangement_id is NULL", statuses)
  end

  scope :by_staff_id, ->staff_id{where(staff_id: staff_id)}
  scope :default_unscope, ->{unscope(where: :apply_type_id)}
  scope :not_start, -> do
    joins(:order_case).where("order_cases.case_started_at >= ?", ServerTime.now)
  end
  scope :by_order_case_ids, ->order_case_ids{where(order_case_id: order_case_ids)}
  scope :by_order_ids, ->order_ids{where(order_id: order_ids)}
  scope :current_not_processed, ->order_ids, order_case_ids do
    by_order_ids(order_ids).by_order_case_ids(order_case_ids).not_processed
  end
  scope :block_staff_apply_order_case, ->(ids, start_time, end_time) do
    joins(:order_case).where(id: ids).where("(order_cases.case_started_at >
      :start_time AND order_cases.case_started_at < :end_time) OR
      (order_cases.case_ended_at > :start_time AND order_cases.case_ended_at < :end_time)
      OR (:start_time > order_cases.case_started_at AND :start_time < order_cases.case_ended_at)
      OR (:end_time > order_cases.case_started_at AND :end_time < order_cases.case_ended_at)",
      start_time: start_time, end_time: end_time)
  end

  scope :in_range, ->(start_time, end_time) do
    joins(:order_case).where("order_cases.case_started_at >= ? AND
      order_cases.case_started_at < ?", start_time, end_time)
  end

  scope :arrangement_in_range, ->(start_time, end_time) do
    joins(:arrangement)
      .where("arrangements.working_started_at >= ? AND
        arrangements.working_started_at <= ?", start_time, end_time)
  end

  scope :group_by_staff_and_order_case_started_date, ->order_case_ids do
    select("staff_id, DATE(order_cases.case_started_at) AS order_case_started_date, " \
      "COUNT(*) AS total_apply")
      .joins(:order_case).where(order_case_id: order_case_ids)
      .group("staff_id, order_case_started_date")
  end

  scope :not_rejected, ->{where.not(status_id: StaffApplyOrderCase.status_ids[:rejected])}
  scope :not_arranged, ->{where.not(status_id: StaffApplyOrderCase.status_ids[:arranged])}
  scope :not_cancel, -> do
    joins(:order_case).where.not(order_cases: {status_id: :cancel})
  end

  scope :around_one_hours_apply, ->order_case do
    joins(:order_case)
      .where.not("(order_cases.case_started_at >= :end_time
        OR order_cases.case_ended_at <= :start_time)",
        start_time: order_case.case_started_at - 1.hour,
        end_time: order_case.case_ended_at + 1.hour)
  end

  scope :time_duplicated, ->order_case do
    joins(:order_case).where("(order_cases.case_started_at > :start_time " \
      "AND order_cases.case_started_at < :end_time) OR " \
      "(order_cases.case_ended_at > :start_time AND order_cases.case_ended_at < :end_time) OR " \
      "(order_cases.case_started_at <= :start_time AND order_cases.case_ended_at >= :end_time)",
      start_time: order_case.case_started_at - 1.hour,
      end_time: order_case.case_ended_at + 1.hour)
  end

  scope :portions_arranged, -> do
    joins(arrangement: :order_portion)
      .where(order_portions: {status_id: OrderPortion.status_ids[:arranged]})
  end

  scope :apply_order_case_after, ->time do
    joins(:order_case).where("order_cases.case_started_at > ?", time)
  end

  scope :by_staff_status, ->staff_status do
    joins(:staff).where("staffs.status_id = ?", staff_status)
  end

  delegate :account_name, :home_station_name, :home_station_station_name, :inlate_ratio,
    :absence_ratio, :insurance_subsection_label, :is_registration, to: :staff, prefix: true
  delegate :store_note, :arrange_comment, to: :arrangement, prefix: true, allow_nil: true
  delegate :matching_rate, to: :staff, prefix: true, allow_nil: true
  delegate :name, to: :location, prefix: true
  delegate :case_started_at, :case_ended_at, to: :order_case

  def sum_evaluation
    staff.lastest_evaluation_summaries.first
  end

  def update_requested_date
    return if self.is_used_original_condition

    oc_start_time = self.order_case.case_started_at.strftime(Settings.time.formats)
    oc_end_time = self.order_case.case_ended_at.strftime(Settings.time.formats)
    if self.requested_started_at
      start_time = self.requested_started_at.strftime(Settings.time.formats)
      day_start = self.order_case.case_started_at
      day_start += 1.day if oc_start_time > oc_end_time && start_time < oc_start_time
      self.requested_started_at = self.requested_started_at.change(day: day_start.day,
        year: day_start.year, month: day_start.month)
    end

    return unless self.requested_ended_at

    end_time = self.requested_ended_at.strftime(Settings.time.formats)
    day_end = self.order_case.case_ended_at
    day_end -= 1.day if oc_start_time > oc_end_time && oc_end_time < end_time

    self.requested_ended_at = self.requested_ended_at.change(day: day_end.day,
      year: day_end.year, month: day_end.month)
  end

  def request_time
    return if self.is_used_original_condition

    next_day = I18n.t("staff.order_cases.next_day") if self.requested_started_at.strftime(Settings
      .date.formats) < self.requested_ended_at.strftime(Settings.date.formats)
    "#{self.requested_started_at.strftime(Settings.time.formats)}~#{next_day}#{self.requested_ended_at.strftime(Settings.time.formats)}"
  end

  def update_remained_apply_for_order_case
    self.order_case.update_remained_apply
  end

  def can_delete
    arrangement_status != OrderPortion::STATUSES[:arranged]
  end

  def is_arrange_or_temporary_arrange
    data_status = [OrderPortion::STATUSES[:arranged], OrderPortion::STATUSES[:temporary_arrange]]
    data_status.include?(arrangement_status)
  end

  def arrangement_status
    arrangement ||= Arrangement.of_staff(self.staff_id).by_order_case_id(self.order_case_id).first
    arrangement&.order_case_status
  end

  def staff_before_debut
    return false if staff.op_confirm?

    staff.current_level.blank? || staff.current_level&.before_debut?
  end

  def staff_special_status
    staff.has_special_status?
  end

  def over_transportation_expenses_limit
    return false unless self.requested_transportation_fee
    return self.requested_transportation_fee > 0 unless payment_unit_price

    if staff.current_level&.before_debut?
      pay_field = :training_transportation_expenses_limit
    else
      pay_field = [staff.level_by_unit_price, staff.rank_by_unit_price,
        "transportation_expenses_limit"].join("_")
    end
    payment_unit_price[pay_field].to_i < self.requested_transportation_fee
  end

  def update_arrange_logs
    ImportDataArrangeLogService.new([self.order_case_id],
      ArrangeLog.action_types[:oc_applied], nil, self.staff_id).import_data
  end

  def auto_matching_with_order_portion
    return if self.staff.current_level.blank? || !staff.op_confirm? ||
      self.staff.arrangement_type.include?(AUTO_MATCHING_NG) ||
      self.staff.arrangement_type.include?(ARRANGEMENT_STOP)

    return unless self.staff_apply? && hour_auto_matching?(self.created_at) &&
      hour_auto_matching?(self.order_case.case_started_at) &&
      self.is_used_original_condition && self.location.is_auto_matching

    valid_portion = self.order_case.order_portions.not_arrange.first
    arrangement = valid_portion&.arrangement
    arrange_condition = ArrangeCondition.new(self.order_case, self.staff, arrangement&.id)
      .valid_arrange?
    return unless arrange_condition[:valid_arrange] && !arrange_condition[:warning_arrange]
    return unless valid_portion.present? && arrangement&.id.present?

    status = ArrangementStaffService.new(arrangement, self.staff, nil, auto_matching: true).arrange_from_none(true)
    return unless status

    arrangement.update_working_time_follow_requested_time
  end

  def hour_auto_matching? time
    if ServerTime.now.hour <= HOUR_END_MATCHING
      start_matching = ServerTime.now.beginning_of_day.yesterday + HOUR_START_MATCHING.hours
      end_matching = ServerTime.now.beginning_of_day + HOUR_END_MATCHING.hours
    else
      start_matching = ServerTime.now.beginning_of_day + HOUR_START_MATCHING.hours
      end_matching = ServerTime.now.beginning_of_day.tomorrow + HOUR_END_MATCHING.hours
    end
    time >= start_matching && time <= end_matching
  end

  def cancel_apply
    self.update_columns(status_id: :rejected, is_staff_canceled: true)
    self.update_remained_apply_for_order_case
  end

  def create_backup_record
    generate_backup_record
    self.really_destroy!
  end

  def generate_backup_record
    BackupRecord.create(record_type: StaffApplyOrderCase.name, record_id: self.id,
      content: self.to_json)
  end

  def backup_and_update_remain_apply
    generate_backup_record
    order_case = self.order_case
    self.really_destroy!
    order_case.update_remained_apply
  end

  def update_order_case_staff_apply_count
    self.order_case.update_staff_apply_count
  end

  def soft_delete_staff_apply
    self.update_columns(is_cancel: true)
    self.destroy
  end

  def update_after_re_applying
    update_order_case_staff_apply_count
    update_remained_apply_for_order_case
    update_arrange_logs
    auto_matching_with_order_portion
  end

  def actual_applied_hour break_time
    (self.requested_working_time - break_time).to_f / Settings.time.seconds_per_minute
  end

  def get_break_time
    staff_time_request = self.requested_working_time
    original_time = self.order_branch.original_time_with_breaks.to_i
    break_time_start = self.requested_started_at +
      Settings.batch_arrange.block_time_3.hours
    if original_time <= BLOCK_TIME_1
      break_time_start = self.requested_started_at +
        Settings.batch_arrange.block_time.hours
      break_time_end = break_time_start + self.order_branch.break_time.minutes
      break_time_start = break_time_end = nil if break_time_start >= break_time_end ||
        break_time_end >= self.requested_ended_at
    elsif original_time > BLOCK_TIME_1 && staff_time_request <= BLOCK_TIME_1
      break_time_start = break_time_end = nil
    elsif staff_time_request > BLOCK_TIME_1 && staff_time_request < BLOCK_TIME_2
      break_time_end = break_time_start +
        (staff_time_request - BLOCK_TIME_1).minutes
    elsif staff_time_request >= BLOCK_TIME_2 && staff_time_request <= BLOCK_TIME_3
      break_time_end = break_time_start + Settings.order.break_time_at_limit_1.minutes
    elsif staff_time_request > BLOCK_TIME_3 && staff_time_request < BLOCK_TIME_4
      break_time_end = break_time_start +
        (staff_time_request - Settings.order.working_time_limit_2).minutes
    elsif staff_time_request >= BLOCK_TIME_4
      break_time_end = break_time_start + Settings.batch_arrange.block_time.hour
    end
    {break_time_start: break_time_start, break_time_end: break_time_end}
  end

  def is_unavailable?
    self.is_cancel? || self.rejected? || self.arrangement&.is_unavailable?
  end

  private

  def order_case_time_rule
    return if self.is_used_original_condition

    errors.add :requested_started_at, :order_case_time if
      requested_started_at.present? && requested_started_at < self.order_case.case_started_at
    errors.add :requested_ended_at, :order_case_time if
      requested_ended_at.present? && requested_ended_at > self.order_case.case_ended_at
  end

  def validate_student_night_job
    case_started_at = self.order_case.case_started_at
    staff_age = staff.age_by_time(case_started_at)
    return if staff_age >= AGE_18 || self.arrangement&.migrated_at

    if self.is_used_original_condition
      errors.add :is_used_original_condition, :student_night_job if
        CheckDayTimeWorking.new(case_started_at,
          self.order_case.case_ended_at).is_student_night_working?
      return
    end
    errors.add :requested_started_at, :student_night_job if
      requested_started_at.present? && CheckDayTimeWorking.is_student_night_hour?(
        requested_started_at.strftime(Settings.time.formats)
      )
    errors.add :requested_ended_at, :student_night_job if
      requested_ended_at.present? && CheckDayTimeWorking.is_student_night_hour?(
        requested_ended_at.strftime(Settings.time.formats)
      )
  end

  def cal_requested_working_time
    if self.is_used_original_condition
      self.requested_working_time = 0
    else
      diff = (self.requested_ended_at.to_i - self.requested_started_at.to_i) / 60
      self.requested_working_time = diff
    end
  end

  def set_transportation_fee
    if order_case.segment_trainning
      self.requested_transportation_fee = Settings.arrange_payment.trainning_transportation_fee
    else
      return unless self.requested_transportation_fee && payment_unit_price

      limit_fee = payment_unit_price[staff.transportation_limit_field].to_i
      self.requested_transportation_fee = limit_fee if self.requested_transportation_fee > limit_fee
    end
  end

  def payment_unit_price
    @payment_unit_price ||= PaymentUnitPrice.by_prefecture_and_national(location.prefecture_id,
      staff.nationality != Type::JAPAN_ID).by_effective_date(order_case.case_started_at.to_date)
      .first
  end

  def check_request_time_changable
    return unless self.is_used_original_condition

    self.requested_started_at = nil
    self.requested_ended_at = nil
  end

  def formatted_weekday_created_date_time
    format_full_weekday created_at
  end

  def format_full_weekday datetime
    return if datetime.blank?

    I18n.l datetime, format: Settings.datetime.full_weekday_format
  end
end
