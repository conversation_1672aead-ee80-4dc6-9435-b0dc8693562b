class WorkAchievement < ApplicationRecord
  acts_as_paranoid
  include CalculateTrigger
  include TimeUtil

  after_save :update_first_approve_fields, :update_staff_hire_date, :update_staff_last_working_day,
    :remove_notification_after_approved

  REST_TIME_RANGES = 1..3
  HOUR_IN_DAY = 24
  MAX_EXPIRED_DAYS = 45
  SECOND_IN_HOUR = 3600
  SECOND_PER_MINUTE = 60
  OWNER_DAILY_JOB_HOUR = 18
  attr_accessor :updatable, :staff_rest1_editable, :staff_rest2_editable, :staff_rest3_editable,
    :owner_rest1_edit, :owner_rest2_edit, :owner_rest3_edit, :owner_update, :has_break,
    :record_locked, :create_by_arrangement, :rest1_editable, :rest2_editable, :rest3_editable,
    :order_branch_start_date, :is_migrate_process

  COPY_ATTRS = %w(working_started_at working_ended_at rest1_started_at rest1_ended_at
    rest2_started_at rest2_ended_at rest3_started_at rest3_ended_at break_time)
  UPDATE_ATTRS = %w(staff_working_started_at staff_working_ended_at
    staff_rest1_started_at staff_rest1_ended_at updater_id
    staff_rest2_started_at staff_rest2_ended_at staff_rest3_started_at
    staff_rest3_ended_at staff_break_time staff_comment updatable
    staff_rest1_editable staff_rest2_editable staff_rest3_editable is_added_calendar
    owner_rest1_started_at owner_rest1_ended_at owner_rest2_started_at owner_rest2_ended_at
    owner_rest3_started_at owner_rest3_ended_at owner_break_time owner_comment
    owner_rest1_edit owner_rest2_edit owner_rest3_edit owner_update has_break
    owner_working_started_at owner_working_ended_at)
  APPROVED_WORKING_TIME_STT_IDS = %w(1 2 3 4)
  APPROVED_WORKING_TIME_STT_IDS_INCLUDE_ABSENCE = APPROVED_WORKING_TIME_STT_IDS + %w(8)
  NOT_INPUTTED_CONFIRMING_STT_IDS = %w(0 5 6 7)
  NOT_INPUTTED_OWNER_CONFIRMING_STT_IDS = %w(0 6)
  OP_APPROVED_STATUS = %i(op_center_approved auto_approved absence)
  WORKING_TIME_STATUSES = {
    not_inputted: "not_inputted",
    staff_approved: "staff_approved",
    owner_approved: "owner_approved",
    op_center_approved: "op_center_approved",
    auto_approved: "auto_approved",
    staff_confirming: "staff_confirming",
    owner_confirming: "owner_confirming",
    op_center_confirming: "op_center_confirming",
    absence: "absence"
  }
  APPROVED_WORKING_TIME_STATUS = %w(staff_approved owner_approved op_center_approved auto_approved)
  REST_TIMES = %w(1 2 3)
  TYPES = %w(staff owner)
  TYPES_WITH_UNDERSCORE = ["staff_", "owner_", ""]
  ARRANGE_UPDATE_ATTRS = %i(working_started_at working_ended_at rest1_started_at rest1_ended_at
    rest2_started_at rest2_ended_at rest3_started_at rest3_ended_at)
  BREAK_TIME_ATTRS = %i(rest1_started_at rest1_ended_at rest2_started_at rest2_ended_at
    rest3_started_at rest3_ended_at break_time)

  FIRST_APPROVE_FIELDS = %w(working_started_at working_ended_at rest1_started_at rest1_ended_at
    rest2_started_at rest2_ended_at rest3_started_at rest3_ended_at break_time)

  TRIGGER_FIELDS = %i(working_started_at working_ended_at rest1_started_at rest1_ended_at
    rest2_started_at rest2_ended_at rest3_started_at rest3_ended_at)
  INCLUDE_TABLES = [arrangement: [order_case: [order: :location], staff: :account]]
  JSON_METHODS = %i(working_month_date location_name order_case_id staff_account_name
    arrangement_working_time arrangement_actual_break_time actual_break_time
    staff_input_working_time staff_break_time_warning)
  STAFF_WORKING_ATTRS = %i(staff_working_started_at staff_working_ended_at staff_rest1_started_at
    staff_rest1_ended_at staff_rest2_started_at staff_rest2_ended_at staff_rest3_started_at
    staff_rest3_ended_at staff_comment)
  JSON_ONLY_ATTRS = %i(id working_time_status_id arrangement_id) + STAFF_WORKING_ATTRS +
    ARRANGE_UPDATE_ATTRS

  belongs_to :arrangement
  belongs_to :staff, optional: true
  has_many :work_achievement_logs
  delegate :is_migrated, to: :arrangement, allow_nil: true

  validates :staff_working_started_at, :staff_working_ended_at, presence: true, if: :updatable
  validates :staff_working_ended_at, value_greater_than: :staff_working_started_at, if: :updatable
  validates :owner_working_started_at, :owner_working_ended_at, presence: true, if: :owner_update
  validates :owner_working_ended_at, value_greater_than: :owner_working_started_at,
    if: :owner_update
  validates :owner_rest1_started_at, presence: true,
    if: proc{|c| c.owner_update && c.has_break && (c.owner_rest2_edit || c.owner_rest1_ended_at)}
  validates :owner_rest1_ended_at, presence: true,
    if: proc{|c| c.owner_update && c.has_break && (c.owner_rest2_edit || c.owner_rest1_started_at)}
  validates :owner_rest2_started_at, :owner_rest2_ended_at, presence: true,
    if: proc{|c| c.owner_update && c.has_break && c.owner_rest2_edit}
  validates :owner_rest3_started_at, :owner_rest3_ended_at, presence: true,
    if: proc{|c| c.owner_update && c.has_break && c.owner_rest3_edit}
  validates :staff_rest1_ended_at, value_greater_than: :staff_rest1_started_at
  validates :staff_rest2_ended_at, value_greater_than: :staff_rest2_started_at
  validates :staff_rest3_ended_at, value_greater_than: :staff_rest3_started_at
  validates :owner_rest1_ended_at, value_greater_than: :owner_rest1_started_at
  validates :owner_rest2_ended_at, value_greater_than: :owner_rest2_started_at
  validates :owner_rest3_ended_at, value_greater_than: :owner_rest3_started_at
  validates :rest1_ended_at, value_greater_than: :rest1_started_at
  validates :rest2_ended_at, value_greater_than: :rest2_started_at
  validates :rest3_ended_at, value_greater_than: :rest3_started_at
  validates :working_ended_at, value_greater_than: :working_started_at
  validates :working_ended_at, :working_started_at, :arrangement_id, presence: true
  validate :rest_time_range_rule, :rest_time_overlap_rule
  validates :rest1_started_at, presence: true,
    if: proc{|c| c.rest1_ended_at || rest2_started_at || rest2_editable}
  validates :rest1_ended_at, presence: true,
    if: proc{|c| c.rest1_started_at || rest2_started_at || rest2_editable}
  validates :rest2_started_at, presence: true,
    if: proc{|c| c.rest2_ended_at || (rest1_started_at && c.rest3_started_at) || rest2_editable}
  validates :rest2_ended_at, presence: true,
    if: proc{|c| c.rest2_started_at || (rest1_started_at && c.rest3_started_at) || rest2_editable}
  validates :rest3_started_at, presence: true, if: proc{|c| c.rest3_ended_at || rest3_editable}
  validates :rest3_ended_at, presence: true, if: proc{|c| c.rest3_started_at || rest3_editable}
  validates :staff_comment, :owner_comment,
    length: {maximum: Settings.maxlength.work_achievement.comment}
  validates :record_locked, record_locked: true, if: :is_locked?
  before_validation :set_working_started_at, :set_working_ended_at, :set_rest_time_date

  enum :working_time_status_id, {not_inputted: 0, staff_approved: 1, owner_approved: 2,
    op_center_approved: 3, auto_approved: 4, staff_confirming: 5, owner_confirming: 6,
    op_center_confirming: 7, absence: 8}
  enum :updater_type_id, {staff: 1, owner: 2, admin: 3, auto: 4}

  delegate :staff_account_name, :order_location_id, :order_location_name, to: :arrangement, allow_nil: true

  scope :inputted, -> do
    where.not(working_time_status_id: self.working_time_status_ids[:not_inputted])
  end
  scope :notif_input_work_achievement, -> do
    where(working_time_status_id: [:not_inputted, :staff_confirming])
      .where("work_achievements.working_ended_at < ?", ServerTime.now)
      .joins(arrangement: :order_portion).where("order_portions.status_id = ?",
        OrderPortion.status_ids[:arranged])
  end

  scope :public_by_owner_location, -> do
    joins(arrangement: [:order_portion, :order_case])
      .where("order_portions.status_id = ?", OrderPortion.status_ids[:arranged])
      .where("order_cases.status_id != ?", OrderCase.status_ids[:cancel])
      .where("order_cases.working_status_id = ?", OrderCase::PUBLIC_WORKING_STATUS_ID)
      .where.not(working_time_status_id: APPROVED_WORKING_TIME_STT_IDS)
      .where(arrangements: {is_prepared: true, is_arrived: true})
      .where("order_cases.case_ended_at < ?", ServerTime.now.beginning_of_day + OWNER_DAILY_JOB_HOUR.hours)
  end

  scope :auto_approved_by_owner_location, -> do
    joins(:work_achievement_logs)
      .where(working_time_status_id: WorkAchievement.working_time_status_ids[:auto_approved])
      .where("work_achievement_logs.action_type = ? OR
        (work_achievement_logs.action_type = ? AND work_achievement_logs.log_data = ?)",
        WorkAchievementLog.action_types[:auto_approve],
        WorkAchievementLog.action_types[:op_update], "auto_approved")
      .where("work_achievement_logs.created_at < ? AND work_achievement_logs.created_at >= ?",
        ServerTime.now.beginning_of_day + OWNER_DAILY_JOB_HOUR.hours,
        ServerTime.now.beginning_of_day - 1.day + OWNER_DAILY_JOB_HOUR.hours)
      .distinct
  end

  scope :past_one_month, -> do
    where.not(working_time_status_id: :absence).where("work_achievements.working_started_at >= ?",
      ServerTime.now.to_date - 1.month)
  end

  scope :by_arrangement_id, ->(arrangement_ids) do
    where(arrangement_id: arrangement_ids)
  end

  scope :not_approved, ->{where.not(working_time_status_id: APPROVED_WORKING_TIME_STT_IDS)}
  scope :approved, ->{where(working_time_status_id: APPROVED_WORKING_TIME_STT_IDS)}
  scope :started, ->{where("work_achievements.working_started_at < ?", ServerTime.now)}
  scope :by_arrangement_ids, ->(arrangement_ids) do
    where(arrangement_id: arrangement_ids)
  end

  scope :by_date, ->date do
    by_date_range(date.beginning_of_day, date.end_of_day)
  end

  scope :by_week, ->(start_week, end_week) do
    by_date_range(start_week.beginning_of_day, end_week.end_of_day)
  end

  scope :by_date_range, ->from_date, to_date do
    where("work_achievements.working_started_at BETWEEN ? AND ?",
      from_date.beginning_of_day, to_date.end_of_day)
  end

  scope :by_staff, ->staff_id do
    joins(:arrangement).where(arrangements: {staff_id: staff_id})
  end

  scope :by_location_code, ->location_code do
    joins(arrangement: {order: :location}).where(locations: {code: location_code})
  end

  scope :by_location_code_or_id, ->location_id, location_code do
    if location_code.blank?
      by_location_ids(location_id)
    else
      by_location_code(location_code)
    end
  end

  scope :not_start, ->{where("work_achievements.working_started_at >= ?", ServerTime.now)}
  scope :still_arranging, ->{joins(:arrangement).where(arrangements: {display_status_id: :arranged})}
  scope :sort_by_date, ->{order("work_achievements.working_started_at ASC")}

  scope :in_range, ->(start_time, end_time) do
    joins(arrangement: :order_case)
      .where(order_cases: {segment_id: OrderCase::SHOW_HAKEN_SEGMENT})
      .where("arrangements.working_started_at >= ? AND arrangements.working_started_at <= ?",
        start_time, end_time)
  end

  scope :arrangement_in_range, ->(start_time, end_time, segment_calc = :default) do
    segment_ids = OrderCase::SEGMENT_ID_FOR_CALC[segment_calc]
    work_achievements = joins(arrangement: :order_case)
      .where("arrangements.working_started_at >= ? AND " \
        "arrangements.working_started_at <= ?", start_time, end_time)
    return work_achievements if segment_ids == :all

    work_achievements.where(order_cases: {segment_id: segment_ids})
  end

  scope :arrangement_working_time_lte, ->(end_time) do
    joins(arrangement: :order_case).where("arrangements.working_started_at <= ?", end_time)
      .where(order_cases: {segment_id: OrderCase::SEGMENT_ID_FOR_CALC[:default]})
  end

  scope :portion_arranged, -> do
    joins(arrangement: :order_portion).where("order_portions.status_id = ?",
      OrderPortion.status_ids[:arranged])
  end
  scope :work_experience_in_range, ->(start_time, end_time, segment_calc = :default) do
    approved.arrangement_in_range(start_time, end_time, segment_calc).portion_arranged
  end
  scope :inlate_in_range, ->(start_time, end_time, segment_calc = :default) do
    approved.joins(arrangement: [:order_case, :arrange_payment])
      .arrangement_in_range(start_time, end_time, segment_calc)
      .where("arrange_payments.payment_late_time > 0")
      .portion_arranged
  end

  scope :portion_arranged_in_range, ->(start_time, end_time) do
    portion_arranged.where("order_portions.case_started_at >= ? AND
      order_portions.case_started_at <= ?", start_time, end_time)
  end

  scope :leave_early_in_range, ->(start_time, end_time, segment_calc = :default) do
    approved.joins(arrangement: [:order_case, :arrange_payment])
      .arrangement_in_range(start_time, end_time, segment_calc)
      .where("arrange_payments.payment_leave_early_time > 0")
      .portion_arranged
  end

  scope :absence_in_range, ->(start_time, end_time, segment_calc = :default) do
    absence.arrangement_in_range(start_time, end_time, segment_calc)
  end

  scope :sum_actual_working_time, ->(start_time, end_time, segment_calc = :default) do
    approved.joins(arrangement: :arrange_payment)
      .arrangement_in_range(start_time, end_time, segment_calc)
      .select("SUM(arrange_payments.payment_actual_working_time) AS actual_time_count")
  end

  scope :total_actual_working_time, ->(end_time) do
    approved.joins(arrangement: :arrange_payment)
      .arrangement_working_time_lte(end_time)
      .select("SUM(arrange_payments.payment_actual_working_time) AS total_time_count")
  end

  scope :sum_working_time, ->(start_time, end_time, segment_calc = :default) do
    approved.joins(arrangement: :arrange_payment)
      .arrangement_in_range(start_time, end_time, segment_calc).group("arrangements.staff_id")
      .select("SUM(arrange_payments.payment_actual_working_time) AS time_count, " \
        "SUM(arrange_payments.payment_basic_time) AS basic_time_count, " \
        "SUM(arrange_payments.payment_ot1_time) AS ot1_time_count, " \
        "SUM(arrange_payments.payment_ot2_time) AS ot2_time_count, " \
        "SUM(arrange_payments.payment_night_time) AS night_time_count, " \
        "SUM(arrange_payments.payment_late_time) AS late_time_count, " \
        "SUM(arrange_payments.payment_leave_early_time) AS leave_early_time_count, " \
        "arrangements.staff_id")
  end

  scope :nearest, ->(limit_num) do
    joins(arrangement: :order_case)
      .where("order_cases.case_started_at < ?", ServerTime.now)
      .where(order_cases: {segment_id: OrderCase::SHOW_HAKEN_SEGMENT})
      .order("order_cases.case_started_at desc").limit(limit_num)
  end

  scope :not_absence, -> do
    where.not(working_time_status_id: self.working_time_status_ids[:absence])
  end

  scope :is_arranged, -> do
    joins(:arrangement).where("arrangements.display_status_id = ?",
      Arrangement.display_status_ids[:arranged])
  end

  scope :not_cancel, -> do
    joins(arrangement: :order_case).where("order_cases.status_id != ?",
      OrderCase.status_ids[:cancel])
  end

  scope :unlock_arrangement, -> do
    joins(:arrangement)
      .where(arrangements: {is_payroll_locked: false, is_billing_locked: false})
  end

  scope :not_expired, ->(to_time) do
    where("work_achievements.working_started_at BETWEEN ? AND ?",
      to_time - MAX_EXPIRED_DAYS.days, to_time)
  end

  scope :auto_approve_for_staff_at, ->(time_start) do
    staff_confirming.where("work_achievements.owner_inputted_at < ?",
      time_start - HOUR_IN_DAY.hours)
  end

  scope :auto_approve_for_owner_at, ->(time_start) do
    owner_confirming.where("work_achievements.staff_inputted_at < ?",
      time_start - HOUR_IN_DAY.hours)
  end

  scope :except_ids, ->ids{where.not(id: ids)}

  scope :working_in_3_months, -> do
    where(working_ended_at: (3.months.ago.beginning_of_day..ServerTime.now))
  end

  scope :by_working_time, ->start_time, end_time do
    where("work_achievements.working_started_at >= ? AND " \
      "work_achievements.working_started_at <= ?", start_time, end_time)
  end

  scope :of_organization, ->organization_id do
    joins(arrangement: :order).where(arrangements: {orders: {organization_id: organization_id}})
  end

  scope :arrived, -> do
    joins(:arrangement).where(arrangements: {is_prepared: true, is_arrived: true})
  end

  scope :haken, -> do
    joins(arrangement: :order)
      .where(arrangements: {orders: {order_segment_id: :haken}})
  end

  scope :by_location_ids, ->location_ids do
    joins(arrangement: :order)
      .where(arrangements: {orders: {location_id: location_ids}})
  end

  scope :not_inputted_owner_confirming, -> do
    where(working_time_status_id: NOT_INPUTTED_OWNER_CONFIRMING_STT_IDS)
  end

  scope :order_by, ->order_key, order_type do
    order(order_key => order_type)
  end

  scope :arrangement_started, -> do
    joins(:arrangement).where("arrangements.working_started_at <= ?", ServerTime.now)
  end

  scope :unskip_staff_violation, -> do
    joins(arrangement: :order).where(arrangements: {orders: {is_fixed_term_project: false, is_limited_day: false}})
  end

  scope :by_working_time_status_ids, ->statuses{where(working_time_status_id: statuses)}

  after_update :update_arrangement_data

  delegate :working_time, :working_time_format, :actual_break_time, to: :arrangement, prefix: true

  def working_time_by_type type = nil
    working_start = type.present? ? working_start_by_type(type) : working_started_at
    working_end = type.present? ? working_end_by_type(type) : working_ended_at
    return unless working_start

    next_day = I18n.t("staff.order_cases.next_day") if working_start.to_date < working_end.to_date
    "#{working_start.strftime(Settings.time.formats)}~#{next_day}#{working_end.strftime(Settings.time.formats)}"
  end

  def create_work_achievement_log action_type, addition_data = {}
    log_datas = {action_type: action_type.to_sym}
    case action_type.to_sym
    when :owner_input, :owner_change_staff_input, :owner_approve, :staff_approve
      log_datas[:user_id] = addition_data[:user_id]
    when :op_input, :op_approve, :op_update
      log_datas[:admin_id] = addition_data[:admin_id]
    end
    log_datas[:log_data] = addition_data[:log_data] if action_type.to_sym == :op_update
    work_achievement_logs.create(log_datas)
  end

  def original_working_time
    return arrangement_working_time_format if not_inputted?
    return arrangement.order_case.working_time_format if owner_confirming?
    return input_working_time_format("staff_") if staff_confirming?

    working_time_by_type
  end

  def input_working_time_format type
    return "" unless send("#{type}working_started_at").present? && send("#{type}working_ended_at").present?

    data_working_time = working_time_by_type type
    break_time_format = display_break_time_format type
    return data_working_time if break_time_format.blank?

    "#{data_working_time} (#{break_time_format})"
  end

  def display_break_time_format type
    REST_TIME_RANGES.map do |rest_time|
      rest_start = rest_started_at(type, rest_time)
      rest_end = rest_ended_at(type, rest_time)
      next unless rest_start

      start_at = rest_start.strftime(Settings.time.formats)
      end_at = rest_end.strftime(Settings.time.formats)
      "#{start_at}~#{end_at}"
    end.compact.join(", ")
  end

  def display_break_time type = ""
    REST_TIME_RANGES.map do |rest_time|
      rest_start = rest_started_at(type, rest_time)
      rest_end = rest_ended_at(type, rest_time)
      next unless rest_start

      start_at = rest_start.strftime(Settings.time.formats)
      end_at = rest_end.strftime(Settings.time.formats)
      "#{start_at}~#{end_at}#{break_time_by_minutes rest_start, rest_end}"
    end.compact.join(I18n.t("common.comma"))
  end

  def break_time_by_minutes start_at, end_at
    count = count_break_time(start_at, end_at)
    I18n.t("corporation.order_case.order_case_detail.display_break_time", count: count)
  end

  def count_break_time start_at, end_at
    (end_at - start_at).to_i / SECOND_PER_MINUTE
  end

  def can_approve_working_time? staff_id
    self.arrangement.staff_id == staff_id && self.staff_confirming?
  end

  def staff_can_edit_rest_time? staff_id
    self.arrangement.staff_id == staff_id && self.not_inputted? &&
      self.arrangement.started?
  end

  def copy_working_data target_type
    COPY_ATTRS.each{|attr| self[attr] = self.send("#{target_type}_#{attr}")}
  end

  def assign_break_time target_type
    rest_time = 0
    REST_TIME_RANGES.each do |rest_number|
      start_time = rest_started_at(target_type, rest_number)
      end_time = rest_ended_at(target_type, rest_number)
      next unless start_time && end_time

      rest_time +=
        end_time >= start_time ? end_time - start_time : end_time + HOUR_IN_DAY.hours - start_time
    end
    self["#{target_type}break_time"] = rest_time.minutes / SECOND_IN_HOUR
  end

  def actual_break_time
    (1..3).map do |index|
      next unless self.send("rest#{index}_started_at")

      [self.send("rest#{index}_started_at").strftime(Settings.time.formats),
        self.send("rest#{index}_ended_at").strftime(Settings.time.formats)].join("～")
    end.compact.join(I18n.t("common.comma"))
  end

  def is_approved?
    self.working_time_status_id.in?(APPROVED_WORKING_TIME_STATUS)
  end

  def is_onwer_or_op_center_confirming?
    self.owner_confirming? || self.op_center_confirming?
  end

  def can_review_location?
    is_onwer_or_op_center_confirming? || is_approved?
  end

  def rest_started_at type, rest_time
    self["#{type}rest#{rest_time}_started_at"]
  end

  def rest_ended_at type, rest_time
    self["#{type}rest#{rest_time}_ended_at"]
  end

  def working_start_by_type type
    self["#{type}working_started_at"]
  end

  def working_end_by_type type
    self["#{type}working_ended_at"]
  end

  def working_type_by_status
    self.staff_confirming? ? TYPES_WITH_UNDERSCORE[1] : TYPES_WITH_UNDERSCORE[0]
  end

  def self.option_for_select_status_ids
    WorkAchievement.working_time_status_ids.keys.map do |status|
      {
        key: status,
        key_i18n: I18n.t("enum_label.work_achievement.working_time_status_ids")[status.to_sym]
      }
    end.to_json
  end

  REST_TIMES.each do |time|
    define_method "rest#{time}_time" do
      return unless rest_started_at("", time).present? && rest_ended_at("", time).present?

      next_day = I18n.t("staff.order_cases.next_day") if rest_started_at("", time)
        .strftime(Settings.date.formats) < rest_ended_at("", time).strftime(Settings.date.formats)
      "#{rest_started_at('', time).strftime(Settings.time.formats)}~#{next_day}#{rest_ended_at('', time).strftime(Settings.time.formats)}"
    end
  end

  def is_locked?
    return false if self.is_migrate_process

    arr = self.arrangement
    return false unless arr&.locked_started_at

    arr.locked_ended_at.blank? || arr.locked_started_at > arr.locked_ended_at
  end

  def can_update_working_status_id? target_status
    return false unless self.arrangement.order_portion.status_id.in? %w(arranged
      cancel_after_arrange_has_insurance)

    arrangement_started = self.arrangement.started?
    case target_status
    when WORKING_TIME_STATUSES[:not_inputted]
      staff_working_started_at.blank? && owner_working_started_at.blank?
    when WORKING_TIME_STATUSES[:owner_confirming]
      return arrangement_started if self.op_center_confirming?

      staff_working_started_at.present? && arrangement_started
    when WORKING_TIME_STATUSES[:staff_confirming]
      owner_working_started_at.present? && arrangement_started
    when WORKING_TIME_STATUSES[:op_center_confirming], *APPROVED_WORKING_TIME_STATUS
      arrangement_started
    else
      true
    end
  end

  def count_working_time
    (self.working_ended_at - self.working_started_at).to_i
  end

  def predetermined_except_break_time
    count_working_time - (self.break_time * SECOND_PER_MINUTE)
  end

  def actual_working_time_format
    minutes_to_times(predetermined_except_break_time / Settings.time.minutes_per_hour)
  end

  def break_time_format
    minutes_to_times(self.break_time)
  end

  def actual_working_time
    predetermined_except_break_time.to_f / SECOND_IN_HOUR
  end

  def is_night_working?
    CheckDayTimeWorking.new(self.working_started_at,
      self.working_ended_at).is_night_working?
  end

  def trigger_arrange_data
    self.calculate_trigger :work_achievement, %i(rest_field)
  end

  def update_order_case_working_status
    self.arrangement.order_case.update_working_status_follow_work_achievement
  end

  def working_month_date
    I18n.l self.working_started_at, format: Settings.date.month_date
  end

  def location_name
    self.arrangement.order_case.location_name
  end

  def order_case_id
    self.arrangement.order_case_id
  end

  def staff_input_working_time
    self.working_time_by_type TYPES_WITH_UNDERSCORE[0]
  end

  class << self
    def working_time_status_options
      working_time_status_ids.map do |key, val|
        {
          id: val,
          key: key,
          name: I18n.t("enum_label.work_achievement.working_time_status_ids.#{key}")
        }
      end
    end
  end

  def break_time_warning? type
    working_start = working_start_by_type(type)
    working_end = working_end_by_type(type)
    return if working_start.blank? || working_end.blank?

    time_diff = (working_end - working_start).to_i / Settings.time.minutes_per_hour
    return if time_diff == 0

    time_diff += Settings.time.minutes_per_day if time_diff < 0
    working_time = time_diff - self["#{type}break_time"].to_i
    over_working_time_limit_rule working_time, type
  end

  def actual_working_time_warning? type
    working_start = working_start_by_type(type)
    working_end = working_end_by_type(type)
    return if working_start.blank? || working_end.blank?

    time_diff = (working_end - working_start).to_i / Settings.time.minutes_per_hour
    working_time = time_diff - self["#{type}break_time"].to_i
    return true if working_time.zero?

    false
  end

  def staff_break_time_warning
    self.break_time_warning? TYPES_WITH_UNDERSCORE[0]
  end

  def update_working_time_follow_arrangement
    FIRST_APPROVE_FIELDS.each do |attr|
      self[attr] = self["staff_#{attr}"] = arrangement[attr]
    end
    self.save
    self.trigger_arrange_data
  end

  def self.arranged_dates_by_staff_and_organization staff_id, organization_id
    self.haken.not_approved.by_staff(staff_id).still_arranging.sort_by_date
      .of_organization(organization_id).unskip_staff_violation
      .pluck(:working_started_at).map(&:to_date)
  end

  def self.worked_dates_by_staff_and_organization staff_id, organization_id
    self.haken.approved.by_staff(staff_id).still_arranging.sort_by_date
      .of_organization(organization_id).unskip_staff_violation
      .pluck(:working_started_at).map(&:to_date)
  end

  def self.arranged_dates_by_staff_and_location_code_or_id staff_id, location_id, location_code
    self.haken.not_approved
      .still_arranging
      .by_staff(staff_id)
      .sort_by_date.unskip_staff_violation
      .by_location_code_or_id(location_id, location_code)
      .pluck(:working_started_at).map(&:to_date)
  end

  def self.worked_dates_by_staff_and_location_code_or_id staff_id, location_id, location_code
    self.haken.approved
      .still_arranging
      .by_staff(staff_id)
      .sort_by_date.unskip_staff_violation
      .by_location_code_or_id(location_id, location_code)
      .pluck(:working_started_at).map(&:to_date)
  end

  private

  def set_rest_time_date
    TYPES_WITH_UNDERSCORE.each do |type|
      REST_TIMES.each do |rest_time|
        start_time = rest_started_at(type, rest_time)&.strftime(Settings.time.formats)
        end_time = rest_ended_at(type, rest_time)&.strftime(Settings.time.formats)
        start_at = ServerTime.parse("#{working_date(type)} #{start_time}") if start_time
        end_at = ServerTime.parse("#{working_date(type)} #{end_time}") if end_time
        next unless start_at && end_at

        self["#{type}rest#{rest_time}_started_at"] = start_at
        self["#{type}rest#{rest_time}_ended_at"] = end_at
        working_start_by_type = working_start_by_type(type)
        next unless working_start_by_type

        if start_at < working_start_by_type
          start_at = self["#{type}rest#{rest_time}_started_at"] = start_at + 1.day
          end_at = self["#{type}rest#{rest_time}_ended_at"] = end_at + 1.day
        end
        self["#{type}rest#{rest_time}_ended_at"] = end_at + 1.day if end_at < start_at
      end
    end
  end

  def working_date type
    date = order_branch_start_date || self.arrangement.order_case.case_started_at
    start_time_str = date.strftime(Settings.time.formats)
    input_time_str = working_start_by_type(type)&.strftime(Settings.time.formats)
    return date.strftime(Settings.date.formats) unless input_time_str

    if input_time_str > start_time_str &&
      (start_time_str.to_time - input_time_str.to_time.yesterday) <= SECOND_IN_HOUR * 2
      date -= 1.day
    elsif input_time_str < start_time_str &&
      (start_time_str.to_time - input_time_str.to_time) > SECOND_IN_HOUR * 2
      date += 1.day
    end
    date.strftime(Settings.date.formats)
  end

  def set_working_started_at
    TYPES_WITH_UNDERSCORE.each do |type|
      next unless working_start_by_type(type)

      start_time = working_start_by_type(type).strftime(Settings.time.formats)
      self["#{type}working_started_at"] = ServerTime.parse("#{working_date(type)} #{start_time}")
    end
  end

  def set_working_ended_at
    TYPES_WITH_UNDERSCORE.each do |type|
      next if working_end_by_type(type).blank? || working_start_by_type(type).blank?

      end_time = working_end_by_type(type).strftime(Settings.time.formats)
      self["#{type}working_ended_at"] = ServerTime.parse("#{working_date(type)} #{end_time}")
      self["#{type}working_ended_at"] += 1.day if working_end_by_type(type) <
        working_start_by_type(type)
    end
  end

  def rest_time_range_rule
    TYPES_WITH_UNDERSCORE.each do |type|
      next unless working_end_by_type(type)

      REST_TIMES.each do |rest_time|
        start_time = rest_started_at(type, rest_time)
        end_time = rest_ended_at(type, rest_time)
        if start_time && start_time >= working_end_by_type(type)
          errors.add "#{type}rest#{rest_time}_started_at", :rest_time_in_range
        elsif end_time && end_time > self["#{type}working_ended_at"]
          errors.add "#{type}rest#{rest_time}_ended_at", :rest_time_in_range
        end
      end
    end
  end

  def rest_time_overlap_rule
    check_overlap REST_TIMES[1], REST_TIMES[0]
    check_overlap REST_TIMES[2], REST_TIMES[0]
    check_overlap REST_TIMES[2], REST_TIMES[1]
  end

  def check_overlap rest_time, compare_time
    TYPES_WITH_UNDERSCORE.each do |type|
      rest_time_start = rest_started_at(type, rest_time)
      rest_time_end = rest_ended_at(type, rest_time)
      compare_time_start = rest_started_at(type, compare_time)
      compare_time_end = rest_ended_at(type, compare_time)
      next if compare_time_start.blank? || compare_time_end.blank? ||
        rest_time_start.blank? || rest_time_end.blank?

      if (rest_time_start >= compare_time_start && rest_time_start < compare_time_end) ||
        (compare_time_start >= rest_time_start && compare_time_start < rest_time_end)
        errors.add "#{type}rest#{rest_time}_started_at", :rest_time_overlap
      end
    end
  end

  def working_started_at_format
    I18n.l self.working_started_at, format: Settings.date.day_and_date
  end

  def over_working_time_limit_rule working_time, type
    (working_time > Settings.order.working_time_limit_1 &&
      working_time <= Settings.order.working_time_limit_2 &&
      self["#{type}break_time"].to_i < Settings.order.break_time_at_limit_1) ||
      (working_time > Settings.order.working_time_limit_2 &&
      self["#{type}break_time"].to_i < Settings.order.break_time_at_limit_2)
  end

  def error_field type
    rest_time_field = "#{type}rest1_started_at"
    REST_TIMES.each do |rest_time|
      start_time = self["#{type}rest#{rest_time}_started_at"]
      end_time = self["#{type}rest#{rest_time}_ended_at"]
      rest_time_field = "#{type}rest#{rest_time}_started_at" if start_time.present? &&
        end_time.present?
    end
    rest_time_field.to_sym
  end

  def update_first_approve_fields
    return if self.first_approved_working_started_at || self.is_migrated
    return unless APPROVED_WORKING_TIME_STATUS.include?(self.working_time_status_id)

    FIRST_APPROVE_FIELDS.each do |approve_field|
      self["first_approved_#{approve_field}"] = self.send(approve_field)
    end
    self.first_approved_account_type = WorkAchievement.updater_type_ids[self.updater_type_id]
    self.first_approved_account_id = self.updater_id
    self.save(validate: false)
  end

  def update_arrangement_data
    return unless self.working_time_status_id_changed? && self.is_approved? &&
      !self.create_by_arrangement

    self.arrangement.update_columns(is_prepared: true, is_arrived: true)
    self.arrangement.calculation_arrangement_data
  end

  def update_staff_hire_date
    staff = self.arrangement.staff
    return unless self.is_approved? && staff && !staff.hire_date

    staff.update_column(:hire_date, self.working_started_at)
  end

  def update_staff_last_working_day
    return unless self.working_time_status_id_changed? && self.is_approved?

    staff = self.arrangement.staff
    staff.update_column(:last_working_day, self.working_started_at) if staff.last_working_day.blank? ||
      self.working_started_at.to_date > staff.last_working_day
  end

  def remove_notification_after_approved
    return unless self.working_time_status_id_changed? && self.is_approved?

    StaffNotification.rm_notif_confirm_work_achievement(self.arrangement.staff_id,
      self.arrangement.order_case_id)
  end
end
