class Station < ApplicationRecord
  acts_as_paranoid

  belongs_to :railway_line, optional: true

  validates :name, :latitude, :longitude, presence: true

  scope :with_name_like, ->(name){where("name like ?", "%#{name}%")}
  scope :of_railway_line, ->line_id{where railway_line_id: line_id}
  scope :of_prefecture, ->prefecture_id{where prefecture_id: prefecture_id.split(",")}

  def full_name
    "#{self.name} (#{self.railway_line&.name})"
  end

  def name_with_railway_line
    if self.railway_line
      "#{self.railway_line.name}#{self.name}#{I18n.t('staff.order_cases.station')}"
    else
      "#{I18n.t('staff.order_cases.line')}#{self.name}#{I18n.t('staff.order_cases.station')}"
    end
  end

  def station_name
    return unless self.name

    "#{self.name}#{I18n.t('staff.order_cases.station')}"
  end
end
