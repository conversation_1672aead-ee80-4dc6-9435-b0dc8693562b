class Prefecture < ApplicationRecord
  acts_as_paranoid

  has_many :locations_with_selects, class_name: "Location"
  has_many :locations
  has_many :billing_unit_prices
  has_many :special_billing_unit_price_prefectures
  has_many :staffs
  has_many :departments, class_name: "Department", foreign_key: :pic_prefecture_id
  has_many :minimum_wages
  has_many :payment_unit_prices
  has_many :payment_rates
  has_many :option_payment_rates
  has_many :railway_lines
  belongs_to :region

  validates :name, presence: true

  scope :by_ids, ->(ids){where id: ids}

  class << self
    def options_for_select current_prefecture = nil
      where.not(id: current_prefecture).pluck :name, :id
    end

    def overview_address_selection
      pluck :name, :id
    end

    def ransackable_attributes _auth_object = nil
      %w(name)
    end
  end

  def allow_store_computer?
    id.in? Settings.prefectures_allow_store_computer
  end
end
