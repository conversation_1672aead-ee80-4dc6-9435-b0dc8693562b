class Arrangement < ApplicationRecord
  extend OrderAsSpecified

  acts_as_paranoid
  include ArrangementSearchDecorator
  include WorkAchievementConcernScope
  include ArrangementFormRecruiting
  include ArrangementInformationForm
  include ArrangementIndividualContractForm
  include ExportArrangementDecorator
  include DomainInfo

  attr_accessor :rest2_editable, :rest3_editable, :record_locked, :is_copy_portion, :staff_present,
    :is_migrate_process, :is_update_break_time

  ARRANGE_PAYMENT_ATTRS = %i(payment_unit_price_addition payment_total_amount
    payment_basic_amount payment_ot_amount payment_night_amount payment_field_1 payment_field_2
    payment_field_3 payment_field_4 payment_field_5 payment_subtraction_amount deduction_field_1
    deduction_field_2 payment_actual_working_time payment_basic_time payment_ot1_time
    payment_night_time payment_late_time payment_leave_early_time adjusment_type_id
    payment_basic_unit_price payment_night_unit_price payment_field_6 payment_field_7
    payment_field_8 payment_field_9 payment_field_10 payment_field_11 payment_field_12
    payment_field_13 payment_field_14 payment_field_15 payment_field_16 payment_field_17
    payment_field_18 payment_field_19 payment_field_20 payment_basic_break_time
    payment_night_break_time payment_urgent_unit_price_addition total_unit_price_addition
    payment_ot2_amount payment_ot_unit_price payment_bonus_unit_price)
  ARRANGE_BILLING_ATTRS = %i(billing_note billing_basic_unit_price billing_unit_price_addition
    billing_ot_unit_price billing_night_unit_price billing_total_amount billing_basic_amount
    billing_ot_amount billing_night_amount billing_field_1 billing_field_2
    billing_field_3 billing_field_4 billing_field_5 billing_field_6 billing_field_7 billing_tax_exemption
    billing_actual_working_time billing_basic_time billing_ot_time billing_night_time
    billing_late_time billing_leave_early_time billing_rest1_started_at billing_rest1_ended_at
    billing_rest2_started_at billing_rest2_ended_at billing_rest3_started_at billing_rest3_ended_at
    billing_field_8 billing_field_9 billing_field_10 billing_basic_break_time
    billing_night_break_time billing_other_addition_fee)
  WORK_ACHIEVEMENT_REST_ATTRS = %i(rest1_started_at rest1_ended_at rest2_started_at rest2_ended_at
    rest3_started_at rest3_ended_at working_time_status_id)
  REST_METHODS = %i(rest1_time rest2_time rest3_time)
  ADMIN_ARRANGEMENT_METHODS = [:order_id, :order_case_id, :break_time, :working_day,
    :working_time, :order_case_status, :order_portion_id,
    :billing_started_at, :billing_ended_at, :exist_history, :working_day_full_date,
    :order_created_day, :order_created_full_day] + ARRANGE_PAYMENT_ATTRS + ARRANGE_BILLING_ATTRS +
    [:work_achievement_rest1_started_at, :work_achievement_rest1_ended_at,
    :work_achievement_rest2_started_at, :work_achievement_rest2_ended_at,
    :work_achievement_rest3_started_at, :work_achievement_rest3_ended_at,
    :arrange_billing_adjusment_type_id, :status_staff_evaluation, :order_corporation_id,
    :order_corporation_full_name, :order_location_id, :location_name, :order_location_code,
    :order_location_stations_1_station_name, :order_order_segment_id, :staff_id, :order_location_job_categories_text,
    :working_need_talking, :working_has_problem, :work_achievement_working_time_status_id,
    :location_evaluation_val, :payment_start_time, :payment_end_time, :order_case_is_special_offer,
    :order_case_is_urgent, :staff_rank, :staff_level, :order_case_segment_id, :is_prepared,
    :is_arrived, :need_prepare_working, :over_working_start_time, :portion_arranged,
    :portion_cancel_after_arrange_has_insurance,
    :working_started_at, :working_ended_at, :payment_started_at, :payment_ot1_time_format,
    :payment_late_time_format, :payment_leave_early_time_format, :billing_ot_time_format,
    :billing_late_time_format, :billing_leave_early_time_format,
    :work_achievement_rest1_started_date, :work_achievement_rest1_ended_date,
    :work_achievement_rest2_started_date, :work_achievement_rest2_ended_date,
    :work_achievement_rest3_started_date, :work_achievement_rest3_ended_date,
    :work_achievement_rest1_time, :work_achievement_rest2_time, :work_achievement_rest3_time,
    :billing_started_date, :order_business_name, :order_pic_department_name,
    :order_real_pic_department_name, :staff_department_name_at_working_time,
    :staff_current_department_name, :order_location_pos_type_id,
    :order_location_is_store_parking_area_usable, :order_note, :order_case_special_offer_note,
    :staff_home_station_station_name, :payment_requested, :portion_canceled, :location_expired?,
    :new_location?, :old_location?, :deleted_user_order_pic?,
    :is_locked?, :finish_apply?, :time_start_edit_staff_working, :rank_by_date, :level_by_date,
    :payment_basic_time_format, :payment_actual_working_time_format, :is_regular_order,
    :payment_ot_night_time_format, :payment_ot_day_time_format, :is_payroll_locked,
    :is_billing_locked, :billing_basic_time_format, :billing_actual_working_time_format,
    :billing_night_time_format, :location_code, :is_time_changable, :available_for_recruiting,
    :required_start_time, :required_end_time, :is_lawson, :invoice_target,
    :order_case_training_session_text, :order_case_location_job_category_name]
  ADMIN_SUM_COLUMNS = [:working_time, :billing_actual_working_time, :billing_basic_time,
    :billing_ot_time, :billing_night_time, :billing_late_time, :billing_leave_early_time,
    :billing_total_amount, :billing_basic_amount, :billing_ot_amount, :billing_night_amount,
    :billing_field_1, :billing_field_2, :billing_field_3, :billing_field_4, :billing_field_5,
    :billing_field_6, :billing_field_7, :billing_tax_exemption, :payment_actual_working_time, :payment_night_time,
    :payment_late_time, :payment_leave_early_time, :payment_total_amount, :payment_basic_amount,
    :payment_ot_amount, :payment_night_amount, :payment_field_1, :payment_field_4, :payment_field_5,
    :payment_subtraction_amount, :order_case_status]
  ADMIN_PAYMENT_OT_TIME_SUM_COLUMNS = [:payment_ot1_time, :payment_ot2_time, :payment_ot_day_time,
    :payment_ot_night_time]
  TRIGGER_ARRANGEMENT_METHODS = [:billing_started_at, :billing_ended_at] + ARRANGE_PAYMENT_ATTRS +
    ARRANGE_BILLING_ATTRS + [:work_achievement_rest1_started_at, :work_achievement_rest1_ended_at,
    :work_achievement_rest2_started_at, :work_achievement_rest2_ended_at,
    :work_achievement_rest3_started_at, :work_achievement_rest3_ended_at, :payment_start_time,
    :payment_end_time, :payment_started_at, :payment_ot1_time_format,
    :payment_late_time_format, :payment_leave_early_time_format, :billing_ot_time_format,
    :billing_late_time_format, :billing_leave_early_time_format,
    :work_achievement_rest1_started_date, :work_achievement_rest1_ended_date,
    :work_achievement_rest2_started_date, :work_achievement_rest2_ended_date,
    :work_achievement_rest3_started_date, :work_achievement_rest3_ended_date,
    :work_achievement_rest1_time, :work_achievement_rest2_time, :work_achievement_rest3_time,
    :billing_started_date, :payment_ot_day_time_format, :payment_ot_night_time_format,
    :payment_basic_time_format, :payment_actual_working_time_format,
    :billing_basic_time_format, :billing_actual_working_time_format, :billing_night_time_format]
  STAFF_ARRANGEMENT_TB_FIELDS = [:id, :tel, :email, :home_tel, :gender_id,
    :total_work_experience, :level_up_training, :new_pos_training]
  STAFF_ARRANGEMENT_TB_METHODS = [:account_email, :account_name, :account_name_kana,
    :age, :uniform_size, :display_workable_time, :is_working_car, :insurance_subsection_label]
  COUNTER_FIELDS = %i(need_talking_count has_problem_count)
  UPDATABLE_ATTRS = [:rest1_started_at, :rest1_ended_at, :rest2_started_at, :rest2_ended_at,
    :rest3_started_at, :rest3_ended_at, :rest2_editable, :rest3_editable, :is_prepared,
    :is_arrived, :note, :working_started_at, :working_ended_at, :store_note,
    :arrange_comment] + COUNTER_FIELDS
  BREAK_TIME_ATTRS = %i(rest1_started_at rest1_ended_at rest2_started_at rest2_ended_at
    rest3_started_at rest3_ended_at break_time)
  ONLY_ATTRS = %i(id note order_case_id store_note arrange_comment order_id
    billing_payment_template_id) + BREAK_TIME_ATTRS
  BEFORE_WORK_TIME = 2.hours + 30.minutes
  REST_TIMES = %w(1 2 3)
  HOUR_IN_DAY = 24
  SECOND_IN_HOUR = 3600
  SECOND_PER_MINUTE = 60

  OP_CENTER_TARGET_STATUS = {
    type_public: %w(type_public type_limited type_hidden cancel),
    type_limited: %w(type_public type_limited type_hidden cancel),
    type_hidden: %w(type_public type_limited type_hidden cancel),
    finish_recruiting: %w(cancel),
    has_apply: %w(cancel),
    cancel: %w(type_public type_limited type_hidden),
    temporary_arrange: %w(arranged cancel absence type_public
      type_limited type_hidden temporary_arrange),
    arranged: %w(cancel_after_arrange_has_insurance cancel_after_arrange_no_insurance absence
      arranged),
    cancel_after_arrange_has_insurance: %w(temporary_arrange arranged
      cancel_after_arrange_no_insurance),
    absence: %w(arranged),
    cancel_after_arrange_no_insurance: %w(temporary_arrange arranged
      cancel_after_arrange_has_insurance)
  }
  DISABLE_DESTROY_STATUS = [:waiting_arrange, :has_staff]
  NOT_FINISH_RECRUITING_STATUSES = %w(recruiting_public recruiting_limited recruiting_hidden)
  MAPPING_DISPLAY_STATUS = {
    type_public: 1,
    type_limited: 2,
    type_hidden: 3,
    has_apply: 4,
    cancel: 5,
    temporary_arrange: 6,
    arranged: 7,
    cancel_after_arrange_has_insurance: 8,
    absence: 9,
    finish_recruiting: 10,
    cancel_after_arrange_no_insurance: 11,
    absence_has_alternative: 12
  }
  AVAILABLE_TO_EDIT_STATUS = NOT_FINISH_RECRUITING_STATUSES + %w(has_apply temporary_arrange arranged)
  NOT_ARRANGED_STATUS = AVAILABLE_TO_EDIT_STATUS - %w(arranged)
  MAPPING_DISPLAY_STATUS_EXPORT = %w(7 8 9)
  PREPARE_HOUR = 1.hour
  WORK_TIME_RANGE = 2.hours
  ARRANGED_DISPLAY_STATUS = "arranged"
  MAX_WORKING_HOUR = 12
  ARRANGE_TIME_VALID = 59
  CANCEL_STATUSES = %w(5 8 11)
  ABSENCE_STATUSES = %w(9 12)
  RECRUITING_STATUSES = %w(1 2 3 4 6)
  AVAILABLE_TEMP_ARRANGED_STATUSES = %w(1 2 3 4)
  BATCH_ARRANGED_DISPLAY_STATUS = %w(7)
  BATCH_ARRANGED_DISPLAY_STATUS_TEXT = %i(arranged)
  UNAVAILABLE_TRAINING_STATUSES = %w(cancel_after_arrange_has_insurance cancel_after_arrange_no_insurance
    absence absence_has_alternative)

  belongs_to :order
  belongs_to :order_case
  belongs_to :staff, optional: true
  belongs_to :staff_department, class_name: "Department", foreign_key: :staff_department_id, optional: true
  belongs_to :order_portion
  belongs_to :order_branch
  has_one :arrange_payment, dependent: :destroy
  has_one :work_achievement, dependent: :destroy
  has_one :staff_evaluation
  has_one :arrange_billing, dependent: :destroy
  has_many :arrange_payment_requests
  has_many :payment_requests, through: :arrange_payment_requests
  has_many :staff_apply_order_cases
  has_many :arrange_logs
  has_many :arrange_payment_logs, dependent: :destroy
  has_many :arrange_billing_logs, dependent: :destroy
  has_one :location_evaluation
  has_one :migration_arrangement_history, dependent: :destroy
  belongs_to :parent_arrangement, class_name: "Arrangement", foreign_key: :parent_id,
    optional: true
  belongs_to :admin, class_name: "Admin", foreign_key: :approved_by_admin_id, optional: true
  belongs_to :billing_payment_template, optional: true

  enum :display_status_id, {recruiting_public: 1, recruiting_limited: 2, recruiting_hidden: 3,
    has_apply: 4, cancel: 5, temporary_arrange: 6, arranged: 7,
    cancel_after_arrange_has_insurance: 8, absence: 9, finish_recruiting: 10,
    cancel_after_arrange_no_insurance: 11, absence_has_alternative: 12}
  enum :current_location_type, {normal_location: 0, new_location: 1, old_location: 3}

  before_validation :set_working_time, :set_rest_time_date, :set_break_time
  before_save :add_current_location_type, :update_current_staff_department_id

  scope :after_time, ->time{where("arrangements.working_started_at > ?", time)}
  scope :in_range_trigger, ->time{where("arrangements.working_started_at >= ?", time)}
  scope :arrived, ->{where(is_arrived: true)}
  scope :by_ids, ->ids do
    where(id: ids).order sanitize_sql_for_order([Arel.sql("FIELD(arrangements.id, ?)"), ids])
  end
  scope :in_range, ->(start_time, end_time) do
    where("arrangements.working_started_at >= ? AND arrangements.working_started_at <= ?",
      start_time, end_time)
  end
  scope :working_started_lte, ->time{where("arrangements.working_started_at <= ?", time)}
  scope :temp_arrange, -> do
    joins(:order_portion).where(order_portions: {status_id: :temporary_arrange})
  end
  scope :temp_arrange_by_ids, ->ids{temp_arrange.by_ids(ids)}
  scope :temporary_arrange_not_in_ids, ->(ids) do
    temp_arrange.where.not(id: ids)
  end

  scope :by_display_status, ->display_status do
    where(display_status_id: display_status)
  end

  scope :of_staff, ->staff_ids{where staff_id: staff_ids}

  scope :is_arranged, -> do
    joins(:order_portion).where("order_portions.status_id = ?", OrderPortion.status_ids[:arranged])
  end

  scope :comming_work, -> do
    joins(:order_portion).where("order_portions.status_id = ?", OrderPortion.status_ids[:arranged])
      .where("order_portions.case_ended_at > ?", ServerTime.now)
      .order("order_portions.case_ended_at ASC")
  end

  scope :staff_work_experience, ->(location_ids) do
    joins(:order_case, :order, :order_portion, :work_achievement)
      .select("orders.location_id as o_location_id, arrangements.staff_id, count(*) as total_exp")
      .where("arrangements.staff_id is NOT NULL AND
        order_portions.status_id = ? AND work_achievements.working_time_status_id in (?) AND
        orders.location_id in (?)", OrderPortion.status_ids[:arranged],
        WorkAchievement::APPROVED_WORKING_TIME_STT_IDS, location_ids)
      .where(order_cases: {segment_id: OrderCase::SHOW_HAKEN_SEGMENT})
      .group("orders.location_id", "arrangements.staff_id")
  end

  scope :by_order_segment, ->(order_segment_id) do
    joins(:order_case).where(order_cases: {segment_id: order_segment_id})
  end

  scope :failed_or_not_yet_request, -> do
    where.not(id: joins(arrange_payment_requests: :payment_request)
      .where.not(payment_requests: {process_status:
        PaymentRequest.process_statuses[:trade_failed]}))
  end

  scope :current_arrange_by_time_range, ->started_at, ended_at do
    where("working_started_at >= ?", started_at - 24.hours)
      .where("(:started_at BETWEEN working_started_at AND working_ended_at)" \
        "OR (:ended_at BETWEEN working_started_at AND working_ended_at)" \
        "OR ((working_started_at BETWEEN :started_at AND :ended_at)" \
        "AND (working_ended_at BETWEEN :started_at AND :ended_at))",
        started_at: started_at, ended_at: ended_at)
  end

  scope :by_month, ->(month) do
    date = Admin::AdminsController.helpers.get_month_the_first(ServerTime.now.to_s)
    case month
    when :prev_month
      start_date, end_date =
        Admin::AdminsController.helpers
          .salary_calculating_range((date.to_date - 1.month).to_date.to_s)
    when :current_month
      start_date, end_date =
        Admin::AdminsController.helpers.salary_calculating_range date
    end
    where working_started_at: start_date..end_date
  end

  scope :not_fail_requests, -> do
    joins(:payment_requests).where.not(payment_requests: {process_status: :trade_failed})
  end

  scope :skip_payment_request_id, ->payment_request_id do
    joins(:payment_requests).where.not(payment_requests: {id: payment_request_id})
  end

  scope :need_confirm_start_go_work_at, ->(start_time) do
    joins(:staff)
      .merge(Staff.working)
      .arranged.where("arrangements.working_started_at >= ?  " \
        "AND arrangements.working_started_at < ?",
        start_time + 2.hours, start_time + 2.hours + 30.minutes)
  end

  scope :need_input_working_time_at, ->(start_time) do
    max_time_input = start_time - Settings.arrangement.input_mail_lower_bound.minutes
    min_time_input = start_time - Settings.arrangement.input_mail_upper_bound.minutes + 1.second
    in_range(min_time_input - 1.day, max_time_input)
      .where("arrangements.working_ended_at BETWEEN ? AND ?", min_time_input, max_time_input)
  end

  scope :interval_need_input_working_time_at, ->(start_time) do
    max_time_input = start_time - Settings.arrangement.input_mail_lower_bound.minutes
    in_range(max_time_input - 1.day, max_time_input)
      .where("arrangements.working_ended_at <= ?", max_time_input)
  end

  scope :not_inputted, -> do
    not_inputted_status = WorkAchievement.working_time_status_ids[:not_inputted]
    joins(:work_achievement).where(work_achievements: {working_time_status_id: not_inputted_status})
  end

  scope :work_achievement_absence, -> do
    absence_status = WorkAchievement.working_time_status_ids[:absence]
    joins(:work_achievement).where(work_achievements: {working_time_status_id: absence_status})
  end

  scope :by_working_status, ->statuses do
    joins(:work_achievement).where(work_achievements: {working_time_status_id: statuses})
  end

  scope :already_worked_in_date_range, ->from_date, to_date do
    absence_status = WorkAchievement.working_time_status_ids[:absence]
    joins(:work_achievement).where.not(work_achievements: {working_time_status_id: absence_status})
      .where.not(staff_id: nil)
      .where("work_achievements.working_started_at BETWEEN ? AND ?",
        from_date.beginning_of_day, to_date.end_of_day)
  end

  scope :working_remind, -> do
    where("working_started_at >= ? AND working_started_at < ?",
      ServerTime.now.next_day.beginning_of_day,
      ServerTime.now.next_day.beginning_of_day + 1.day)
  end

  scope :by_order_case_id, ->order_case_id{where(order_case_id: order_case_id)}

  scope :by_prefecture, ->(prefecture_id) do
    joins(:order).where(orders: {location_prefecture_id: prefecture_id})
  end

  scope :by_corporation, ->(corporation_ids) do
    joins(:order).where(orders: {corporation_id: corporation_ids})
  end

  scope :by_location, ->(location_ids) do
    joins(:order).where(orders: {location_id: location_ids})
  end

  scope :without_corporation, ->(corporation_ids) do
    joins(:order).where.not(orders: {corporation_id: corporation_ids})
  end

  scope :without_location, ->(location_ids) do
    joins(:order).where.not(orders: {location_id: location_ids})
  end

  scope :great_than_time_now, -> do
    joins(:order_case).where("order_cases.case_ended_at >= ?", ServerTime.now)
  end

  scope :is_japanese, ->{joins(:staff).where(staffs: {nationality: Type::JAPAN_ID})}

  scope :is_nationality, ->{joins(:staff).where.not(staffs: {nationality: Type::JAPAN_ID})}

  scope :locked, -> do
    where("arrangements.locked_started_at > arrangements.locked_ended_at OR " \
      "(arrangements.locked_started_at IS NOT NULL AND arrangements.locked_ended_at IS NULL)")
  end

  scope :payroll_locked, ->{where(is_payroll_locked: true)}
  scope :not_locked_payroll, ->{where(is_payroll_locked: false)}

  scope :not_locked, -> do
    where("(locked_ended_at > locked_started_at) OR
      (locked_started_at IS NULL AND locked_ended_at IS NULL)")
  end

  scope :locked_of_payment_requests, ->payment_request_ids do
    joins(:arrange_payment_requests)
      .where("arrange_payment_requests.payment_request_id in (?)", payment_request_ids)
      .locked
  end

  scope :belongs_to_location, ->(location_id) do
    joins(:order).where(orders: {location_id: location_id})
  end

  scope :worked_for_location, ->(location_id) do
    arranged.belongs_to_location(location_id)
  end

  scope :not_arrange, -> do
    statuses = OrderPortion::ARRANGE_AND_CANCEL_AFTER_ARRANGE_STTS
    joins(:order_portion).where.not(order_portions: {status_id: statuses})
  end

  scope :by_order_portions_ids, ->order_portion_ids{where(order_portion_id: order_portion_ids)}

  scope :not_temporary_arrange, -> do
    joins(:order_portion)
      .where("order_portions.status_id != ?", OrderPortion.status_ids[:temporary_arrange])
  end

  scope :order_working_started_at, ->{order(working_started_at: :asc)}

  scope :training_segment, -> do
    joins(:order_case).where(order_cases: {segment_id: OrderCase::TRAINING_SEGMENTS})
  end

  scope :not_training_segment, -> do
    joins(:order_case).where.not(order_cases: {segment_id: OrderCase::TRAINING_SEGMENTS})
  end

  scope :contract_segment, -> do
    joins(:order_case).where(order_cases: {segment_id: :contract})
  end

  scope :haken_regular_segment, -> do
    joins(:order_case).where(order_cases: {segment_id: [:haken, :regular_order]})
  end
  scope :is_cancel_statuses, ->{where(display_status_id: CANCEL_STATUSES)}
  scope :is_absence_statuses, ->{where(display_status_id: ABSENCE_STATUSES)}
  scope :is_avalable_show_jobs, -> do
    where.not(display_status_id: CANCEL_STATUSES + ABSENCE_STATUSES)
  end
  scope :is_available_temporary_arranged, -> do
    where(display_status_id: AVAILABLE_TEMP_ARRANGED_STATUSES)
  end
  scope :is_recruiting_jobs, ->{where(display_status_id: RECRUITING_STATUSES)}
  scope :is_alternative, -> do
    joins(:order_portion).where.not(order_portions: {original_portion_id: nil})
  end

  scope :not_arrange_portion, -> do
    joins(:order_portion)
      .where(order_portions: {status_id: OrderPortion.status_ids[:not_arrange]})
  end

  scope :is_finish_recruiting, -> do
    where("arrangements.working_ended_at < ?", ServerTime.now)
  end

  scope :not_worked, -> do
    where("arrangements.working_ended_at >= ? OR arrangements.is_arrived = 0", ServerTime.now)
  end

  scope :start_after_now, -> do
    where("arrangements.working_started_at > ?", ServerTime.now)
  end

  scope :location_sum_billing_total_amount, -> do
    joins(:order, :arrange_billing)
      .group("orders.location_id")
      .sum("arrange_billings.billing_total_amount")
  end

  scope :location_sum_billing_tax_exemption, -> do
    joins(:order, :arrange_billing)
      .group("orders.location_id")
      .sum("arrange_billings.billing_tax_exemption")
  end

  scope :is_training_session, -> do
    joins(:order_case)
      .where(order_cases: {training_session_code: [:training_first_round, :training_second_round]})
  end

  scope :training_not_applicable, -> do
    joins(:order_case).where(
      order_cases: {segment_id: OrderCase::TRAINING_SEGMENT_IDS, training_session_code: :training_not_applicable}
    )
  end

  scope :order_by_custom_ids, ->(ids) do
    clause = sanitize_sql_array(["FIELD(arrangements.id, ?)", ids])
    order(Arel.sql(clause))
  end

  delegate :is_time_changable, :required_start_time, :required_end_time, to: :order_branch
  delegate(*ARRANGE_BILLING_ATTRS, to: :arrange_billing)
  delegate(:adjusment_type_id, *REST_METHODS, to: :arrange_billing, prefix: true)
  delegate(*ARRANGE_PAYMENT_ATTRS, to: :arrange_payment)
  delegate(*WORK_ACHIEVEMENT_REST_ATTRS, *REST_METHODS, to: :work_achievement,
    prefix: true)
  delegate :is_urgent, :is_special_offer, :segment_id, :special_offer_note, :case_started_at,
    :case_ended_at, :work_time, :contract_time, :invoice_target, :training_session_text,
    :segment_name, :location_job_category_name,
    to: :order_case, prefix: true, allow_nil: true
  alias invoice_target order_case_invoice_target
  delegate :is_migrated, :location_caution_to_staff_mail, to: :order, allow_nil: true
  delegate :corporation_full_name, :order_segment_id, :segment_name, :location_name, :location_short_name,
    :location_code, :location_stations_1_name, :location_stations_1_station_name,
    :location_pos_type_id, :location_is_store_parking_area_usable, :note, :corporation_id,
    :location_id, :business_name, :pic_department_name, :location_tel, :full_address,
    :location_full_address, :organization_full_name, :organization_position_name,
    :haken_destination_pic_position, :haken_destination_pic_name, :haken_destination_pic_id,
    :haken_destination_pic_tel, :mandator_name, :mandator_position, :mandator_tel,
    :claim_pic_position, :claim_pic_name, :claim_pic_tel, :corporation_address,
    :corporation_representative_name, :claim_process_pic_name, :haken_source_pic_position,
    :haken_source_pic_name, :order_pic_user_name, :location_postal_code,
    :mandator_id, :haken_source_pic_tel, :corporation_tel, :corporation_group_id,
    :real_pic_department_name, :current_location_type, :user_order_pic, :is_from_lawson?,
    :corporation_group_full_name, :location_job_categories_text, :location_is_export_timesheet,
    to: :order, prefix: true, allow_nil: true
  delegate :current_department_name, :account_name, :home_station_station_name,
    :age, :indefinite_employment_flag, :insurance_subsection_type, :basic_pension_number,
    :employment_insurance_number, :full_address, :name, :gender_id, :age_type, :contract_date,
    :employment_period_status, :health_insurance, :welfare_pension_insurance,
    :uniform_size, :pant_size, :shoes_size, :employment_insurance, :stable_employments_list,
    :training_or_career_list, :complaint_list, :account_name_kana, :total_work_experience,
    :home_tel, :tel, :account_email, :display_workable_time, :is_working_car,
    :level_up_training, :new_pos_training, :staff_number,
    :insurance_subsection_label, to: :staff, prefix: true, allow_nil: true

  validates :parent_id, uniqueness: {scope: [:order_case_id, :staff_id]},
    if: proc{|a| a.staff_id.present?}
  validates :order_portion_id, uniqueness: true, if: proc{|a| a.staff_id.nil? && a.is_migrated}
  validates :order_id, :order_branch_id, :order_case_id, :order_portion_id, :working_started_at,
    :working_ended_at, :break_time, :order_segment_id, presence: true
  validates :staff_id, presence: true, if: :staff_present
  validates :rest1_started_at, presence: true, if: proc{|o| o.rest2_editable || o.rest1_ended_at}
  validates :rest1_ended_at, presence: true, if: proc{|o| o.rest2_editable || o.rest1_started_at}
  validates :rest2_started_at, :rest2_ended_at, presence: true, if: :rest2_editable
  validates :rest3_started_at, :rest3_ended_at, presence: true, if: :rest3_editable

  validates :rest1_ended_at, value_greater_than: :rest1_started_at
  validates :rest2_ended_at, value_greater_than: :rest2_started_at
  validates :rest3_ended_at, value_greater_than: :rest3_started_at
  validates :working_ended_at, value_greater_than: :working_started_at
  validate :working_time_limit_rule, :required_break_time_rule,
    :rest_time_range_rule, :rest_time_overlap_rule
  validate :closed_day_rule, if: proc{|a| a.new_record?}
  validates :record_locked, record_locked: true, if: proc{|a| a.is_locked? && !a.note_changed?}
  validates :working_ended_at, in_future: true, if: proc{|o| o.is_copy_portion}
  validate :working_time_overlap_rule, if: proc{|a| a.arranged? && a.is_update_break_time}

  def arranged_arrangements
    return [self] unless self.order_case.regular_order?

    self.order.arrangements.of_staff(self.staff_id).arranged
      .includes(:arrange_payment, order: [location: [:stations_1, :prefecture]])
      .order(working_started_at: :asc)
  end

  def order_case_status
    return self.order_portion.status_id unless self.order_portion.not_arrange?
    return "finish_recruiting" if finish_apply?
    return "has_apply" if self.order_case.remained_apply > 0

    self.order_case.public_type_id
  end

  def targetable_status system_name
    case system_name
    when "op_center"
      OP_CENTER_TARGET_STATUS[self.order_case_status.to_sym]
    end
  end

  def finish_apply?
    ServerTime.now > self.working_ended_at
  end

  def working_time
    return if working_started_at.blank? || working_ended_at.blank?

    next_day = I18n.t("staff.order_cases.next_day") if self.working_started_at.strftime(Settings
      .date.formats) < self.working_ended_at.strftime(Settings.date.formats)
    "#{self.working_started_at.strftime(Settings.time.formats)}~#{next_day}#{self.working_ended_at.strftime(Settings.time.formats)}"
  end

  def working_time_format
    rest_time_fortmat = REST_TIMES.map do |rest_time|
      rest_start = rest_started_at(rest_time)
      rest_end = rest_ended_at(rest_time)
      next unless rest_start

      start_at = rest_start.strftime(Settings.time.formats)
      end_at = rest_end.strftime(Settings.time.formats)
      "#{start_at}~#{end_at}"
    end.compact.join(", ")
    return working_time if rest_time_fortmat.blank?

    "#{working_time} (#{rest_time_fortmat})"
  end

  def status_arrangement
    if absence?
      :absence
    elsif cancel_after_arrange_has_insurance?
      :cancel_after_arrange_has_insurance
    elsif cancel_after_arrange_no_insurance?
      :cancel_after_arrange_no_insurance
    elsif is_not_input?
      :not_input
    elsif is_pending_approve_and_not_staff_confirming?
      :pending_approve_and_not_staff_confirming
    elsif is_pending_approve_and_staff_confirming?
      :pending_approve_and_staff_confirming
    elsif is_prepared_for_work?
      :prepared_going_work
    elsif is_arranged?
      :arranged
    elsif absence_has_alternative?
      :absence_has_alternative
    elsif temporary_arrange?
      :applied
    elsif has_apply?
      :applied
    end
  end

  def status_staff_evaluation
    if self.staff_id.blank? && self.order_case.remained_apply > 0
      :waiting_arrange
    elsif self.staff_id.blank? && self.order_case.remained_apply == 0
      :arrange
    else
      :has_staff
    end
  end

  def available_for_recruiting
    self.display_status_id.in?(AVAILABLE_TO_EDIT_STATUS)
  end

  def not_finish_recruiting_status?
    self.display_status_id.in?(NOT_FINISH_RECRUITING_STATUSES)
  end

  def has_alternative_op?
    OrderPortion.by_original_portion_id(self.order_portion_id).exists?
  end

  def portion_arranged
    self.order_portion.arranged?
  end

  def portion_cancel_after_arrange_has_insurance
    self.order_portion.cancel_after_arrange_has_insurance?
  end

  def can_update_is_prepared?
    ServerTime.now > self.working_started_at - WORK_TIME_RANGE
  end

  def can_update_is_arrived?
    need_prepare_working
  end

  def need_prepare_working
    ServerTime.now > self.working_started_at - PREPARE_HOUR
  end

  def over_working_start_time
    ServerTime.now >= self.working_started_at
  end

  def can_public_after_cancel?
    ServerTime.now < self.working_ended_at - WORK_TIME_RANGE
  end

  def can_cancel_public_after_arranged?
    ServerTime.now < self.working_started_at - WORK_TIME_RANGE
  end

  def exist_history
    self.arrange_logs.any?
  end

  def count_working_time
    (self.working_ended_at - self.working_started_at).to_i
  end

  def predetermined_except_break_time
    count_working_time - (self.break_time.to_i * SECOND_PER_MINUTE)
  end

  def display_predetermined_time
    hours = predetermined_except_break_time / SECOND_IN_HOUR
    minutes = (predetermined_except_break_time / SECOND_PER_MINUTE) % SECOND_PER_MINUTE
    I18n.t("print.staff_info.predetermined_time", hours: hours, minutes: minutes)
  end

  def payment_requested
    self.payment_requests.present?
  end

  def staff_rank
    return unless staff

    staff.current_rank&.rank
  end

  def rank_by_date
    return unless staff

    staff.staff_ranks.where(start_use_date: self.working_started_at.beginning_of_month.to_date)
      .first&.rank || Settings.rank_default_key
  end

  def level_by_date
    return unless staff

    staff.staff_levels.recent_level_before_date(self.working_started_at.to_date).first&.level
  end

  def staff_level
    return unless staff

    staff.current_level&.level
  end

  def calculation_arrangement_data old_status = nil
    arr_payment = arrange_payment
    arr_billing = arrange_billing
    trigger_service = Calculation::TriggerArrangementDataService.new(order_portion, work_achievement, arr_payment,
      arr_billing, arranged: true, old_status: old_status)
    trigger_service.billing_payment_template = self.billing_payment_template if
      self.billing_payment_template_id.present?
    trigger_service.execute
    arr_payment.save
    arr_billing.save
  end

  def rest_time_list
    REST_TIMES.map do |rest_time|
      [self["rest#{rest_time}_started_at"]&.strftime(Settings.time.formats),
        self["rest#{rest_time}_ended_at"]&.strftime(Settings.time.formats)].compact.join("~")
    end.reject(&:empty?).join(I18n.t("common.comma"))
  end

  def portion_canceled
    self.order_portion.cancel?
  end

  def location_expired?
    location = self.order&.location
    return false if location.blank?

    !location.is_active_at_moment?(self.order_branch.started_at)
  end

  def actual_break_time
    REST_TIMES.map do |index|
      next unless self.send("rest#{index}_started_at")

      [self.send("rest#{index}_started_at").strftime(Settings.time.formats),
        self.send("rest#{index}_ended_at").strftime(Settings.time.formats)].join("～")
    end.compact.join(I18n.t("common.comma"))
  end

  def is_locked?
    return false if self.is_migrate_process

    is_payroll_locked || is_billing_locked
  end

  def contract_time
    time = (self.working_ended_at - self.working_started_at).round.abs

    Time.strptime(time.to_s, "%s").utc.strftime("%H:%M")
  end

  def time_start_edit_staff_working
    self.working_started_at <= ServerTime.now + BEFORE_WORK_TIME
  end

  def started_at_format_month_day
    I18n.l self.working_started_at, format: Settings.date.month_day_and_date
  end

  def staff_department_name_at_working_time
    return unless self.staff_id

    Department.find_by(id: self.staff.department_id_by_date(self.working_started_at))&.name
  end

  def of_same_order_case
    Arrangement.where(order_case_id: self.order_case_id).where.not(id: self.id)
  end

  class << self
    def status_id_options
      status_ids = {recruiting: [1, 2, 3, 4], finish_recruiting: 10, temporary_arrange: 6, arranged: 7,
        cancel: 5, cancel_after_arrange_has_insurance: 8, cancel_after_arrange_no_insurance: 11,
        absence: 9, absence_has_alternative: 12}
      status_ids.map do |key, val|
        {
          id: val,
          key: key,
          name: I18n.t("model_status.arrangement.display_status.#{key}")
        }
      end
    end

    def get_statistic_data arrangement_ids
      counts = {}
      status = Arrangement.where(id: arrangement_ids).select(:order_portion_id, :order_case_id,
        :working_started_at, :working_ended_at).includes(:order_portion, :order_case)
        .map(&:order_case_status)
      status.group_by(&:itself).each do |key, value|
        if key.in? %w(cancel_after_arrange_no_insurance cancel_after_arrange_has_insurance)
          counts["cancel_after_arrange_has_insurance"] = value.length +
            counts["cancel_after_arrange_has_insurance"].to_i
        else
          counts[key] = value.length
        end
      end
      counts.merge("total" => status.count)
    end

    def exist_payroll_locked? staff_ids, start_date, end_date
      in_range(start_date, end_date.end_of_day).of_staff(staff_ids).payroll_locked.exists?
    end
  end

  def started?
    self.working_started_at <= ServerTime.now
  end

  def update_working_time_follow_requested_time
    return unless staff_apply_order_case && !staff_apply_order_case.is_used_original_condition

    update_working_time(staff_apply_order_case.requested_started_at,
      staff_apply_order_case.requested_ended_at)
  end

  def staff_apply_order_case
    StaffApplyOrderCase.find_by(order_case_id: order_case_id, staff_id: staff_id)
  end

  def update_working_time_follow_order_case
    return unless staff_apply_order_case && !staff_apply_order_case.is_used_original_condition

    update_working_time order_case.case_started_at, order_case.case_ended_at
  end

  def update_payment_ot1_time_in_working_day_of staff_id
    working_date = work_achievement.working_started_at.to_date
    arranged_work_achievements ||= WorkAchievement.includes(
      arrangement: [:arrange_billing, :arrange_payment,
        {order_portion: [:order_branch, :order_case, {arrangement: :order}]}]
    ).by_staff(staff_id).by_working_time(working_date.beginning_of_day, working_date.end_of_day)
      .except_ids(self.work_achievement.id)

    work_achievements = (arranged_work_achievements.to_a << self.work_achievement)
      .sort_by(&:working_started_at)
    order_cases = work_achievements.map{|_w| work_achievement.arrangement.order_portion.order_case}
    data_info = OrderCase.data_info(order_cases, payment_rates: {}, option_payment_rates: {})
    work_achievements.each do |wa|
      wa.calculate_trigger :arrange_payment, %i(ot1_field), false, data_info
    end
  end

  def update_mail_sent_at field
    self.update_column field, ServerTime.now
  end

  def is_both_day_and_night_working?
    CheckDayTimeWorking.new(self.working_started_at,
      self.working_ended_at).is_both_day_and_night_working?
  end

  def is_night_working?
    CheckDayTimeWorking.new(self.working_started_at,
      self.working_ended_at).is_night_working?
  end

  def can_resend_confirm_arrange_mail?
    self.staff_id && self.display_status_id == Arrangement::ARRANGED_DISPLAY_STATUS
  end

  def deleted_user_order_pic?
    self.order_user_order_pic.blank?
  end

  def is_lawson
    self.order_is_from_lawson?
  end

  def is_store_computer_int
    self.order.user_order_pic&.account&.is_store_computer_account? ? 1 : 0
  end

  def is_displayable_arranged_status?
    statuses = %w(arranged absence absence_has_alternative cancel_after_arrange_has_insurance
      cancel_after_arrange_no_insurance)
    display_status_id.in? statuses
  end

  def is_unavailable?
    display_status_id.in? UNAVAILABLE_TRAINING_STATUSES
  end

  def job_time_info
    [month_day_week_with_text, working_time].join
  end

  private
  def is_not_input?
    self.is_prepared && self.is_arrived && self.work_achievement.not_inputted?
  end

  def is_pending_approve_and_not_staff_confirming?
    self.is_prepared && self.is_arrived && (self.work_achievement.owner_confirming? ||
      self.work_achievement.op_center_confirming?)
  end

  def is_pending_approve_and_staff_confirming?
    self.is_prepared && self.is_arrived && self.work_achievement.staff_confirming?
  end

  def is_prepared_for_work?
    sever_time = ServerTime.now
    (!self.is_prepared && sever_time > self.working_started_at - 1.hour && !self.is_arrived) ||
    ((self.is_prepared && sever_time > self.working_started_at) && self.work_achievement.not_inputted?)
  end

  def is_arranged?
    !self.is_prepared && !self.is_arrived &&
    self.work_achievement.not_inputted? && !NOT_ARRANGED_STATUS.include?(self.display_status_id)
  end

  def rest_started_at rest_time
    self["rest#{rest_time}_started_at"]
  end

  def rest_ended_at rest_time
    self["rest#{rest_time}_ended_at"]
  end

  def working_date
    self.working_started_at&.strftime(Settings.date.formats)
  end

  def set_rest_time_date
    REST_TIMES.each do |rest_time|
      start_time = rest_started_at(rest_time)&.strftime(Settings.time.formats)
      end_time = rest_ended_at(rest_time)&.strftime(Settings.time.formats)
      start_at = ServerTime.parse("#{working_date} #{start_time}") if start_time
      end_at = ServerTime.parse("#{working_date} #{end_time}") if end_time
      self["rest#{rest_time}_started_at"] = start_at if start_at
      self["rest#{rest_time}_ended_at"] = end_at if end_at
      if start_at && self.working_started_at.present? && start_at < self.working_started_at
        start_at = self["rest#{rest_time}_started_at"] = start_at + 1.day
        end_at = self["rest#{rest_time}_ended_at"] = end_at + 1.day if end_at
      end
      self["rest#{rest_time}_ended_at"] = end_at + 1.day if start_at && end_at &&
        end_at < start_at
    end
  end

  def set_break_time
    total_rest_time = 0
    REST_TIMES.each do |rest_time|
      start_time = rest_started_at rest_time
      end_time = rest_ended_at rest_time
      next unless start_time && end_time

      total_rest_time +=
        end_time >= start_time ? end_time - start_time : end_time + HOUR_IN_DAY.hours - start_time
    end
    self.break_time = total_rest_time.minutes / SECOND_IN_HOUR
  end

  def rest_time_range_rule
    return if working_ended_at.blank?

    REST_TIMES.each do |rest_time|
      start_time = rest_started_at rest_time
      end_time = rest_ended_at rest_time
      if start_time && start_time >= working_ended_at
        errors.add "rest#{rest_time}_started_at", :rest_time_in_range
      elsif end_time && end_time > working_ended_at
        errors.add "rest#{rest_time}_ended_at", :rest_time_in_range
      end
    end
  end

  def rest_time_overlap_rule
    check_overlap REST_TIMES[1], REST_TIMES[0]
    check_overlap REST_TIMES[2], REST_TIMES[0]
    check_overlap REST_TIMES[2], REST_TIMES[1]
  end

  def check_overlap rest_time, compare_time
    rest_time_start = rest_started_at rest_time
    rest_time_end = rest_ended_at rest_time
    compare_time_start = rest_started_at compare_time
    compare_time_end = rest_ended_at compare_time
    return if compare_time_start.blank? || compare_time_end.blank? ||
      rest_time_start.blank? || rest_time_end.blank?

    if (rest_time_start >= compare_time_start && rest_time_start < compare_time_end) ||
      (compare_time_start >= rest_time_start && compare_time_start < rest_time_end)
      errors.add "rest#{rest_time}_started_at", :rest_time_overlap
    end
  end

  def set_working_time
    return if working_started_at.blank? || working_ended_at.blank?

    start_date = self.working_started_at.strftime(Settings.date.formats)
    end_time = self.working_ended_at.strftime(Settings.time.formats)
    self.working_ended_at = ServerTime.parse("#{start_date} #{end_time}")
    self.working_ended_at += 1.day if self.working_ended_at < self.working_started_at
  end

  def working_time_limit_rule
    return if self.is_migrated
    return if working_started_at.blank? || working_ended_at.blank?
    return if shift_time_in_hours <= MAX_WORKING_HOUR

    errors.add(:working_ended_at, :over_12)
  end

  def required_break_time_rule
    return if self.is_migrated
    return if working_started_at.blank? || working_ended_at.blank?

    shift_minutes = shift_time_in_minutes
    working_time = shift_minutes - self.break_time.to_i

    if work_over_6h_break_under_45m? working_time
      errors.add(error_field, :over_working_time_limit_1, break_time: required_break_6h_shift(shift_minutes))
    elsif work_over_8h_break_under_60m? working_time
      errors.add(error_field, :over_working_time_limit_2, break_time: required_break_8h_shift(shift_minutes))
    elsif working_time <= 0
      errors.add(error_field, :rest_time_too_much)
    end
  end

  def error_field
    rest_time_field = :rest1_started_at
    REST_TIMES.each do |rest_time|
      start_time = self["rest#{rest_time}_started_at"]
      end_time = self["rest#{rest_time}_ended_at"]
      rest_time_field = "rest#{rest_time}_started_at" if start_time.present? && end_time.present?
    end
    rest_time_field
  end

  def update_working_time start_time, end_time
    self.update!(working_started_at: start_time, working_ended_at: end_time)
    work_achievement.assign_attributes(working_started_at: start_time, working_ended_at: end_time,
      staff_working_started_at: start_time, staff_working_ended_at: end_time)
    work_achievement.assign_break_time WorkAchievement::TYPES_WITH_UNDERSCORE[0]
    work_achievement.break_time = work_achievement.staff_break_time
    work_achievement.save!
    work_achievement.trigger_arrange_data
  end

  def working_time_overlap_rule
    start_time = working_started_at - ARRANGE_TIME_VALID.minutes
    end_time = working_ended_at + ARRANGE_TIME_VALID.minutes
    current_arranged_list = staff.arrangements.where.not(id: self.id).is_arranged
      .current_arrange_by_time_range(start_time, end_time)
    return if is_skip_check_arrange_time?(current_arranged_list)

    errors.add :working_started_at, :overlap if current_arranged_list.present?
  end

  def closed_day_rule
    started_at = self.order_branch&.started_at
    location = self.order&.location
    return if started_at.blank? || location.blank?

    is_location_active = location.is_active_at_moment?(started_at)
    return if is_location_active

    options = {after: location.closed_at.to_date, before: started_at.to_date}
    errors.add(:working_started_at, :out_of_closed_day, options)
  end

  def is_skip_check_arrange_time? current_arranged_list
    return false if current_arranged_list.blank?

    current_arranged_list.find_each do |arranged|
      return true if arranged.order_location_id == self.order_location_id &&
        (self.working_started_at >= arranged.working_ended_at ||
          self.working_ended_at <= arranged.working_started_at)
    end
  end

  def add_current_location_type
    if is_copy_portion || is_update_break_time
      self.current_location_type = :normal_location
      return
    end
    return unless self.new_record?

    self.current_location_type = order_current_location_type.to_sym
  end

  def month_day_week_with_text
    I18n.l working_started_at, format: Settings.date.month_day_and_date
  end

  def is_training_order_case?
    order_case.training_first_round? || order_case.training_second_round?
  end

  def update_current_staff_department_id
    if self.staff_id.blank?
      self.staff_department_id = nil
      return
    end

    return if self.staff_department_id.present? && !self.staff_id_changed?

    staff_department = StaffDepartment.select(:department_id)
      .where(staff_id: self.staff_id)
      .where("staff_departments.affiliation_date <= ?", self.working_started_at)
      .order("staff_departments.affiliation_date desc")&.first

    self.staff_department_id = staff_department&.department_id
  end

  def shift_time_in_minutes
    time_diff = (working_ended_at - working_started_at).to_i / Settings.time.minutes_per_hour
    time_diff += Settings.time.minutes_per_day if time_diff < 0
    time_diff
  end

  def shift_time_in_hours
    shift_time_in_minutes.to_f / Settings.time.minutes_per_hour
  end

  def work_over_6h_break_under_45m? working_time
    working_time > Settings.order.working_time_limit_1 &&
      working_time <= Settings.order.working_time_limit_2 &&
      self.break_time.to_i < Settings.order.break_time_at_limit_1
  end

  def work_over_8h_break_under_60m? working_time
    working_time > Settings.order.working_time_limit_2 &&
      self.break_time.to_i < Settings.order.break_time_at_limit_2
  end

  def required_break_6h_shift shift_minutes
    required_break = shift_minutes - Settings.order.working_time_limit_1
    return required_break if required_break < Settings.order.break_time_at_limit_1

    Settings.order.break_time_at_limit_1
  end

  def required_break_8h_shift shift_minutes
    required_break = shift_minutes - Settings.order.working_time_limit_2
    return required_break if required_break < Settings.order.break_time_at_limit_2

    Settings.order.break_time_at_limit_2
  end
end
