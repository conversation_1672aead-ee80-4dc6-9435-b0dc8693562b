class TrainingSchedule < ApplicationRecord
  acts_as_paranoid

  include TrainingScheduleDecorator

  UNSPECIFIED_OPTION = "-1"

  INCLUDE_MODELS = [:location, {person_in_charge: :account}].freeze

  RESTORE_PARAMS = {
    deleted_by: nil,
    deleted_at: nil,
    is_full: false
  }.freeze

  UNAVAILABLE_RESTORE_SESSION_CODE = %w(training_first_round training_second_round)

  belongs_to :location
  belongs_to :creator, foreign_key: :creator_id, class_name: "Admin", optional: true
  belongs_to :person_in_charge, foreign_key: :person_in_charge_id, class_name: "Admin", optional: true
  belongs_to :training_schedule, foreign_key: :deleted_by, class_name: "TrainingSchedule", optional: true

  has_many :training_schedule_applicants, dependent: :destroy

  enum :training_session_code, {training_first_round: 1, training_second_round: 2, single_session: 3}

  # Validations ---
  validates :person_in_charge_id,
    presence: {message: I18n.t("admin.training_schedule.errors.invalid_person_in_charge")}, on: :create
  validates :start_time, :end_time, presence: true, on: :create
  validate :start_time_less_than_end_time, :existed_schedule, on: :create,
    if: ->{self.start_time.present? && self.end_time.present?}
  validate :update_existed_schedule, on: :update, if: :person_in_charge_id_changed?
  # ------

  delegate :name, :formatted_full_address, :prefecture_name, :prefecture_id, :stations_1_name,
    to: :location, prefix: true, allow_nil: true
  delegate :name, :email, to: :person_in_charge, prefix: true, allow_nil: true

  # Callbacks ---
  after_create :create_admin_action_log
  # ------

  scope :by_location, ->location_id{where(location_id: location_id)}

  scope :by_person_in_charge, ->(person_in_charge_id) do
    trainer_id = person_in_charge_id == UNSPECIFIED_OPTION ? nil : person_in_charge_id
    where(person_in_charge_id: trainer_id)
  end

  scope :start_after, ->time{where("start_time > ?", time)}

  scope :start_before, ->time{where("start_time < ?", time)}

  scope :available, ->{where(is_full: false)}

  scope :by_start_time, ->(time){where("DATE_FORMAT(start_time, '%H:%i') = ?", time.utc.strftime("%H:%M"))}

  scope :between_dates, ->from, to{where("start_time >= ? AND start_time <= ?", from, to)}

  scope :deleted_by_schedule, ->(schedule_id){TrainingSchedule.only_deleted.where(deleted_by: schedule_id)}

  def self.get_by_search_params dates, location_ids, training_session_code, person_in_charge_id
    training_schedules = TrainingSchedule
      .includes(:location, person_in_charge: :account)
      .where(start_time: dates)
      .order(:start_time)
    training_schedules = training_schedules.send(training_session_code) if training_session_code.present?
    training_schedules = training_schedules.by_location(location_ids.split(",")) if location_ids.present?
    training_schedules = training_schedules.by_person_in_charge(person_in_charge_id) if person_in_charge_id.present?
    applicants = TrainingScheduleApplicant
      .includes(staff: :account)
      .not_absent_by_schedule_ids(training_schedules.pluck(:id))
    training_schedules = training_schedules.map do |schedule|
      schedule.formatted_calendar_data(applicants[schedule.id])
    end
    training_schedules = training_schedules.group_by{|ts| ts[:start_time]}
    training_schedules.each_key do |time|
      training_schedules[time] = training_schedules[time].group_by{|ts| ts[:training_date]}
    end
    training_schedules
  end

  def self.available_schedules_by_location training_center_id, current_schedule_id = nil
    schedules = TrainingSchedule.available
    schedules = schedules.or(where(id: current_schedule_id)) if current_schedule_id.present?

    schedules = schedules.single_session
      .start_after(ServerTime.now + 24.hours)
      .by_location(training_center_id)
      .order(start_time: :asc)

    {
      single_session_schedules: schedules.as_json(
        only: :id,
        methods: [:formatted_datetime],
        rename: {formatted_datetime: :datetime}
      )
    }
  end

  def has_training_time_passed?
    ServerTime.now > end_time
  end

  def absent_deadline
    start_time_date = start_time.to_date
    (start_time_date - 2.days).in_time_zone.change(hour: 12)
  end

  def able_to_absent?
    ServerTime.now < absent_deadline
  end

  def can_change_person_in_charge? current_admin_id
    person_in_charge_id.blank? || person_in_charge_id == current_admin_id
  end

  def assign_is_full
    valid_applicants_count = self.training_schedule_applicants.not_absent.size
    self.is_full = valid_applicants_count >= self.total_portion
  end

  def applied_staff_ids
    training_schedule_applicants.booked.pluck(:staff_id).uniq
  end

  # (Deprecated by TinhDT): Unused methods
  # def can_send_notify?
  #   applied_staff_ids.present? && training_time_with_day.present? && !has_training_time_passed?
  # end

  # def notify_schedule_deleted current_admin
  #   return unless can_send_notify?

  #   notification_data = []
  #   Staff.where(id: applied_staff_ids).includes(:account).each do |staff|
  #     if staff&.account&.email.present?
  #       StaffMailer.notify_training_schedule_deleted(
  #         staff,
  #         location_name,
  #         formatted_datetime,
  #         training_date_weekday
  #       ).deliver_now
  #     else
  #       MessageSenderService.notify_training_schedule_deleted(
  #         staff.account_tel,
  #         staff.account_name,
  #         training_time_with_day
  #       )
  #     end
  #     opt = deleted_notification_json(staff.id, training_time_with_day)
  #     if current_admin&.id.present?
  #       opt[:creator_type] = :admin
  #       opt[:creator_id] = current_admin.id
  #     end
  #     notification_data << opt
  #   end
  #   send_notifications(notification_data)
  # end

  # def send_notifications data
  #   jids = []
  #   data.in_groups_of(Settings.app_notification.batch_size).each do |data|
  #     jids << AppSendNotificationWorker.perform_async(data.reject(&:blank?))
  #   end
  #   p jids
  # end

  # def deleted_notification_json staff_id, date_format
  #   {
  #     staff_id: staff_id,
  #     creator_type: :by_system,
  #     notification_type: :deleted_training_schedule,
  #     params: {
  #       job_date_time: date_format
  #     }
  #   }
  # end

  def remove_other_schedules_and_notify_trainer
    related_schedules = get_related_schedules.includes(:location, person_in_charge: :account)
    deleted_schedule_ids = []

    deleted_schedules = related_schedules.map do |schedule|
      deleted_schedule_ids << schedule.id

      {
        location_name: schedule.location_name,
        training_session: schedule.training_session_text,
        training_time: schedule.training_time,
        person_in_charge: schedule.person_in_charge_name
      }
    end

    TrainingSchedules::SendNotificationToTrainersWorker.perform_async(
      self.id,
      "booked",
      deleted_schedules.as_json
    )

    return if deleted_schedule_ids.empty?

    related_schedules.update_all(deleted_by: id, deleted_at: ServerTime.now, updated_at: ServerTime.now)

    deleted_schedule_ids.each do |schedule_id|
      TrainingScheduleLoggingWorker.perform_async(
        "admin",
        {
          admin_id: nil,
          target_id: schedule_id,
          action_type: "auto_delete"
        }.as_json
      )
    end
  end

  # TODO(Phuc): Optimize this method when dataset is large
  def get_related_schedules
    same_date                 = TrainingSchedule.where(start_time: start_time.to_date.all_day)
    same_trainer              = same_date.where(person_in_charge_id: person_in_charge_id)
    same_location_and_session = same_date.where(location_id: location_id, training_session_code: training_session_code)
    same_location_and_session.or(same_trainer).where.not(id: id)
  end

  # (Deprecated by TinhDT): Unused methods
  # def notify_trainer schedule_status_code, data = {}
  #   return if [person_in_charge_id, person_in_charge_email].any?(&:blank?)

  #   case schedule_status_code
  #   when :booked
  #     AdminMailer.notify_staff_booked_training_schedule(person_in_charge_email, data).deliver_now
  #   when :absent
  #     deleted_schedules = data[:restore_schedules]
  #     deleted_schedules = [] if training_schedule_applicants.not_absent.present?
  #     data = data.merge(absent_mailer_data(deleted_schedules))
  #     AdminMailer.notify_staff_cancel_schedule(person_in_charge_email, data).deliver_now
  #     notify_to_op_center(data) if deleted_schedules.present?
  #   when :delete
  #     AdminMailer.notify_delete_schedule(person_in_charge_email, delete_mailer_data).deliver_now
  #   end
  # end

  # def get_deleted_schedules
  #   deleted_schedules = TrainingSchedule.deleted_by_schedule(id)
  #   return [] if deleted_schedules.blank?

  #   deleted_schedules.includes(INCLUDE_MODELS).map(&:deleted_format)
  # end

  def get_histories
    admin_logs = AdminActionLog.includes(admin: :account)
      .search_by_target("TrainingSchedule")
      .search_by_target_id(id)

    staff_logs = StaffActionLog.includes(staff: :account)
      .search_by_target("TrainingSchedule")
      .search_by_target_id(id)

    logs = admin_logs + staff_logs

    logs.sort_by(&:created_at).reverse
  end

  private

  def start_time_less_than_end_time
    return if start_time < end_time

    errors.add(:base, I18n.t("admin.training_schedule.errors.start_time_less_than_end_time"))
  end

  def existed_schedule
    _existed_schedule
  end

  def update_existed_schedule
    _existed_schedule
  end

  # Can't use existed_schedule method for both update and create because the second will override the first
  def _existed_schedule
    related_schedules = get_related_schedules
    return if related_schedules.blank?

    applicants = TrainingScheduleApplicant.not_absent_by_schedule_ids(related_schedules.pluck(:id))
    return if applicants.blank?

    if related_schedules.where(id: applicants.keys).pluck(:person_in_charge_id).include?(person_in_charge_id)
      errors.add(:base, I18n.t("admin.training_schedule.errors.same_date_and_trainer"))
    else
      errors.add(:base, I18n.t("admin.training_schedule.errors.same_date_location_and_session"))
    end
  end

  def create_admin_action_log
    return if creator_id.blank?

    TrainingScheduleLoggingWorker.perform_async(
      "admin",
      {admin_id: creator_id, target_id: id, action_type: "create"}.as_json
    )
  end
end
