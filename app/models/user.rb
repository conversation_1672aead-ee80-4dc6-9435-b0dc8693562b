class User < ApplicationRecord
  acts_as_paranoid

  include WardenFunction

  wardenable :recoverable, :authenticatable, :confirmable, :trackable

  mount_uploader :avatar, AdminAvatarUploader

  attr_accessor :require_avatar, :on_corporation_user

  USER_DEFAULT_START_DATE = Date.current
  USER_ATTRIBUTES = [:corporation_id, :role_id, :started_at,
    :avatar, :remove_avatar, {user_locations_attributes: [:id, :corporation_group_id,
    :location_id, :_destroy], user_corporation_groups_attributes: [:id, :corporation_group_id,
    :_destroy], user_groups_attributes: [:id, :corporation_group_tag_id,
    :_destroy]}]
  OWNER_ATTRIBUTES = [:corporation_id, :role_id, :started_at, :avatar, :remove_avatar]
  CHANGE_FIELDS = [:role_id, :corporation_id]

  enum :role_id, {owner: 1, system_admin: 2, approver: 3, normal: 4}

  ROLE_CAN_APPROVE = [1, 2, 3]
  CAN_SURVEY_ROLES = %w(owner system_admin approver)
  ADMINISTRATOR_ROLES = %w(owner system_admin)

  belongs_to :account, optional: true
  belongs_to :corporation
  has_many :user_corporation_groups, dependent: :destroy
  has_many :corporation_groups, through: :user_corporation_groups
  has_many :user_locations, dependent: :destroy
  has_many :locations, through: :user_locations
  has_many :location_surveys
  has_many :user_order_search_conditions, dependent: :destroy
  has_many :user_search_conditions, dependent: :destroy
  has_many :user_order_case_search_conditions, dependent: :destroy
  has_many :user_arrange_billing_search_conditions, dependent: :destroy
  has_many :organizations, through: :corporation_groups
  has_many :corporation_group_violations, as: :violation_day_updater, class_name: "CorporationGroup"
  has_many :owner_notification_users, dependent: :destroy
  has_many :user_groups, dependent: :destroy
  has_many :corporation_group_tags, through: :user_groups
  has_many :user_haken_destination_search_conditions, dependent: :destroy

  accepts_nested_attributes_for :user_corporation_groups,
    reject_if: proc{|attributes| attributes[:corporation_group_id].blank?}, allow_destroy: true
  accepts_nested_attributes_for :user_locations,
    reject_if: :validate_user_location_exist, allow_destroy: true
  accepts_nested_attributes_for :user_groups,
    reject_if: proc{|attributes| attributes[:corporation_group_tag_id].blank?}, allow_destroy: true

  validates :corporation_id, :role_id, :account_id, presence: true
  validates :started_at, valid_date: true, presence: true
  validates :avatar, presence: true, if: :require_avatar
  validate :change_corporation_group, on: :update
  validate :check_rule_system_administrator
  validates :user_locations, presence: true, if: :on_corporation_user
  validates :account_id, uniqueness: true
  delegate :full_name, to: :corporation, prefix: true
  delegate :email, :name, :name_kana, to: :account, allow_nil: true

  after_save :add_all_location_for_owner, :add_all_corporation_group_for_owner,
    :add_all_group_tag_for_owner
  after_update :delete_all_association_user

  scope :by_corporation_id, ->(corporation_id) do
    where(corporation_id: corporation_id)
  end

  scope :by_email, ->(email) do
    joins(:account).where(accounts: {email: email})
  end

  scope :by_location_id, ->(location_id) do
    joins(:user_locations).where(user_locations: {location_id: location_id})
  end

  scope :of_lawson, -> do
    joins(:corporation).where(corporations: {is_lawson: true})
  end

  scope :outside_lawson, -> do
    joins(:corporation).where(corporations: {is_lawson: false})
  end

  scope :can_approve_notification, ->(location_id) do
    by_location_id(location_id).by_role_can_approve
  end

  scope :by_role_can_approve, -> do
    where(role_id: ROLE_CAN_APPROVE)
  end

  scope :by_role_can_survey, ->(location_id) do
    includes(:account, :user_locations).where(user_locations: {location_id: location_id})
  end

  scope :sort_by_created_at, ->{order(created_at: :desc)}

  scope :user_search_by_name, ->(params) do
    query_condition = %i(name name_kana).map do |key_word|
      RansackKeywordExtractor.or_contains(params[key_word].to_s, ["account_#{key_word}"])
    end.flatten
    ransack(m: "and", g: query_condition).result
  end

  scope :search_by_name_or_name_kana, ->(key_word) do
    query_condition = %i(name name_kana).map do |attr|
      RansackKeywordExtractor.or_contains(key_word.to_s, ["account_#{attr}"])
    end.flatten
    ransack(m: "or", g: query_condition).result
  end

  scope :by_ids, ->ids{where(id: ids)}

  scope :by_corporation, ->corporation_id do
    where(corporation_id: corporation_id)
  end

  scope :by_not_corporations, ->corporation_ids do
    where.not(corporation_id: corporation_ids)
  end

  scope :without_current_user, ->current_user_id do
    where.not(id: current_user_id)
  end

  scope :by_departments, ->department_ids do
    joins(:corporation).where("pic_department_id IN (?)", department_ids).group(:id)
  end

  # scope :without_role_user, ->{where(role_id: [:owner, :admin])}

  scope :batch_daily_job, -> do
    joins(:corporation).joins("INNER JOIN types ON types.id = corporations.transaction_status_id")
      .where.not("(types.category_id = 107 AND types.type_no = 20) OR (types.category_id = 107 AND\
      types.type_no = 60)")
  end

  scope :user_corporation_groups_not_owner, ->corporation_group_id do
    where.not(role_id: User.role_ids[:owner])
      .by_corporation_group(corporation_group_id)
  end

  scope :by_corporation_group, ->corporation_group_id do
    joins(:user_corporation_groups)
      .where(user_corporation_groups: {corporation_group_id: corporation_group_id})
  end

  scope :has_owner_code, ->{where.not(owner_code_id: nil)}

  scope :active_by_date, ->date do
    where("started_at <= ?", date)
  end

  scope :have_location_not_closed, -> do
    joins(:locations)
      .where("locations.closed_at IS NULL OR
        locations.closed_at > ?", ServerTime.now).distinct
  end

  def role_name
    role_id.present? ? I18n.t("admin.users.roles.#{role_id}") : ""
  end

  # Deprecated (TinhDT): Already callback
  # def destroy_pics before_corporation_id
  #   copr_grp_ids = Corporation.find(before_corporation_id).corporation_groups.ids
  #   self.user_locations.where(corporation_group_id: copr_grp_ids).destroy_all
  #   self.user_corporation_groups.where(corporation_group_id: copr_grp_ids).destroy_all
  # end

  def is_started?
    return unless self.started_at

    Date.current >= self.started_at
  end

  def active_for_authentication?
    super && is_started? && skip_confirm?
  end

  def picked_locations
    self.locations.select :name, :id
  end

  # Deprecated (TinhDT): Unused method
  # def send_confirmation_instructions
  #   generate_confirmation_token
  #   UserMailer.active_account(self).deliver_now
  # end

  # Deprecated (TinhDT): Already callback
  # def create_user_locations
  #   self.corporation.locations.each do |location|
  #     self.user_locations.create! corporation_group_id: location.corporation_group.id,
  #       location_id: location.id
  #   end
  # end

  # Deprecated (TinhDT): Already callback
  # def create_user_corporation_groups
  #   self.corporation.corporation_groups.each do |corporation_group|
  #     self.user_corporation_groups.create! corporation_group_id: corporation_group.id
  #   end
  # end

  def skip_confirm?
    return true if self.confirmation_token.blank?

    self.confirmed_at.present?
  end

  def not_have_to_confirm?
    self.new_record? || self.owner?
  end

  def can_change_pic? location_id
    self.owner? || (self.system_admin? && self.location_ids.include?(location_id))
  end

  def can_create_or_edit_user? role
    return true if self.owner?
    return false if self.normal?

    if self.system_admin?
      role.in? %w(approver normal)
    else
      role == "normal"
    end
  end

  def update_last_read_notification_at
    self.update_columns(last_read_notification_at: ServerTime.now)
  end

  def is_role_can_survey_location?
    return false if self.normal? || self.corporation.labor?
    return true if self.owner? || self.system_admin?

    !!self.account&.is_store_computer_account?
  end

  def validation_location_validate_create_order_type
    return :valid unless self.account.is_store_computer_account?

    active_locations = self.locations.is_active
    return :inactive_locations if active_locations.blank?
    return :invalid_step_1 if active_locations.map(&:valid_order_step_1_info?).include?(false)

    :valid
  end

  private
  def add_all_location_for_owner
    return unless self.owner? && CHANGE_FIELDS.any?{|f| self.send("saved_change_to_#{f}").present?}

    locations = self.corporation.locations
    user_locations_exists = self.user_locations.pluck :user_id, :location_id, :corporation_group_id
    user_locations_params = locations.map do |location|
      [self.id, location.id, location.corporation_group_id]
    end
    user_locations_new = user_locations_params - user_locations_exists
    UserLocation.import([:user_id, :location_id, :corporation_group_id], user_locations_new)
  end

  def add_all_group_tag_for_owner
    return unless self.owner? &&
      CHANGE_FIELDS.any?{|f| self.send("saved_change_to_#{f}").present?} &&
      self.corporation.corporation_groups.any?{|c| c.corporation_group_tag_id.present?}

    user_groups_exists = self.user_groups.pluck(:user_id, :corporation_group_tag_id,
      :corporation_group_id)
    user_groups_tag_params = []
    self.corporation.corporation_group_tags.each do |group_tag|
      next if group_tag.corporation_groups.blank?

      group_tag.corporation_groups.each{|g| user_groups_tag_params << [self.id, group_tag.id, g.id]}
    end.compact
    user_groups_new = user_groups_tag_params - user_groups_exists
    UserGroup.import([:user_id, :corporation_group_tag_id, :corporation_group_id], user_groups_new)
  end

  def add_all_corporation_group_for_owner
    return unless self.owner? && CHANGE_FIELDS.any?{|f| self.send("saved_change_to_#{f}").present?}

    user_corportion_groups_exists = self.user_corporation_groups
      .pluck(:user_id, :corporation_group_id)
    user_corportion_groups_params = self.corporation.corporation_groups.map do |corporation_group|
      [self.id, corporation_group.id]
    end
    user_corporation_groups_new = user_corportion_groups_params - user_corportion_groups_exists
    UserCorporationGroup.import([:user_id, :corporation_group_id], user_corporation_groups_new)
  end

  def delete_all_association_user
    return if Order.updated_by_user(self.id).exists? || self.saved_change_to_corporation_id.blank?

    self.user_locations.destroy_all
    self.user_corporation_groups.destroy_all
    self.user_groups.update_all deleted_at: ServerTime.now
  end

  def change_corporation_group
    return unless self.corporation_id_changed?

    errors.add :corporation_id, :can_not_change_corporation_group if
    Order.updated_by_user(self.id).exists?
  end

  def check_rule_system_administrator
    return unless self.approver? || self.normal?

    has_rule_admin = self.corporation.users.where(role_id: ADMINISTRATOR_ROLES).present?
    errors.add :role_id, :not_have_administrator unless has_rule_admin
  end

  def validate_user_location_exist attributes
    attributes[:location_id].blank? || UserLocation.by_param_ids(attributes).exists?(user_id: self.id)
  end

  class << self
    def ransackable_attributes _auth_object = nil
      %w(account_id)
    end

    def ransackable_associations _auth_object = nil
      %w(account)
    end
  end

  protected
  def confirmation_required?
    false
  end
end
