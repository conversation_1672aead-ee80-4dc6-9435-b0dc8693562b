module RedisModels
  class TrainingCenter
    class << self
      TRAINING_CENTERS_CACHED_KEY = "training_center_locs".freeze

      def get_cache
        redis.get(TRAINING_CENTERS_CACHED_KEY)
      end

      def del_cache
        redis.del(TRAINING_CENTERS_CACHED_KEY)
      end

      def store_cache value, expire: 1.hour
        return if value.blank?

        redis.set(TRAINING_CENTERS_CACHED_KEY, value.to_json, ex: expire)
      end

      private

      def redis
        @redis ||= Lawson::RedisConnector.new
      end
    end
  end
end
