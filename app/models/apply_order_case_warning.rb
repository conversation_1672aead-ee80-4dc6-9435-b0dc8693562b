class ApplyOrderCaseWarning
  include ActiveModel::Model
  include ActiveModel::Validations

  attr_reader :staff, :order_case, :order, :actual_working_time, :contract_warning

  validate :out_of_contract

  def initialize staff, order_case
    @staff = staff
    @order_case = order_case
    @order = @order_case.order
  end

  private
  def out_of_contract
    return unless staff.haken?
    return if order_case.segment_trainning

    check_date = order_case.regular_order? ? last_working_date : working_date
    return if check_date.blank?
    return if staff.contract_history_by_date(check_date).present? ||
      staff.contract_history_by_date(ServerTime.now.to_date).blank?

    errors.add :contract_warning, :out_of_contract
  end

  def working_date
    order_case.case_started_at.to_date
  end

  def last_working_date
    order.last_started_at&.to_date
  end
end
