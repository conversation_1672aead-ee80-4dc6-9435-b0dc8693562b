class TrainingScheduleCondition
  include ActiveModel::Model
  include ActiveModel::Validations

  attr_reader :schedule_condition
  attr_accessor :schedule_ids

  validate :schedule_was_deleted
  validate :session_empty
  validate :single_session_is_full, if: ->{@single_session.present?}
  validate :session_is_full, :second_session_after_first_session,
    if: ->{[@first_session, @second_session].all?(&:present?)}

  def initialize schedule_ids
    @schedule_ids = schedule_ids
    schedules = TrainingSchedule.with_deleted.where(id: schedule_ids)
    @first_session = schedules.find(&:training_first_round?)
    @second_session = schedules.find(&:training_second_round?)
    @single_session = schedules.find(&:single_session?)
  end

  def valid_schedule?
    formatted_response
  end

  private

  # TODO(PHUC): If have more formats in future, consider chosing a common format
  def formatted_response
    {
      valid: valid?,
      error_messages: errors.messages[:schedule_condition],
      error_details: errors.details[:schedule_condition].map{|err| err[:error]}
    }
  end

  # Schedule validations

  def schedule_was_deleted
    errors.add(
      :schedule_condition,
      :schedule_was_deleted,
      message: I18n.t("staff.training_schedule.errors.schedule_unavailable")
    ) if [@first_session&.deleted_at, @second_session&.deleted_at, @single_session&.deleted_at].any?(&:present?)
  end

  def session_empty
    errors.add(
      :schedule_condition,
      :session_empty,
      message: I18n.t("staff.training_schedule.errors.must_select_training_session")
    ) unless [@first_session, @second_session].all?(&:present?) || @single_session.present?
  end

  def session_is_full
    errors.add(
      :schedule_condition,
      :session_is_full,
      message: I18n.t("staff.training_schedule.errors.schedule_unavailable")
    ) if [@first_session, @second_session].any?(&:is_full?)
  end

  def single_session_is_full
    errors.add(
      :schedule_condition,
      :session_is_full,
      message: I18n.t("staff.training_schedule.errors.schedule_unavailable")
    ) if @single_session.is_full?
  end

  def second_session_after_first_session
    errors.add(
      :schedule_condition,
      :second_session_after_first_session,
      message: I18n.t("staff.training_schedule.errors.training_sessions_order")
    ) if (@second_session.start_time.to_date - @first_session.start_time.to_date).to_i <= 0
  end
end
