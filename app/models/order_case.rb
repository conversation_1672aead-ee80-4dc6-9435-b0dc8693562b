class OrderCase < ApplicationRecord
  acts_as_paranoid

  include OrderCaseDecorator
  include OrderCaseWagesDecorator
  include OrderCaseSearchDecorator
  include DomainInfo
  include OrderCaseConcernFilterScope
  include TrainingScheduleConcern
  include Api::LocationJobConstants

  SHOW_HAKEN_SEGMENT = [1, 7]

  CONTRACT_SEGMENT = "contract"

  joined_staff_staff_apply_oc_proc = proc do
    joins(:staff).where("staff_apply_order_cases.status_id != ?",
      StaffApplyOrderCase.status_ids[:rejected])
  end

  unscope_staff_staff_apply_oc_proc = proc do
    default_unscope
  end

  before_save :update_arrangement_public_status

  belongs_to :order_branch
  belongs_to :order
  belongs_to :location_job_category, optional: true
  has_many :order_portions
  has_many :arrangements
  has_many :arrangements_not_absence, ->{where.not(display_status_id: Arrangement.display_status_ids[:absence])},
    class_name: "Arrangement"
  has_many :staff_apply_order_cases
  has_many :joined_staff_staff_apply_order_cases, joined_staff_staff_apply_oc_proc,
    class_name: "StaffApplyOrderCase"
  has_many :unscope_staff_staff_apply_order_cases, unscope_staff_staff_apply_oc_proc,
    class_name: "StaffApplyOrderCase"
  has_many :staff_keep_order_cases
  has_many :staffs, through: :staff_apply_order_cases
  has_many :staff_like_locations, foreign_key: :related_order_case_id
  has_many :work_achievements, through: :arrangements
  has_many :order_case_public_staffs
  has_one :job_page_view
  has_many :staff_evaluations, through: :arrangements
  has_many :location_evaluations, through: :arrangements
  has_many :urgent_offer_mail_staffs
  has_one :registration_answer
  has_many :staff_order_case_offers
  has_one :registration_answer

  validates :order_id, :order_branch_id, :case_started_at,
    :case_ended_at, :total_portion, presence: true

  enum :segment_id, {haken: 1, training: 2, new_pos_training: 3, rank_up_training: 4,
    career_up_training: 5, regular_order: 6, contract: 7}
  enum :invoice_target, {lawson_invoice: 0, separate_invoice: 1}

  IMPORT_ATTRIBUTES = [:order_id, :order_branch_id, :case_started_at, :case_ended_at,
    :total_portion, :estimation, :immediate_fee, :is_special_offer, :special_offer_fee,
    :special_offer_note, :location_job_category_id, :invoice_target]
  ORDER_CASE_BASIC_ATTRIBUTES = [:id, :order_id, :order_branch_id, :case_started_at, :case_ended_at,
    :staff_apply_count, :peak_period_order_rate, :peak_period_unit_rate, :is_urgent, :segment_id]
  DEADLINE_APPLY = 2.hours
  BEFORE_WORK_TIME = 2.hours + 30.minutes
  ORDER_CASE_BASIC_METHODS = [:started_at, :working_time, :status, :site_name, :location_latitude,
    :location_longitude, :location_station_1_info, :started_at_format_month_day, :location_id,
    :timeout_apply_before_2_hour, :special_offer_order_rate, :payment_unit_price_addition,
    :is_time_changable, :location_get_type, :break_time, :placeholder_transportation_fee,
    :segment_regular, :location_thumbnail_path, :location_logo, :thumbnail_path]
  ORDER_CASE_METHODS = [:working_time, :site_name, :order_date, :valid_cancel, :remain_day_note,
    :order_portion_arranged, :working_status_i18n, :status_id_i18n, :started_at_format_month_date,
    :status_before_cast, :arrangements_status_i18n]
  ORDER_CASE_INDEX_METHODS = [:started_at, :working_time, :status, :started_at_format_month_day,
    :location_id, :timeout_apply_before_2_hour, :segment_regular,
    :is_time_changable, :break_time, :placeholder_transportation_fee,
    :site_name, :location_station_1_info, :location_latitude, :location_longitude,
    :location_get_type, :location_thumbnail_path, :location_logo, :thumbnail_path]
  SEARCH_ORDER_CASE_METHODS = [:corporation_full_name, :location_name, :started_at, :location_id,
    :corporation_id]
  BATCH_ARRANGE_METHODS = [:site_name, :started_at_format_month_date, :location_id,
    :location_stations_1_station_name, :total_arrange, :working_time, :segment_trainning,
    :sum_evaluation, :is_urgent, :segment_regular]
  TABLE_RELATIONSHIP = [order: [location: [:prefecture, {stations_1: [:railway_line],
    corporation_group: [:corporation]}]], arrangements: [:arrange_billing, :arrange_payment]]
  WORK_LIST_TABLE_RELATIONSHIP = [:arrangements, :location_job_category, {order: [location: [stations_1: [:railway_line],
    corporation_group: [:corporation]]]}]
  WORK_LIST_BASIC_ATTRIBUTES = [:id, :order_id, :order_branch_id, :staff_apply_count, :is_urgent,
    :segment_id, :status_id]
  WORK_LIST_METHODS = [:location_station_1_info, :started_at_format_year_month_and_date, :site_name,
    :working_time, :location_get_type, :location_thumbnail_path, :thumbnail_path]
  TABLE_RELATIONSHIP_JOBS = [:location_job_category,
    {order: [location: [:job_category, :prefecture, {stations_1: [:railway_line],
    corporation_group: [:corporation]}]], arrangements: [:arrange_billing, :arrange_payment]}]
  INCLUDE_TABLES = [:location_job_category, :order_portions, :work_achievements,
    [order: [location: [:prefecture]]],
    [arrangements: [:arrange_billing, :arrange_payment]]]
  API_ORDER_CASE_BASIC_INFO = %i(location_logo location_only_name job_started_at working_time)
  APPLIED_STATUS = %w(recruiting finish_recruiting adjusting)
  FINISHED_STATUS = %w(adjusting arranged not_inputted confirming not_approved)
  AVAILABLE_PUBLIC_STATUS = %w(recruiting_public recruiting_limited)
  PUBLIC_WORKING_STATUS_ID = [2]
  OFFER_FEE_RATE = 0.7
  SEGMENT_ID_FOR_CALC = {default: 1, payroll: :all, staff_count: :all}
  BATCH_ARRANGE_INCLUDE_TABLES = [:location_job_category,
    [order: [location: [:stations_1, :last_evaluation_summaries]]],
    [joined_staff_staff_apply_order_cases: [staff: [:account, :home_railway_line,
    :home_station, :lastest_evaluation_summaries]]],
    :order_portions]
  NON_REGULAR_SEGMENT_IDS = [1, 2, 3, 4, 5, 7]
  TRAINING_SEGMENT_IDS = [2, 3, 4, 5]
  REGULAR_SEGMENT = "regular_order"
  TRAINING_SEGMENTS = %w(training new_pos_training rank_up_training career_up_training)
  JOB_SEARCH_PARAMS = [:order_key, :order_type, :start_date_from, :start_date_to,
    :is_open, :is_time_changable, :is_regular_order, :search_by_prefecture, :location_prefecture_id]
  JOB_SEARCH_ATTRS = [:id, :case_started_at, :case_ended_at, :is_urgent, :segment_id,
    :peak_period_order_rate, :peak_period_unit_rate, :status_id]
  JOB_SEARCH_METHODS = [:started_at, :working_time, :site_name, :location_id,
    :location_latitude, :location_longitude, :location_station_1_info, :location_thumbnail_path,
    :location_logo, :timeout_apply_before_2_hour, :location_job_categories_text, :is_time_changable,
    :predetermined_time, :break_time, :is_regular_order, :location_station_1_short_info,
    :location_only_name, :thumbnail_path]
  NEW_JOB_SEARCH_ATTRS = [:id, :is_urgent, :segment_id, :status_id]
  NEW_JOB_SEARCH_METHODS = [:started_at, :working_time, :is_time_changable, :predetermined_time,
    :break_time, :is_regular_order, :location_id, :timeout_apply_before_2_hour, :start_date_dash_format]
  IDENTIFY_KEY = [:location_job_category_id, :job_category_key]
  delegate :location_id, :type_id, :location_prefecture_name, :location_city, :corporation_id,
    :corporation_full_name, :location_street_number, :location_building, :location_name,
    :location_is_store_parking_area_usable, :location_short_name, :location_full_address,
    :location_address, :location_postal_code, :location_tel, :location_fax,
    :location_station_1_info, :location_station_2_info, :location_station_3_info,
    :location_station_4_info, :location_station_5_info, :location_station_1, :location_station_2,
    :location_station_3, :location_station_4, :location_station_5, :location_stations_1_name,
    :location_stations_2_name, :location_stations_3_name, :location_stations_4_name,
    :location_stations_5_name, :location_latitude, :location_longitude,
    :location_is_store_parking_area_usable, :location_stations_1_station_name,
    :location_prefecture_id, :location_location_type, :location_get_type, :location_thumbnail_background_path,
    :location_thumbnail_path, :location_logo, :location_job_categories_text,
    :location_job_content, :location_personal_things, :location_clothes, :location_special_note,
    :location_station_1_short_info, :location_only_name, :pic_department_id, :location_camelized_type,
    allow_nil: true, to: :order
  delegate :is_time_changable, :work_time, :contract_time, to: :order_branch
  delegate :predetermined_time, :required_start_time, :required_end_time, to: :order_branch, allow_nil: true
  delegate :job_category_name, :thumbnail_path, :personal_things, :clothes,
    :job_content, :special_note, to: :location_job_category, allow_nil: true, prefix: false
  enum :status_id, {recruiting: 1, finish_recruiting: 2, cancel: 3, adjusting: 4, arranged: 5}
  enum :public_type_id, {type_public: 1, type_limited: 2, type_hidden: 3}
  enum :working_status_id, {not_inputted: 0, confirming: 1, not_approved: 2, approved: 3, absence: 4}

  scope :where_keep_order, ->ids do
    where(id: ids).order(sanitize_sql_for_order([Arel.sql("FIELD(order_cases.id, ?)"), ids]))
  end
  scope :show_haken, ->{where(segment_id: SHOW_HAKEN_SEGMENT)}
  scope :has_remain_apply, ->{where("order_cases.remained_apply > 0")}
  scope :have_portions, ->{where("order_cases.total_portion > 0")}
  scope :nearest_working_cases, ->(start_at, end_at) do
    where("order_cases.case_started_at > ? and order_cases.case_started_at < ?" \
      "and (order_cases.status_id != ?)",
      start_at, end_at, OrderCase.status_ids[:cancel])
      .show_haken.order(case_started_at: :desc)
      .limit(Settings.order_case.nearest.limit)
  end
  scope :total_order_portion, -> do
    joins(:order_portions).where(order_portions: {status_id: OrderPortion::VALID_STATUS})
      .select("order_cases.id, count(order_portions.id) as total_count")
      .group("order_cases.id")
  end
  scope :success_order_portion, -> do
    joins(:order_portions).where(order_portions: {status_id: :arranged})
      .select("order_cases.id, count(order_portions.id) as success_count")
      .group("order_cases.id")
  end

  scope :portions_arranged, -> do
    joins(:order_portions)
      .where(order_portions: {status_id: OrderPortion.status_ids[:arranged]})
  end

  scope :by_date, ->date do
    where("order_cases.case_started_at BETWEEN ? AND ?",
      date.beginning_of_day, date.end_of_day)
  end

  scope :available_apply, -> do
    joins(:arrangements)
      .where(arrangements: {display_status_id: AVAILABLE_PUBLIC_STATUS})
      .where("(is_urgent = 0 AND case_started_at > :deadline_apply) " \
        "OR (is_urgent = 1 AND case_ended_at > :deadline_apply)",
        deadline_apply: (ServerTime.now + DEADLINE_APPLY)).distinct
  end

  scope :by_pic_department, ->department_ids do
    joins(:order)
      .where(orders: {pic_department_id: department_ids})
  end

  scope :haken_expired_apply, -> do
    where.not(segment_id: :regular_order)
      .where("(order_cases.is_urgent = 1 AND order_cases.case_ended_at <= ?)
        OR (order_cases.is_urgent = 0 AND order_cases.case_ended_at <= ?)",
        ServerTime.now + DEADLINE_APPLY, ServerTime.now)
  end

  scope :regular_expired_apply, -> do
    joins(:order).where(orders: {order_segment_id: :regular_order})
      .where("orders.last_started_at <= ?", ServerTime.now).distinct
  end

  scope :staff_register_expired_apply, -> do
    where("order_cases.case_started_at <= ?", ServerTime.now + 10.hours)
  end

  scope :haken_case_nearest, -> do
    show_haken
      .where("order_cases.case_started_at < ?", ServerTime.now)
      .order("order_cases.case_started_at desc")
      .limit(Settings.order_case.nearest.limit)
  end

  scope :for_calc_evaluation, ->staff_id do
    joins(:staff_evaluations).includes(:arrangements)
      .haken_case_nearest
      .select("order_cases.id, count(staff_evaluations.id) as count_evaluation, " \
        "sum(staff_evaluations.evaluation1) as sum_eval1, " \
        "sum(staff_evaluations.evaluation2) as sum_eval2, " \
        "sum(staff_evaluations.evaluation3) as sum_eval3")
      .where("arrangements.staff_id = ?", staff_id)
      .group("order_cases.id")
  end

  scope :for_calc_location_evaluation, ->location_id do
    joins(:location_evaluations, :order)
      .haken_case_nearest
      .select("order_cases.id, avg(location_evaluations.evaluation) as sum_eval")
      .where("orders.location_id = ?", location_id)
      .group("order_cases.id")
  end

  scope :current_oc_by_time_range, ->started_at, ended_at do
    where("(:started_at BETWEEN case_started_at AND case_ended_at) " \
      "OR (:ended_at BETWEEN case_started_at AND case_ended_at) " \
      "OR ((case_started_at BETWEEN :started_at AND :ended_at) " \
      "AND (case_ended_at BETWEEN :started_at AND :ended_at))",
      started_at: started_at, ended_at: ended_at)
  end

  scope :order_by_total_apply_asc, -> do
    order(staff_apply_count: :asc)
  end

  scope :order_by_matching_rate_asc, -> do
    joins(order: :location).order("locations.matching_rate asc")
  end

  scope :order_by_started_at_asc, -> do
    order("order_cases.case_started_at asc")
  end

  scope :order_by_location_name, ->order_by do
    case order_by
    when "location_name_asc"
      joins(order: :location).order("locations.name asc")
    when "location_name_desc"
      joins(order: :location).order("locations.name desc")
    end
  end

  scope :batch_arrange_order, ->(sort_by, order_by = "") do
    if sort_by == "started_at_asc"
      order_by_location_name(order_by).order_by_started_at_asc
        .order_by_total_apply_asc.order_by_matching_rate_asc
    else
      order_by_location_name(order_by).order_by_total_apply_asc
        .order_by_matching_rate_asc.order_by_started_at_asc
    end
  end

  scope :not_cancel, ->{where.not(status_id: OrderCase.status_ids[:cancel])}

  scope :by_ids, ->ids{where(id: ids)}

  scope :by_arrangement, ->arrangement_ids do
    joins(:arrangements).where(arrangements: {id: arrangement_ids})
  end

  scope :by_owner_location, ->location_ids do
    joins(:order).where(orders: {location_id: location_ids})
  end

  scope :by_public_owner_location, -> do
    where(working_status_id: PUBLIC_WORKING_STATUS_ID).where("order_cases.case_ended_at < ?",
      ServerTime.now.beginning_of_day + 18.hours)
  end

  scope :by_approve_owner_location, -> do
    where(status_id: OrderCase.status_ids[:arranged])
      .where("order_cases.arranged_at < ? AND order_cases.arranged_at >= ?",
        ServerTime.now.beginning_of_day + 18.hours,
        ServerTime.now.beginning_of_day - 1.day + 18.hours)
  end

  scope :by_cancel_owner_location, -> do
    joins(:order_portions).where("order_portions.status_id = ?", OrderPortion.status_ids[:cancel])
      .where("canceled_at < ? AND canceled_at >= ?",
        ServerTime.now.beginning_of_day + 18.hours,
        ServerTime.now.beginning_of_day - 1.day + 18.hours)
  end

  scope :by_auto_approved_owner_location, -> do
    joins(order_portions: {arrangement: [work_achievement: :work_achievement_logs]})
      .where(work_achievements:
        {working_time_status_id: WorkAchievement.working_time_status_ids[:auto_approved]})
      .where("work_achievement_logs.action_type = ? OR
        (work_achievement_logs.action_type = ? AND work_achievement_logs.log_data = ?)",
        WorkAchievementLog.action_types[:auto_approve],
        WorkAchievementLog.action_types[:op_update], "auto_approved")
      .where("work_achievement_logs.created_at < ? AND work_achievement_logs.created_at >= ?",
        ServerTime.now.beginning_of_day + 18.hours,
        ServerTime.now.beginning_of_day - 1.day + 18.hours).distinct
  end

  scope :finish_an_hour_ago, -> do
    where("order_cases.case_started_at >= ? AND order_cases.case_started_at < ?",
      ServerTime.now - 25.hours, ServerTime.now)
      .where.not(status_id: :cancel).where("order_cases.case_ended_at >= ? AND order_cases.case_ended_at < ?",
        ServerTime.now - 1.hour, ServerTime.now)
  end

  scope :arranged_and_not_approved_portions, -> do
    joins(order_portions: {arrangement: :work_achievement})
      .where(order_portions: {status_id: OrderPortion.status_ids[:arranged]})
      .where.not(work_achievements:
        {working_time_status_id: WorkAchievement::APPROVED_WORKING_TIME_STT_IDS})
  end

  scope :arranged_and_approved_portions, -> do
    joins(order_portions: {arrangement: :work_achievement})
      .where(order_portions: {status_id: OrderPortion.status_ids[:arranged]})
      .where(work_achievements:
        {working_time_status_id: WorkAchievement::APPROVED_WORKING_TIME_STT_IDS})
  end

  scope :order_case_started_at, ->{order(case_started_at: :asc)}

  scope :by_order_id, ->(order_id){where(order_id: order_id)}

  scope :start_after_now, -> do
    where("order_cases.case_started_at > ?", ServerTime.now)
  end

  scope :start_after, ->time do
    where("order_cases.case_started_at > ?", time)
  end

  scope :first_training_session, -> do
    where(training_session_code: :training_first_round)
  end

  scope :second_training_session, -> do
    where(training_session_code: :training_second_round)
  end

  scope :ocs_time_changable, ->order_id do
    joins(:order_branch).where(order_id: order_id).where(order_branches: {is_time_changable: true})
  end

  scope :unskip_staff_violation, -> do
    joins(:order).where(orders: {is_fixed_term_project: false, is_limited_day: false})
  end

  scope :uncertain_start_or_not_full_applied, -> do
    where("order_cases.training_schedule_code = 2 OR " \
      "(order_cases.training_schedule_code = 1 AND " \
      "order_cases.total_portion > order_cases.staff_apply_count)")
  end

  scope :order_by_ids, ->(ids){order(sanitize_sql_for_order([Arel.sql("FIELD(order_cases.id, ?)"), ids]))}

  after_update :reset_cache_of_job_search

  def segment_name
    return "" unless segment_id

    I18n.t("corporation.order.segment_ids.#{segment_id}")
  end

  def job_category_data
    return {
      location_job_category_id: 0,
      job_category_key: "#{order.location_id}_0"
    } if location_job_category_id.blank?
    thumbnail_background_path = location_job_category.thumbnail_path
    job_categories_text = location_job_category.job_category_name
    {
      job_category_name: job_categories_text,
      thumbnail_path: thumbnail_background_path,
      personal_things: location_job_category.personal_things,
      clothes: location_job_category.clothes,
      job_content: location_job_category.job_content,
      special_note: location_job_category.special_note,
      location_job_category_id: location_job_category_id,
      job_category_key: "#{self.location_id}_#{location_job_category_id}",
      thumbnail_background_path: thumbnail_background_path,
      job_categories_text: job_categories_text
    }
  end

  def location_info
    self.order.location
      .as_json(only: Location::API_BASIC_INFO, methods: Location::API_JOB_METHODS)
      .merge(self.job_category_data.as_json)
  end

  def show_haken?
    haken? || contract?
  end

  def not_training?
    show_haken? || regular_order?
  end

  def to_json_for_staff_detail
    self.to_json(
      only: [:order_branch_id, :id, :order_id, :case_started_at, :segment_id],
      methods: [:location_id, :placeholder_transportation_fee, :is_midnight]
    )
  end

  def joined_staff_staff_apply_order_cases_not_absence
    self.joined_staff_staff_apply_order_cases.reject do |saoc|
      saoc.arrangement&.absence? || saoc.arrangement&.absence_has_alternative?
    end
  end

  def first_order_case
    return self unless self.regular_order?

    OrderCase.by_order_id(self.order_id).order(case_started_at: :asc).first
  end

  def regular_order_arrange_payment
    return unless self.regular_order?

    self.arrangements.first.arrange_payment
  end

  def count_staff_applies
    staff_apply_ocs = StaffApplyOrderCase.by_order_ids(self.order_id).by_order_case_ids(self.id)
    staff_apply_ocs = staff_apply_ocs.by_staff_status(Staff.status_ids["op_confirm"]) unless training?
    staff_apply_ocs.not_processed.size + staff_apply_ocs.arranged.portions_arranged.size
  end

  def sum_evaluation
    self.order.location&.evaluation
  end

  def job_started_at
    return "" unless case_started_at

    I18n.l self.case_started_at, format: Settings.date.month_date
  end

  def total_arrange
    statuses = self.order_portions.map(&:status_id)
    portion_statuses = OrderPortion::STATUSES
    statuses.select do |status|
      [portion_statuses[:arranged], portion_statuses[:temporary_arrange]].include?(status)
    end.size
  end

  def get_request_time order_portion
    staff_apply_order_cases&.find_by(staff_id: order_portion.arrangement.staff_id)&.request_time
  end

  def calculate_price payment_rate = nil, regular_order_price = nil
    estimation_params = {
      started_at: self.case_started_at.to_date,
      working_start_time: self.case_started_at&.strftime("%H:%M"),
      working_end_time: self.case_ended_at&.strftime("%H:%M"),
      prefecture_id: self.order.location.prefecture_id,
      staff_count: self.total_portion,
      is_special_offer: self.is_special_offer,
      special_offer_fee: self.special_offer_fee,
      is_except_holiday: order_branch.is_except_holiday,
      is_time_changable: order_branch.is_time_changable,
      corporation_id: self.order.corporation_id,
      location_id: self.order.location_id,
      special_offer_note: self.special_offer_note,
      rest1_started_at: order_branch.rest1_started_at&.strftime(Settings.time.formats),
      rest1_ended_at: order_branch.rest1_ended_at&.strftime(Settings.time.formats),
      rest2_started_at: order_branch.rest2_started_at&.strftime(Settings.time.formats),
      rest2_ended_at: order_branch.rest2_ended_at&.strftime(Settings.time.formats),
      rest3_started_at: order_branch.rest3_started_at&.strftime(Settings.time.formats),
      rest3_ended_at: order_branch.rest3_ended_at&.strftime(Settings.time.formats),
      order_created_at: self.order.created_at
    }

    if regular_order_price
      estimation_params = estimation_params.merge(regular_order_price)
      EstimateOrderPriceService.regular_order_price(estimation_params, false, self.id)
    else
      EstimateOrderPriceService.individual_order(estimation_params, false, payment_rate, self)
    end
  end

  def update_cancel_status
    ActiveRecord::Base.transaction do
      self.order_portions.update_all(status_id: OrderPortion.status_ids[:cancel])
      self.update(status_id: OrderCase.status_ids[:cancel], canceled_at: ServerTime.now,
        remained_apply: 0, total_portion: 0)
      cancel_staff_apply_data self.staff_apply_order_cases.default_unscope
      self.order_branch.update_total_portion
      self.order.update_total_order_portions
      true
    end
  rescue ActiveRecord::Rollback
    false
  end

  def self.data_info order_cases, data = nil
    return if data.nil?

    working_dates = order_cases.map(&:working_date).uniq
    location_prefecture_id = order_cases[0].order.location_prefecture_id
    corporation_id = order_cases[0].order.corporation_id
    location_id = order_cases[0].order.location_id
    option_payment_rates = {}
    payment_rates = {}
    peak_periods = {}

    working_dates.each do |started_at|
      if data.try(:[], :payment_rates).to_h.keys.include?(started_at.to_date.to_s)
        payment_rates[started_at] = data.try(:[], :payment_rates).try(:[], started_at.to_date.to_s)
      else
        payment_rates[started_at] = LocationPaymentRate.by_location(location_id).in_range_date(started_at).first
        payment_rates[started_at] ||= PaymentRate.by_prefecture_corporation(location_prefecture_id, corporation_id)
          .in_range_date(started_at).first
        payment_rates[started_at] ||= PaymentRate.by_all_corporation.by_prefecture(location_prefecture_id)
          .in_range_date(started_at).first
        data[:payment_rates][started_at.to_date.to_s] = payment_rates[started_at]
      end
      if data.try(:[], :option_payment_rates).to_h.keys.include?(started_at.to_date.to_s)
        option_payment_rates[started_at] = data.try(:[], :option_payment_rates).try(:[], started_at.to_date.to_s)
      else
        option_payment_rates[started_at] = OptionPaymentRate.by_prefecture_corporation(location_prefecture_id,
          corporation_id).in_range_date(started_at).first
        option_payment_rates[started_at] ||= OptionPaymentRate.by_all_corporation(location_prefecture_id)
          .in_range_date(started_at).first
        data[:option_payment_rates][started_at.to_date.to_s] = payment_rates[option_payment_rates]
      end
    end

    chain_ids = payment_rates&.values.to_a.map(&:chain_id).uniq

    period_rates = PeakPeriods::GetPeriodRatesByDatesQuery.execute(
      working_dates,
      location_prefecture_id,
      corporation_id,
      chain_ids
    )

    order_cases.each do |order_case|
      working_date = order_case.case_started_at.strftime(Settings.date.formats)
      peak_periods[order_case.id] = period_rates[working_date]
    end

    {
      payment_rates: payment_rates,
      option_payment_rates: option_payment_rates,
      peak_periods: peak_periods
    }
  end

  def self.update_cancel_status order_cases
    ActiveRecord::Base.transaction do
      order_cases.each do |order_case|
        order_case.order_portions.update_all(status_id: OrderPortion.status_ids[:cancel])
        order_case.update(status_id: OrderCase.status_ids[:cancel], canceled_at: ServerTime.now,
          remained_apply: 0, total_portion: 0)
        order_case.cancel_staff_apply_data(order_case.staff_apply_order_cases.default_unscope)
        order_case.order_branch.update_total_portion
        order_case.order.update_total_order_portions
      end
      true
    end
  rescue ActiveRecord::Rollback
    false
  end

  def update_remained_apply
    remained = self.staff_apply_order_cases.default_unscope.not_processed.size
    self.update_column :remained_apply, remained
  end

  def update_status_follow_order_portion
    self.assign_status_follow_order_portion
    self.reassign_total_portion
    self.save
    self.order_branch.update_total_portion
    self.order.update_total_order_portions
  end

  def assign_status_follow_order_portion
    order_case_status = status_by_portion
    self.status_id = order_case_status
    case order_case_status
    when :arranged
      self.arranged_at = ServerTime.now
    when :cancel
      self.canceled_at = ServerTime.now
    end
  end

  def update_working_status_follow_work_achievement
    self.working_status_id = working_status_by_work_achievement
    self.save
  end

  def update_status_by_or_portion_wk_achievement
    self.working_status_id = working_status_by_work_achievement
    assign_status_follow_order_portion
    reassign_total_portion
    self.save
    self.order_branch.update_total_portion
    self.order.update_total_order_portions
  end

  def reassign_total_portion
    self.total_portion = self.order_portions.valid_status.count
  end

  def create_order_portion_data parent_arrangement
    order = self.order
    order_branch = self.order_branch
    portion = OrderPortion.new(order_id: self.order_id,
      order_branch_id: self.order_branch.id, order_case_id: self.id,
      case_started_at: self.case_started_at, case_ended_at: self.case_ended_at)
    portion.original_portion_id = parent_arrangement.order_portion_id
    portion.save
    arrangement = Arrangement.new(order_id: order.id, order_branch_id: order_branch.id,
      order_case_id: portion.order_case_id, order_portion_id: portion.id,
      working_started_at: portion.case_started_at, working_ended_at: portion.case_ended_at,
      order_segment_id: order.order_segment_id, parent_id: parent_arrangement.id,
      is_penalty_target: false)
    arrangement.billing_payment_template_id = parent_arrangement.billing_payment_template_id
    Arrangement::BREAK_TIME_ATTRS.each{|attr| arrangement[attr] = order_branch[attr]}
    arrangement.save
    ArrangeLog.create_by(ArrangeLog.action_types[:oc_created], nil, arrangement)
    parent_arrangement.update_attribute(:child_id, arrangement.id)
    work_achievement = WorkAchievement.new(arrangement_id: arrangement.id,
      working_time_status_id: WorkAchievement.working_time_status_ids[:not_inputted])
    arr_paym = ArrangePayment.new(arrangement_id: arrangement.id)
    arr_bill = ArrangeBilling.new(arrangement_id: arrangement.id)
    trigger_service = Calculation::TriggerArrangementDataService.new(portion, work_achievement,
      arr_paym, arr_bill, not_trigger: true)
    trigger_service.billing_payment_template = parent_arrangement.billing_payment_template if
      parent_arrangement.billing_payment_template_id.present?
    trigger_service.execute
    parent_arr_bill = parent_arrangement.arrange_billing
    work_achievement.save
    arr_paym.save
    arr_bill.save
    ArrangeBilling::UPDATABLE_BILLING_FIELDS.each do |field|
      arr_bill[field] = parent_arr_bill.send(field)
    end
    arr_bill.calculate_trigger :arrange_billing, ArrangeBilling::UPDATABLE_BILLING_FIELDS, true
  end

  def backup_staff_apply_data staff_apply_oc
    staff_apply_oc.compact.each &:create_backup_record
    self.update_remained_apply
  end

  def cancel_staff_apply_data staff_apply_oc
    staff_apply_oc.compact.each &:soft_delete_staff_apply
    self.update_remained_apply
  end

  def update_billing_field_5
    self.arrangements.each do |arrangement|
      arr_bill_special_offer_fee = arrangement.absence? ? 0 : self.special_offer_fee
      arr_bill = arrangement.arrange_billing
      arr_bill.update_column(:billing_field_5, arr_bill_special_offer_fee.to_i)
      arr_bill.calculate_trigger :arrange_billing, %i(billing_field_5), true
    end
  end

  def update_staff_apply_count
    self.update_columns(staff_apply_count: count_staff_applies.to_i)
  end

  def segment_trainning
    segment_id&.in?(TRAINING_SEGMENTS)
  end

  def segment_regular
    segment_id && segment_id == REGULAR_SEGMENT
  end

  def valid_cancel
    self.order_portions.none?(&:arranged?) && self.case_started_at > ServerTime.now &&
      !self.cancel? && (!training? || training_not_applicable?)
  end

  def timeout_apply_before_2_hour
    return haken_timeout_apply? unless self.regular_order?

    over_normal_apply_deadline? if self.regular_order? && is_last_order_case?
    false
  end

  def is_ended_next_day?
    self.case_started_at.strftime(Settings.date.formats) < self.case_ended_at
      .strftime(Settings.date.formats)
  end

  def haken_timeout_apply?
    return if self.regular_order?

    self.is_urgent ? over_urgent_apply_deadline? : over_normal_apply_deadline?
  end

  def registration_staff_timeout_apply? is_op_confirm
    return if is_op_confirm || ServerTime.now.to_date < Settings.release_staff_interview.to_date

    self.case_started_at < ServerTime.now + 10.hours
  end

  def over_normal_apply_deadline?
    self.case_started_at <= ServerTime.now
  end

  def over_urgent_apply_deadline?
    self.case_ended_at <= ServerTime.now + DEADLINE_APPLY
  end

  def full_arranged?
    return true if self.total_portion.zero?

    self.total_portion.positive? && self.total_portion == self.order_portions.select(&:arranged?).count
  end

  def exist_staff_confirmed?
    staff_apply_order_cases.by_staff_status(Staff.status_ids["op_confirm"]).not_rejected.present?
  end

  def exist_staff_applying?
    staff_apply_order_cases.by_staff_status(Staff.status_ids["op_confirm"]).not_processed.present?
  end

  def confirm_comming_work?
    self.case_started_at <= ServerTime.now + BEFORE_WORK_TIME
  end

  def can_show_unit_price_for_staff? staff
    data_date = self.case_started_at
    (staff.has_level_by_date?(data_date) && staff.has_rank_by_date?(data_date)) ||
      staff.current_level&.before_debut?
  end

  def can_add_calendar? arrangement
    self.case_started_at > ServerTime.now && !arrangement.work_achievement.is_added_calendar
  end

  def finished_working_time?
    ServerTime.now > self.case_ended_at
  end

  def is_day_working?
    CheckDayTimeWorking.new(self.case_started_at, self.case_ended_at).is_day_working?
  end

  def is_night_working?
    CheckDayTimeWorking.new(self.case_started_at, self.case_ended_at).is_night_working?
  end

  def valid_batch_order_case?
    remained_apply > 0 && self.order_portions.arranged.size < total_portion
  end

  def is_disable? rejected_order_case_ids
    self.timeout_apply_before_2_hour || rejected_order_case_ids.include?(self.id)
  end

  def is_both_day_and_night_working?
    CheckDayTimeWorking.new(self.case_started_at,
      self.case_ended_at).is_both_day_and_night_working?
  end

  def started?
    self.case_started_at <= ServerTime.now
  end

  def start_date_dash_format
    self.case_started_at.strftime(Settings.date.dash_separator)
  end

  def has_time_adjusment?
    staff_apply_order_cases.not_rejected.any?{|o| !o.is_used_original_condition}
  end

  def is_last_order_case?
    return true unless self.regular_order?

    self.case_ended_at == self.order.overall_ended_at
  end

  def is_regular_order
    self.regular_order?
  end

  # Deprecated: Will be removed in next phase of refactoring new_batch_arranges
  def recruit_process
    statuses = order_portions.pluck(:status_id)
    total_portion = statuses.size
    portion_statuses = OrderPortion::STATUSES
    total_canceled = total_status(statuses, portion_statuses[:cancel])
    return :cancel if statuses.blank? || total_canceled == total_portion

    available_apply = order_portions.any?(&:in_apply?)
    return :recruiting if available_apply && has_status?(statuses, portion_statuses[:not_arrange])
    return :adjusting if has_status?(statuses, portion_statuses[:temporary_arrange])

    out_of_apply = order_portions.all?{|portion| !portion.in_apply?}
    return :arranged if (available_apply || out_of_apply) && has_status?(statuses, portion_statuses[:arranged])

    :finish_recruiting
  end

  def segment_id_camelized
    segment_id.camelize(:lower)
  end

  def self.closed_apply_oc_ids order_case_ids, is_staff_confirmed
    return OrderCase.by_ids(order_case_ids).staff_register_expired_apply.pluck(:id).uniq unless
      is_staff_confirmed

    closed_apply_oc_ids = OrderCase.by_ids(order_case_ids).haken_expired_apply.pluck(:id)
    closed_apply_oc_ids += OrderCase.by_ids(order_case_ids).regular_expired_apply.pluck(:id)
    closed_apply_oc_ids.uniq
  end

  def job_category_key
    "#{location_id}_#{location_job_category_id || 0}"
  end

  def location_job_category_name
    job_category_name.presence || location_job_categories_text
  end

  def training_schedule_type_text
    return "" unless uncertain_start?

    I18n.t("staff.order_cases.scheduled_to_start")
  end

  def is_student_night_working?
    CheckDayTimeWorking.new(self.case_started_at, self.case_ended_at).is_student_night_working?
  end

  def is_student_day_working?
    CheckDayTimeWorking.new(self.case_started_at, self.case_ended_at).is_student_day_working?
  end

  private
  def payment_rate
    payment_rate = LocationPaymentRate.by_location(location_id).in_range_date(self.working_date).first
    payment_rate ||= PaymentRate.by_prefecture_corporation(location_prefecture_id, corporation_id)
      .in_range_date(self.working_date).first
    payment_rate ||= PaymentRate.by_all_corporation.by_prefecture(location_prefecture_id)
      .in_range_date(self.working_date).first
    payment_rate || PaymentRate.new
  end

  def payment_unit_price staff
    regular_order_arrange_payment.payment_basic_unit_price if self.regular_order?
    @payment_unit_price ||= PaymentUnitPrice.by_prefecture_and_national(
      self.location_prefecture_id, staff.nationality_other_than_japan?
    ).by_effective_date(self.case_started_at.to_date).first
  end

  def status_by_portion
    statuses = order_portions.reload.pluck(:status_id)
    portion_statuses = OrderPortion::STATUSES
    total_portion = self.order_portions.size
    total_canceled = total_status(statuses, portion_statuses[:cancel])
    return :cancel if statuses.blank? || total_canceled == total_portion

    available_apply = order_portions.any?(&:in_apply?)
    return :recruiting if available_apply && has_status?(statuses, portion_statuses[:not_arrange])

    return :adjusting if has_status?(statuses, portion_statuses[:temporary_arrange])

    out_of_apply = order_portions.all?{|portion| !portion.in_apply?}
    return :arranged if (available_apply || out_of_apply) && has_status?(statuses, portion_statuses[:arranged])

    :finish_recruiting
  end

  def working_status_by_work_achievement
    working_statuses = self.work_achievements.includes(arrangement: :order_portion).map do |wk|
      next unless wk.arrangement.order_portion.arranged?

      wk.working_time_status_id
    end.compact
    return :not_inputted if working_statuses.blank?

    statuses = WorkAchievement::WORKING_TIME_STATUSES
    total_absence = total_status(working_statuses, statuses[:absence])

    if total_absence == working_statuses.size
      :absence
    elsif has_status?(working_statuses, statuses[:owner_confirming])
      :not_approved
    elsif has_status?(working_statuses, statuses[:staff_confirming]) ||
      has_status?(working_statuses, statuses[:op_center_confirming])
      :confirming
    elsif has_status?(working_statuses, statuses[:not_inputted])
      :not_inputted
    else
      :approved
    end
  end

  def has_status? working_statuses, status
    working_statuses.include?(status)
  end

  def total_status statuses, target_status
    statuses.select{|status| status == target_status}.size
  end

  def update_arrangement_public_status
    return if remained_apply_before_last_save == remained_apply &&
      public_type_id_before_last_save == public_type_id

    arrangements = self.arrangements.includes(:order_portion)
    arrangements.each do |arrangement|
      update_status = Arrangement::MAPPING_DISPLAY_STATUS[arrangement.order_case_status.to_sym]
      next if arrangement.display_status_before_cast == update_status

      arrangement.update_column(:display_status_id, update_status)
    end
  end

  class << self
    def get_status_id_options statuses, i18n_key
      statuses.map do |key, value|
        {
          key: value,
          name: I18n.t("corporation.order_case.order_case_list_page.#{i18n_key}.#{key}")
        }
      end
    end

    def segment_ids_for_select
      segment_ids.map do |key, val|
        {
          id: val,
          key: key,
          name: I18n.t("enum_label.order_case.segment_ids.#{key}")
        }
      end
    end
  end

  def reset_cache_of_job_search
    return unless self.is_special_offer_changed? || self.is_urgent_changed? ||
      self.peak_period_order_rate_changed? || self.special_offer_fee_changed?

    CachingLocationJobsDataService.reload_cache(self.location_id, nil)
    JobsWage::CacheJobsWagesService.reload_cache(self)
  end
end
