class StaffEmploymentHistory < ApplicationRecord
  include StaffEntryStep2Validate

  strip_attributes

  acts_as_paranoid

  belongs_to :staff

  SERVICE_WORK_TYPES = {apparel: 1, tel: 2, food: 3, cash: 4, card: 5}
  OFFICE_WORK_TYPES = {affairs: 1, sale: 2, accounting: 3, call: 4}
  OTHER_WORK_TYPES = {food_factory: 1, manufacturing: 2, GS: 3, warehouse: 4}

  serialize :service_work_type, type: Array, coder: YAML
  serialize :office_work_type, type: Array, coder: YAML
  serialize :other_work_type, type: Array, coder: YAML

  before_save :convert_work_type_to_int

  private
  def convert_work_type_to_int
    service_work_type.map! &:to_i
    office_work_type.map! &:to_i
    other_work_type.map! &:to_i
  end
end
