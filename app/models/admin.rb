class Admin < ApplicationRecord
  include WardenFunction

  wardenable :recoverable, :authenticatable, :trackable

  mount_uploader :avatar, AdminAvatarUploader

  ADMIN_DEFAULT_START_DATE = Date.current
  ADMIN_ATTRIBUTES = [:role_id, :started_at, :stopped_at, :is_insurance_mail_receiver,
    :can_view_my_number, :avatar, :remove_avatar, :can_view_user_details,
    :can_update_billing_payment, :can_create_billing_payment_template,
    {admin_departments_attributes: [:id, :department_id, :_destroy]}]
  ROLE_ADMIN = 1
  ROLE_USER = 2

  belongs_to :account
  belongs_to :role
  has_many :admin_departments
  has_many :departments, through: :admin_departments
  has_many :admin_order_search_conditions
  has_many :admin_staff_search_conditions
  has_many :admin_corporation_search_conditions
  has_many :admin_location_search_conditions
  has_many :admin_training_schedule_search_conditions
  has_many :admin_search_conditions
  has_many :user_search_conditions
  has_many :admin_device_verification_search_conditions
  has_many :admin_device_login_log_search_conditions
  has_many :admin_arrange_search_conditions
  has_many :admin_corporation_group_tag_search_conditions
  has_many :admin_staff_contract_search_conditions
  has_many :foreign_employment_status_search_conditions
  has_many :admin_information_form_search_conditions
  has_many :admin_batch_arrange_search_conditions
  has_many :corporation_group_violations, as: :violation_day_updater, class_name: "CorporationGroup"
  has_many :arrange_logs
  has_many :admin_arrange_billing_search_conditions
  has_many :admin_payroll_search_conditions
  has_many :calculate_payroll_logs
  has_many :rank_level_calculation_logs
  has_many :staff_work_condition_export_logs
  has_many :admin_access_user_histories

  accepts_nested_attributes_for :admin_departments, reject_if: :all_blank, allow_destroy: true

  validates :role_id, presence: true
  validates :started_at, valid_date: true, presence: true
  validates :stopped_at, valid_date: true, date_greater_than: {with: :started_at, is_equal: true}
  validate :remove_department_attributes, on: :update
  validates :account_id, uniqueness: true
  validate :must_have_admin_department
  delegate :tel, :email, :name, :name_kana, to: :account, allow_nil: true
  delegate :admin_admin?, :admin_user?, :name, :permissions,
    :except_permissions, to: :role, prefix: true

  scope :search_by, ->keyword, fields = [] do
    return if keyword.blank?

    fields = %w(account_name account_name_kana) if fields.blank?
    group_conditions = RansackKeywordExtractor.or_contains keyword, fields
    ransack(m: "or", g: group_conditions).result
  end

  scope :role_admin, ->{where(role_id: ROLE_ADMIN).where.not(id: Settings.insurance_receiver_ids)}

  scope :active_admins, ->{where("stopped_at is Null or stopped_at >= ?", ServerTime.now)}

  scope :role_admin_user, -> do
    active_admins.where(role_id: [ROLE_ADMIN, ROLE_USER]).where.not(id: Settings.insurance_receiver_ids)
  end

  scope :is_insurance_mail_receiver, -> do
    active_admins.where(is_insurance_mail_receiver: true)
  end

  scope :can_view_my_number, -> do
    active_admins.where(can_view_my_number: true)
  end

  scope :can_update_billing_payment, -> do
    active_admins.where(can_update_billing_payment: true)
  end

  scope :can_create_billing_payment_template, -> do
    active_admins.where(can_create_billing_payment_template: true)
  end

  # ! Deprecated: not used
  # scope :search_all_text, ->keyword, fields = [] do
  #   return if keyword.blank?

  #   fields = %w(name) if fields.blank?
  #   query_condition = {m: "or", g: []}
  #   fields.map do |field|
  #     query_condition[:g] << {"#{field}_cont".to_sym => keyword.gsub("　", " ")}
  #     query_condition[:g] << {"#{field}_cont".to_sym => keyword.gsub(" ", "　")}
  #   end
  #   ransack(m: "or", g: [query_condition]).result
  # end

  def is_expired_login?
    return unless self.stopped_at

    Date.current >= self.stopped_at
  end

  def is_started?
    return unless self.started_at

    Date.current >= self.started_at
  end

  def can_authenticate?
    self.is_started? && !self.is_expired_login?
  end

  def active_for_authentication?
    super && can_authenticate?
  end

  def formatted_created_at
    self.created_at.strftime(Settings.date.formats)
  end

  def can_manage_verification?
    Ability.new(self).can? :manage, DeviceVerification
  end

  def get_search_condition model
    key = "admin:#{id}:search_conditions:#{model.name.underscore}"
    cached_content = redis_connector.get(key)
    return cached_content if cached_content.present?

    set_search_condition(model, {})
    {}
  end

  def set_search_condition model, search_condition
    key = "admin:#{id}:search_conditions:#{model.name.underscore}"
    redis_connector.set(key, search_condition, ex: 1.week)
  end

  class << self
    def options_for_select
      Admin.includes(:account).map{|admin| [admin.name, admin.id]}
    end

    def ransackable_attributes _auth_object = nil
      %w(account_id)
    end

    def ransackable_associations _auth_object = nil
      %w(account)
    end
  end

  private

  def redis_connector
    Lawson::RedisConnector.new
  end

  def must_have_admin_department
    return if self.admin_departments.present?

    errors.add :admin_departments, :no_admin_department
  end

  def remove_department_attributes
    departments_ids = self.admin_departments
      .select(&:marked_for_destruction?).pluck(:department_id)
    errors.add :admin_departments, :used_by_staff if StaffDepartment.by_department(departments_ids).present?
    return if departments_ids.blank? ||
      (CorporationGroup.by_pic_id_and_pic_department_id(self.id, departments_ids).empty? &&
        Corporation.by_pic_id_and_pic_department_id(self.id, departments_ids).empty?)

    errors.add :admin_departments, :can_not_remove_department
  end
end
