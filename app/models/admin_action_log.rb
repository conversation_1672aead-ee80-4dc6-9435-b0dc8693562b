class AdminActionLog < ApplicationRecord
  include ActionLogConcern
  acts_as_paranoid

  TARGET_DATA = %w(training_schedule)
  ACTION_LOG_METHODS = %w(target admin_name formatted_action_time content_text)
  SPECIAL_CONTENT_KEYS = {
    training_schedule: "person_in_charge_id"
  }

  belongs_to :admin
  belongs_to :target_object, polymorphic: true, optional: true

  delegate :name, to: :admin, prefix: true, allow_nil: true

  serialize :content, coder: YAML

  scope :search_by_admin, ->(admin_id){where(admin_id: admin_id)}

  def content_text
    case self.action_type.downcase.to_sym
    when :create, :delete, :restore, :auto_delete, :auto_restore
      simple_translation
    when :update
      update_translation
    when :admin_books_staff, :admin_set_staff_to_absent_with_notice, :admin_set_staff_to_absent_without_notice,
      :admin_set_staff_to_joined
      staff_translation
    when :admin_set_staff_reason_cancel
      admin_set_reason_translation
    else
      []
    end
  end

  private

  def simple_translation
    [
      I18n.t(
        "admin.admin_action_log.#{self.target_object_type.underscore}.#{self.action_type.downcase}",
        admin_name: admin_name_text
      )
    ]
  end

  def update_translation
    content_hash = content.first
    return simple_update_translation unless content_hash.is_a?(Hash)

    content_hash.map do |key, value|
      I18n.t(
        "admin.admin_action_log.#{self.target_object_type.underscore}.#{self.action_type.downcase}.#{key}",
        admin_name: admin_name_text,
        new_value: updated_value(key, value)
      )
    end
  end

  def simple_update_translation
    [
      I18n.t(
        "admin.admin_action_log.#{self.target_object_type.underscore}.#{self.action_type.downcase}.updated",
        admin_name: admin_name_text
      )
    ]
  end

  def staff_translation
    staff_info = Staff.with_deleted.find_by(id: self.content["staff_id"])&.account_name_with_staff_number
    [
      I18n.t(
        "admin.admin_action_log.#{self.target_object_type.underscore}.#{self.action_type.downcase}",
        admin_name: admin_name_text,
        staff_info: staff_info
      )
    ]
  end

  def admin_set_reason_translation
    staff_info = Staff.with_deleted.find_by(id: content["staff_id"])&.account_name_with_staff_number
    [
      I18n.t(
        "admin.admin_action_log.#{self.target_object_type.underscore}.#{self.action_type.downcase}",
        admin_name: admin_name_text,
        staff_info: staff_info,
        reason_text: self.content["staff_training_survey_answer"]
      )
    ]
  end

  def updated_value key, value
    return Admin.find_by(id: value)&.name if key == SPECIAL_CONTENT_KEYS[self.target_object_type.underscore.to_sym]

    value
  end

  def admin_name_text
    self.admin_name || I18n.t("admin.arrangements.modal.history_modal.content.system")
  end
end
