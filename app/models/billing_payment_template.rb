class BillingPaymentTemplate < ApplicationRecord
  acts_as_paranoid

  NUMBER_FIELDS = %i(billing_basic_unit_price billing_night_unit_price
    area_allowance short_allowance absence_discount tax_exemption
    payment_basic_unit_price payment_night_unit_price transportation_fee)
  TEMPLATE_ATTRS = %i(id name) + NUMBER_FIELDS
  TEMPLATE_METHODS = %i(location_name creator_name)
  CREATE_TEMPLATE_ATTRS = %i(name location_id) + NUMBER_FIELDS
  UPDATE_TEMPLATE_ATTRS = %i(name)
  BILLING_FIELDS = %i(payment_basic_unit_price payment_night_unit_price transportation_fee)
  PAYMENT_FIELDS = %i(billing_basic_unit_price billing_night_unit_price area_allowance
    short_allowance absence_discount tax_exemption)

  belongs_to :location
  belongs_to :admin, class_name: "Admin", foreign_key: :creator_id, optional: true
  delegate :name, to: :location, prefix: true

  validates :location_id, :name, presence: true
  validates(*NUMBER_FIELDS, numericality: {only_integer: true}, allow_nil: true)
  validates(*NUMBER_FIELDS.excluding(:absence_discount), numericality: {greater_than_or_equal_to: 0}, allow_nil: true)

  before_create :set_to_negative

  scope :by_location_id, ->location_id do
    return if location_id.blank?

    where(location_id: location_id)
  end

  scope :by_corporation_id, ->corporation_id do
    return if corporation_id.blank?

    joins(location: [corporation_group: :corporation])
      .where(locations: {corporation_groups: {corporation_id: corporation_id}})
  end

  scope :by_name, ->name do
    return if name.blank?

    where("billing_payment_templates.name LIKE ?", "%#{sanitize_sql_like(name)}%")
  end

  alias_attribute :payment_field_1, :transportation_fee
  alias_attribute :billing_field_1, :area_allowance
  alias_attribute :billing_field_2, :short_allowance
  alias_attribute :billing_field_4, :absence_discount
  alias_attribute :billing_tax_exemption, :tax_exemption

  def creator_name
    admin&.account&.name
  end

  private

  def set_to_negative
    return if self.absence_discount.blank? || self.absence_discount.to_i < 0

    self.absence_discount *= -1
  end
end
