class FieldName < ApplicationRecord
  acts_as_paranoid

  REDIS_KEY = "all_field_names"

  enum :type_id, {t_payment_deduction: 1, t_billing: 2}
  enum :subtype_id, {st_payment: 1, st_deduction: 2, st_billing: 3}
  enum :disposal, {day: 1, month: 2}

  after_commit :update_redis

  class << self
    def option_for_show_labels
      self.pluck(:field_key, :name).to_h.deep_symbolize_keys
    end

    def option_for_show_labels_from_redis
      JSON.parse(Lawson::RedisConnector.new.get(REDIS_KEY) || "{}").deep_symbolize_keys
    end
  end

  private

  def update_redis
    redis = Lawson::RedisConnector.new
    all_fields = self.class.option_for_show_labels
    redis.set REDIS_KEY, all_fields.to_json
  end
end
