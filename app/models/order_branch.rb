class OrderBranch < ApplicationRecord
  acts_as_paranoid
  include ExportOrderSearchDecorator
  include ExportOrderBranchUnitPriceDecorator
  include ExportOrderPicDecorator

  attr_accessor :rest1_editable, :rest2_editable, :rest3_editable, :selected_dates, :bill_paym_options,
    :estimate_total_portion, :is_copied, :create_by_arrangement, :arrangement_staff_id, :change_arrangement_time,
    :data_info, :is_range, :location_job_category_id, :invoice_target, :must_validate_required_time

  TYPE_INDIVIDUAL = "individual_order"

  ORDER_BRANCH_ATTRS = [:id, :started_at, :ended_at, :week_count, :is_except_holiday, :is_sunday,
    :is_monday, :is_tuesday, :is_wednesday, :is_thursday, :is_friday, :is_saturday,
    :working_start_time, :working_end_time, :break_time, :staff_count, :predetermined_time,
    :estimation, :is_time_changable, :is_urgent, :sequence_no, :is_special_offer,
    :special_offer_fee, :special_offer_note, :rest1_started_at, :rest1_ended_at,
    :rest2_started_at, :rest2_ended_at, :rest3_started_at, :rest3_ended_at,
    :rest1_editable, :rest2_editable, :rest3_editable, :_destroy, :create_by_arrangement,
    :arrangement_staff_id, :selected_dates, :payment_basic_unit_price, :payment_night_unit_price,
    :billing_unit_price, :billing_night_unit_price, :required_start_time, :required_end_time, :is_range]
  INCLUDE_FIELDS = [order: [corporation_group: [:pic, :pic_department],
    location: [:stations_1, :stations_2, :stations_3, :stations_4]]]
  REST_TIMES = %w(1 2 3)
  HOUR_IN_DAY = 24
  SECOND_IN_HOUR = 3600
  SECOND_PER_MINUTE = 60
  WORKING_TIME_ATTRS = %i(working_start_time working_end_time rest1_started_at rest1_ended_at
    rest2_started_at rest2_ended_at rest3_started_at rest3_ended_at)
  WORKING_TIME_FULL_ATTRS = WORKING_TIME_ATTRS + %i(is_time_changable required_start_time required_end_time)
  MONTH_START_DAY = 16
  START_NIGHT = "22:00"
  END_NIGHT = "05:00"
  REQUIRED_START_NIGHT = "21:45"
  REQUIRED_END_NIGHT = "05:15"
  DAY_OF_WEEK_ATTRS = %i(is_sunday is_monday is_tuesday is_wednesday is_thursday is_friday is_saturday)

  belongs_to :order
  has_many :order_cases, dependent: :destroy
  has_many :order_portions, dependent: :destroy
  has_many :staff_apply_order_cases
  has_many :arrangements
  has_many :work_achievements, through: :arrangements
  has_many :arrange_payments, through: :arrangements
  has_many :arrange_billings, through: :arrangements
  has_many :migration_arrangement_histories, through: :arrangements
  has_many :children_order_branches, foreign_key: :original_branch_id, class_name: "OrderBranch"

  validates :order, presence: true
  validates :started_at, :ended_at, :working_start_time, :working_end_time,
    :staff_count, :predetermined_time, presence: true
  validate :collective_order_rule, :violation_day_rule, :working_time_rule, :break_time_rule,
    :closed_day_rule
  validates :staff_count, numericality: {only_integer: true, greater_than_or_equal_to: 1,
    less_than_or_equal_to: 100}
  validates :started_at, in_future: true,
    unless: proc{is_migrated || create_by_arrangement || change_arrangement_time}
  validates :special_offer_fee, presence: true, if: :is_special_offer
  validates :rest1_started_at, presence: true, if: proc{|o| o.rest2_editable || o.rest1_ended_at}
  validates :rest1_ended_at, presence: true, if: proc{|o| o.rest2_editable || o.rest1_started_at}
  validates :rest2_started_at, :rest2_ended_at, presence: true, if: :rest2_editable
  validates :rest3_started_at, :rest3_ended_at, presence: true, if: :rest3_editable
  validates :rest1_ended_at, value_greater_than: :rest1_started_at
  validates :rest2_ended_at, value_greater_than: :rest2_started_at
  validates :rest3_ended_at, value_greater_than: :rest3_started_at
  validates :sequence_no, uniqueness: {scope: :order_id}, if: :is_migrated
  validate :staff_count_must_same_value, :check_overlap_working_time
  validate :rest_time_range_rule, :rest_time_overlap_rule, unless: :is_migrated
  validate :payroll_locked_rule, :billing_locked_rule, :create_arrangement_rule,
    :staff_created_at_rule, if: :create_by_arrangement, unless: :is_migrated
  validates :required_start_time, :required_end_time, presence: true, if: :validate_required_time?
  validate :required_time_rule, if: :validate_required_time?

  after_create :create_order_cases
  before_validation :set_rest_time_date, :set_break_time, :set_required_time_date
  before_validation :set_selected_day, if: proc{is_migrated && !is_except_holiday && order.batch?}

  delegate :is_migrated, to: :order, allow_nil: true

  def calculate_relate_field_after_assign_attr data = nil, action = :create # rubocop:disable Metrics/PerceivedComplexity
    return unless self.started_at

    self.send :set_break_time
    self.estimate_total_portion = 0
    estimate_data = self.estimate_price_params
    if order.individual?
      order_case_num = 1
      result = [EstimateOrderPriceService.individual_order(estimate_data, true)]
      return if result.compact.empty?

      ended_at = self.started_at
      estimation = result.map{|r| r[:summary]}.reduce(0, :+)
    elsif order.template?
      if self.is_range
        DAY_OF_WEEK_ATTRS.each{|wday| self[wday] = true}
        ended_at = self.ended_at
      else
        wday = DAY_OF_WEEK_ATTRS[self.started_at.to_date.wday]
        self[wday] = true
        ended_at = self.started_at
      end
      result = EstimateOrderPriceService.template_order(estimate_data, true)
      order_case_num = result.count
      return if result.compact.empty?

      estimation = result.map{|r| r[:summary]}.reduce(0, :+)
    elsif order.batch?
      ended_at = action == :create ? self.started_at + ((7 * self.week_count.to_i) - 1).days :
        self.ended_at
      estimate_data = estimate_data.merge(data) unless data.nil?
      result = EstimateOrderPriceService.collective_order(estimate_data, true)
      order_case_num = result.count
      return if result.compact.empty?

      estimation = result.map{|r| r[:summary]}.reduce(0, :+)
    elsif order.non_recurring?
      return if self.ended_at.nil?

      order_case_num = 1
      result = EstimateOrderPriceService.non_recurring_order(estimate_data, true)
      return if result.compact.empty?

      ended_at = self.ended_at
      ## Todo: Wait for the calculation
      estimation = 0
    elsif order.recurring?
      return if self.ended_at.nil?

      order_case_num = 1
      result = EstimateOrderPriceService.recurring_order(estimate_data, true)
      return if result.compact.empty?

      ended_at = self.ended_at
      ## Todo: Wait for the calculation
      estimation = 0
    end
    self.estimation = estimation
    self.estimate_total_portion = self.staff_count.to_i * order_case_num
    self.ended_at = ended_at
    self.predetermined_time = count_predetermined_time
    set_is_time_changable
  end

  def calculate_relate_field_when_migrate
    self.estimate_total_portion = 0
    if order.individual?
      order_case_num = 1
      ended_at = self.started_at
    elsif order.batch?
      ended_at = self.started_at + ((7 * self.week_count.to_i) - 1).days
      result = EstimateOrderPriceService.days_of_order(self.started_at,
        get_selected_days.map(&:to_i), self.week_count, self.is_except_holiday)
      order_case_num = result.count
    end

    self.estimation = 0
    self.estimate_total_portion = self.staff_count.to_i * order_case_num
    self.ended_at = ended_at
    self.predetermined_time = count_predetermined_time
  end

  def estimate_price_params
    {
      started_at: self.started_at,
      ended_at: self.ended_at,
      working_start_time: self.working_start_time&.strftime(Settings.time.formats),
      working_end_time: self.working_end_time&.strftime(Settings.time.formats),
      prefecture_id: self.order.location.prefecture_id,
      staff_count: self.staff_count,
      selected_days: get_selected_days.join(","),
      week_count: self.week_count,
      is_except_holiday: self.is_except_holiday,
      is_time_changable: self.is_time_changable,
      is_special_offer: self.is_special_offer,
      special_offer_fee: self.special_offer_fee,
      corporation_id: self.order.corporation_id,
      location_id: self.order.location_id,
      rest1_started_at: self.rest1_started_at&.strftime(Settings.time.formats),
      rest1_ended_at: self.rest1_ended_at&.strftime(Settings.time.formats),
      rest2_started_at: self.rest2_started_at&.strftime(Settings.time.formats),
      rest2_ended_at: self.rest2_ended_at&.strftime(Settings.time.formats),
      rest3_started_at: self.rest3_started_at&.strftime(Settings.time.formats),
      rest3_ended_at: self.rest3_ended_at&.strftime(Settings.time.formats),
      special_offer_note: self.special_offer_note,
      order_created_at: self.order.created_at,
      selected_dates: self.selected_dates,
      is_range: self.is_range || (self.ended_at && self.ended_at > self.started_at)
    }
  end

  def detail_info data
    payment_rate = data[:payment_rates][self.started_at.to_date.to_s]
    if self.order_cases.present? && !self.is_copied
      if order.regular_order?
        arr_billing = self.arrange_billings[0]
        arr_payment = self.arrange_payments[0]
        regular_order_price = {
          billing_basic_unit_price: arr_billing.billing_basic_unit_price,
          billing_night_unit_price: arr_billing.billing_night_unit_price,
          payment_basic_unit_price: arr_payment.payment_basic_unit_price,
          payment_night_unit_price: arr_payment.payment_night_unit_price
        }
        ocs = order_cases.where("total_portion > 0")
        order_case_info = ocs.map do |order_case|
          order_case.calculate_price(nil, regular_order_price)
        end
      else
        order_case_info = order_cases.map do |order_case|
          order_case.calculate_price(payment_rate)
        end
      end
    elsif order.individual?
      order_case_info = [EstimateOrderPriceService.individual_order(estimate_price_params)]
    elsif order.template?
      order_case_info = EstimateOrderPriceService.template_order(estimate_price_params.merge(data))
    else
      order_case_info = EstimateOrderPriceService.collective_order(estimate_price_params.merge(data))
    end
    order_case_info = order_case_info.compact
    format_info = {
      started_at: self.started_at&.strftime("%Y/%m/%d"),
      ended_at: self.ended_at&.strftime("%Y/%m/%d"),
      selected_days: self.get_selected_days.join(","),
      working_end_time: self.working_end_time&.strftime("%H:%M"),
      working_start_time: self.working_start_time&.strftime("%H:%M"),
      total_estimation: order_case_info.sum{|detail_info| detail_info[:summary]},
      estimate_data: order.individual? ? order_case_info[0] : order_case_info
    }

    self.as_json(
      except: [
        :order_id, :started_at, :ended_at, :working_start_time,
        :working_end_time, :created_at, :updated_at, :sequence_no
      ]
    ).merge(format_info)
  end

  def get_selected_days
    selected_days = []
    Date::DAYNAMES.each do |day_name|
      selected_days << day_name.to_date.wday if self.send("is_#{day_name.downcase}").to_s.true?
    end
    selected_days
  end

  def update_total_portion
    self.update_column(:total_portion, self.order_portions.valid_status.count)
  end

  def create_order_cases
    return if !order.confirmed? || self.create_by_arrangement || self.change_arrangement_time ||
      self.order_cases.present?

    if self.is_migrated
      service = MigrateOrderCaseService.new(self, order)
    else
      service = ImportDataOrderBranchService.new(self, order, bill_paym_options)
    end
    service.location_job_category_id = location_job_category_id
    service.invoice_target = invoice_target || order&.location&.default_invoice_target
    service.import_data
    self.update_total_portion
    order.update_total_order_portions
  end

  def work_time
    [self.working_start_time&.strftime(Settings.time.formats),
      self.working_end_time&.strftime(Settings.time.formats)].join("～")
  end

  def rest_time
    (1..3).map do |index|
      next unless self.send("rest#{index}_started_at")

      [self.send("rest#{index}_started_at").strftime(Settings.time.formats),
        self.send("rest#{index}_ended_at").strftime(Settings.time.formats)].join("～")
    end.compact.join(I18n.t("common.comma"))
  end

  def week_count_name
    start_date = I18n.l self.started_at, format: Settings.date.day_and_date
    weeks = I18n.t("corporation.order_case.order_case_detail.week",
      week: self.week_count)
    except_holiday = I18n.t("corporation.order_case.order_case_detail.exclude_holiday") if self.is_except_holiday
    [start_date, [weeks, except_holiday].join(" ")].join("～")
  end

  def break_time_format_hour
    return unless break_time

    [break_time / 60, break_time % 60].map{|t| t.to_s.rjust(2, "0")}.join(":")
  end

  def contract_time
    time = (self.working_end_time - self.working_start_time).round.abs

    Time.strptime(time.to_s, "%s").utc.strftime("%H:%M")
  end

  def current_month_by_working_date
    payroll_date =
      started_at.day < Settings.payroll.first_day_of_month ? started_at : started_at + 1.month
    Admin::AdminsController.helpers
      .salary_calculating_range payroll_date.strftime(Settings.date.formats)
  end

  def actual_break_time
    (1..3).map do |index|
      next unless self.send("rest#{index}_started_at")

      [self.send("rest#{index}_started_at").strftime(Settings.time.formats),
        self.send("rest#{index}_ended_at").strftime(Settings.time.formats)].join("～")
    end.compact.join(I18n.t("common.comma"))
  end

  def original_time_with_breaks
    end_time = self.working_end_time
    return unless self.working_start_time && end_time

    end_time += 1.day if end_time < self.working_start_time
    (end_time - self.working_start_time).to_i / SECOND_PER_MINUTE
  end

  class << self
    def caculate_started_and_ended_at selected_dates
      tmp_dates = selected_dates.split(",")
      {started_at: tmp_dates.min, ended_at: tmp_dates.max}
    end

    def data_info order_branches
      started_ats = order_branches.map(&:started_at).uniq.compact.reject(&:blank?)
      prefecture_id = order_branches[0].order.location.prefecture_id
      corporation_id = order_branches[0].order.corporation_id
      location_id = order_branches[0].order.location_id
      option_payment_rates = {}
      payment_rates = {}
      started_ats.each do |started_at|
        date = started_at.to_date
        payment_rates[date.to_s] = EstimateOrderPriceService.get_base_price(
          prefecture_id: prefecture_id,
          corporation_id: corporation_id,
          location_id: location_id,
          started_at: date
        )

        option_payment_rates[date.to_s] = OptionPaymentRate.by_prefecture_corporation(prefecture_id,
          corporation_id).in_range_date(started_at).first
        option_payment_rates[date.to_s] ||= OptionPaymentRate.where(prefecture_id: prefecture_id,
          is_all: true).in_range_date(started_at).first
      end
      peak_periods = PeakPeriod.by_target_dates(started_ats).index_by(&:target_date)

      {
        payment_rates: payment_rates,
        peak_periods: peak_periods,
        option_payment_rates: option_payment_rates,
        corporation: order_branches[0].order.corporation
      }
    end
  end

  def selected_dates_by_type
    return [] if started_at.nil? || ended_at.nil?

    if order.non_recurring?
      self.selected_dates.split(",")
    else
      EstimateOrderPriceService.days_of_recurring_order(started_at, ended_at, is_except_holiday,
        get_selected_days).map(&:to_s)
    end
  end

  private
  def collective_order_rule
    return unless self.order&.batch?

    errors.add(:is_saturday, :blank) if self.send(:get_selected_days).blank?
    errors.add(:week_count, :blank) if week_count == 0
  end

  def violation_day_rule
    acception_date = self.order&.corporation_group_haken_acceptance_started_at&.to_date
    return if self.started_at.blank? || self.is_migrated

    started_at = self.started_at.to_date
    ended_at = self.ended_at&.to_date
    violation_day = self.order.violation_day&.to_date
    if violation_day.present?
      is_invalid_date = started_at >= violation_day || (ended_at && ended_at >= violation_day)
      is_invalid_date = started_at < acception_date if acception_date.present? && !is_invalid_date
      errors.add(:started_at, :out_of_violation_day) if is_invalid_date
    else
      errors.add(:started_at, :violation_day_blank)
    end
  end

  def working_time_rule
    return if self.is_migrated || self.change_arrangement_time

    if self.create_by_arrangement
      errors.add(:working_end_time, :zero) if self.working_start_time == self.working_end_time
      return
    end
    if self.started_at && self.working_start_time
      minimum_start_time = Settings.order.minimum_start_time.hours.from_now.beginning_of_minute
      start_time = ServerTime.parse("#{self.started_at.strftime Settings.date.formats}
      #{self.working_start_time.strftime Settings.time.formats}")
      errors.add(:working_start_time, :minimum) if start_time < minimum_start_time
    end
    errors.add(:working_end_time, :zero) if self.working_start_time == self.working_end_time
  end

  def break_time_rule
    return if self.working_start_time.blank? || self.working_end_time.blank? || self.is_migrated

    time_diff = (working_end_time - working_start_time).to_i / 60
    return if time_diff == 0

    time_diff += Settings.time.minutes_per_day if time_diff < 0
    hour_diff = time_diff.to_f / 60
    errors.add(:working_end_time, :over_12) if hour_diff > 12
    working_time = time_diff - self.break_time.to_i

    over_working_time_limit_rule working_time, time_diff
  end

  def over_working_time_limit_rule working_time, time_diff
    if working_time > Settings.order.working_time_limit_1 &&
      working_time <= Settings.order.working_time_limit_2 &&
      self.break_time.to_i < Settings.order.break_time_at_limit_1
      if time_diff - Settings.order.working_time_limit_1 < Settings.order.break_time_at_limit_1
        errors.add(error_field, :over_working_time_limit_1, break_time: time_diff -
          Settings.order.working_time_limit_1)
      else
        errors.add(error_field, :over_working_time_limit_1,
          break_time: Settings.order.break_time_at_limit_1)
      end
    elsif working_time > Settings.order.working_time_limit_2 &&
      self.break_time.to_i < Settings.order.break_time_at_limit_2
      if time_diff - Settings.order.working_time_limit_2 < Settings.order.break_time_at_limit_2
        errors.add(error_field, :over_working_time_limit_2, break_time: time_diff -
          Settings.order.working_time_limit_2)
      else
        errors.add(error_field, :over_working_time_limit_2,
          break_time: Settings.order.break_time_at_limit_2)
      end
    elsif working_time <= 0
      errors.add(error_field, :too_much)
    end
  end

  def closed_day_rule
    location_closed_at = self.order&.location&.closed_at
    return if location_closed_at.blank? || self.started_at.blank?

    started_at = self.started_at.to_date
    ended_at = self.ended_at&.to_date
    closed_day = location_closed_at.to_date
    options = {after: closed_day, before: started_at}
    start_or_end_gte_closed_day = started_at >= closed_day || (ended_at && ended_at >= closed_day)
    errors.add(:started_at, :out_of_closed_day, options) if start_or_end_gte_closed_day
  end

  def set_rest_time_date
    REST_TIMES.each do |rest_time|
      start_time = self.send("rest#{rest_time}_started_at")
      end_time = self.send("rest#{rest_time}_ended_at")
      if start_time && working_start_date_time && start_time < working_start_date_time
        start_time = self["rest#{rest_time}_started_at"] = start_time + 1.day
        end_time = self["rest#{rest_time}_ended_at"] = end_time + 1.day if end_time
      end
      self["rest#{rest_time}_ended_at"] = end_time + 1.day if start_time && end_time &&
        end_time < start_time
    end
  end

  def working_start_date_time
    return unless working_start_time

    ServerTime.parse(working_start_time.strftime(Settings.time.formats))
  end

  def working_end_date_time
    return if working_end_time.blank? || working_start_time.blank?

    end_time = if self.is_migrated
                 start_date = self.started_at.strftime(Settings.date.formats)
                 end_at = self.working_end_time.strftime(Settings.time.formats)
                 "#{start_date} #{end_at}".in_time_zone
               else
                 ServerTime.parse(working_end_time.strftime(Settings.time.formats))
               end
    start_time_format = working_start_time.strftime(Settings.time.formats)
    end_time_format = working_end_time.strftime(Settings.time.formats)
    end_time += 1.day if end_time_format < start_time_format
    end_time
  end

  def set_break_time
    total_rest_time = 0
    REST_TIMES.each do |rest_time|
      start_time = self["rest#{rest_time}_started_at"]
      end_time = self["rest#{rest_time}_ended_at"]
      next unless start_time && end_time

      total_rest_time +=
        end_time >= start_time ? end_time - start_time : end_time + HOUR_IN_DAY.hours - start_time
    end
    self.break_time = total_rest_time.minutes / SECOND_IN_HOUR
  end

  def rest_time_range_rule
    return unless working_end_date_time

    REST_TIMES.each do |rest_time|
      start_time = self.send("rest#{rest_time}_started_at")
      end_time = self.send("rest#{rest_time}_ended_at")
      if start_time && start_time >= working_end_date_time
        errors.add "rest#{rest_time}_started_at", :in_range
      elsif end_time && end_time > working_end_date_time
        errors.add "rest#{rest_time}_ended_at", :in_range
      end
    end
  end

  def rest_time_overlap_rule
    check_overlap REST_TIMES[1], REST_TIMES[0]
    check_overlap REST_TIMES[2], REST_TIMES[0]
    check_overlap REST_TIMES[2], REST_TIMES[1]
  end

  def check_overlap rest_time, compare_time
    rest_time_start = self.send("rest#{rest_time}_started_at")
    rest_time_end = self.send("rest#{rest_time}_ended_at")
    compare_time_start = self.send("rest#{compare_time}_started_at")
    compare_time_end = self.send("rest#{compare_time}_ended_at")
    return if compare_time_start.blank? || compare_time_end.blank? ||
      rest_time_start.blank? || rest_time_end.blank?

    if (rest_time_start >= compare_time_start && rest_time_start < compare_time_end) ||
      (compare_time_start >= rest_time_start && compare_time_start < rest_time_end)
      errors.add "rest#{rest_time}_started_at", :overlap
    end
  end

  def error_field
    rest_time_field = :rest1_started_at
    REST_TIMES.each do |rest_time|
      start_time = self.send("rest#{rest_time}_started_at")
      end_time = self.send("rest#{rest_time}_ended_at")
      rest_time_field = "rest#{rest_time}_started_at" if start_time.present? && end_time.present?
    end
    rest_time_field
  end

  def count_predetermined_time
    end_time = self.working_end_time
    return unless self.working_start_time && end_time

    end_time += 1.day if end_time < self.working_start_time
    ((end_time - self.working_start_time).to_i / SECOND_PER_MINUTE) - break_time.to_i
  end

  def create_arrangement_rule
    return unless self.started_at && self.working_start_time

    start_time = ServerTime.parse("#{self.started_at.strftime Settings.date.formats}
    #{self.working_start_time.strftime Settings.time.formats}")
    errors.add(:working_start_time, :maximum) if start_time > ServerTime.now
  end

  def billing_locked_rule
    return unless self.started_at

    month_billing = MonthBilling.by_corporation_id(self.order.corporation_id)
      .by_billing_date_confirmed(self.started_at.beginning_of_month.to_date)
    errors.add :started_at, :billing_locked if month_billing.present?
  end

  def payroll_locked_rule
    return unless self.started_at && arrangement_staff_id

    payroll_date = self.current_month_by_working_date[1].beginning_of_month.to_date
    payroll = Payroll.of_staffs(payroll_date, [arrangement_staff_id])
    errors.add(:started_at, :payroll_locked) if payroll.present?
  end

  def set_selected_day
    Holiday.by_range_date(self.started_at, self.ended_at).each do |holiday|
      day_name = holiday.holiday.strftime(Settings.date.weekdays).downcase
      self["is_#{day_name}"] = true
    end
  end

  def staff_created_at_rule
    return unless self.started_at && arrangement_staff_id.present?

    staff = Staff.find_by(id: arrangement_staff_id)
    valid_started_date = staff && self.started_at.to_date >= staff.created_at.to_date
    errors.add(:started_at, :over_staff_created_at) unless valid_started_date
  end

  def set_is_time_changable
    if order.regular_order?
      self.is_time_changable = false
    elsif order.template? && !self.is_time_changable? &&
      (working_start_time.to_formatted_s(:time) == END_NIGHT || working_end_time.to_formatted_s(:time) == START_NIGHT)
      self.is_time_changable = true
      if working_start_time.to_formatted_s(:time) == END_NIGHT
        self.required_start_time = REQUIRED_END_NIGHT
        self.required_end_time = working_end_time.to_formatted_s(:time)
      end
      if working_end_time.to_formatted_s(:time) == START_NIGHT
        self.required_end_time = REQUIRED_START_NIGHT
        self.required_start_time = working_start_time.to_formatted_s(:time)
      end
    else
      return unless working_start_time.to_formatted_s(:time) == END_NIGHT ||
        working_end_time.to_formatted_s(:time) == START_NIGHT

      self.is_time_changable = true
    end
  end

  def staff_count_must_same_value
    return unless order&.regular_order?

    order_branch_first = order.order_branches.first
    return if order_branch_first.nil? || staff_count == order_branch_first.staff_count

    errors.add :staff_count, :not_same_value
  end

  def check_overlap_working_time
    return if !order&.regular_order? || working_start_time.nil? || working_end_time.nil?

    check_time = CheckDayTimeWorking.new(working_start_time, working_end_time)
    error_field = :selected_dates
    error_field = :started_at if order.recurring?
    order.order_branches.each do |ob|
      next if ob.equal?(self) || ob.started_at.nil? || ob.ended_at.nil?

      dates = selected_dates_by_type & ob.selected_dates_by_type
      next if dates.blank? || ob.working_start_time.nil? || ob.working_end_time.nil?

      in_range = {start_time: ob.working_start_time, end_time: ob.working_end_time}
      errors.add error_field, :overlap if check_time.overlap? in_range
    end
  end

  def set_required_time_date
    start_time = self.required_start_time
    end_time = self.required_end_time
    if start_time && working_start_date_time && start_time < working_start_date_time
      start_time = self.required_start_time = start_time + 1.day
      end_time = self.required_end_time = end_time + 1.day if end_time
    end
    self.required_end_time = end_time + 1.day if start_time && end_time && end_time < start_time
  end

  def required_time_rule
    return unless working_end_date_time

    if required_start_time && required_start_time >= working_end_date_time
      errors.add :required_start_time, :in_range
    elsif required_end_time && required_end_time > working_end_date_time
      errors.add :required_end_time, :in_range
    end
  end

  def validate_required_time?
    return unless must_validate_required_time && is_time_changable?

    is_time_changable_changed? || required_start_time_changed? || required_end_time_changed?
  end
end
