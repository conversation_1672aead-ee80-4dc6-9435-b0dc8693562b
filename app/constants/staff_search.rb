module StaffSearch
  SEARCH_TYPES = %w(sort_string integer string date string_not_query date_nested
    string_wildcard nested_contract_histories arranged_arrangements nested_payrolls
    nested_staff_notifications)
  SEARCH_BY_STRING_WILDCARD = %w(account_name_lower account_name_kana)
  SEARCH_BY_INTEGER = %w(admin_id id nationality prefecture_id
    last_contract_indefinite_employment_flag_before_type_cast staff_number_int
    level_before_type_cast haken_type_before_type_cast)
  SEARCH_BY_FREE_WORDS = %w(name name_kana)
  SEARCH_BY_STRING = %w(gender_id status_id staff_type current_department_id
    occupation staff_status level_up_training_format contract_status foreign_id
    residence_status staff_number my_number_status_id signup_device has_experience has_applied)
  SEARCH_BY_EMAIL = %w(email account_email)
  SEARCH_BY_TEL = %w(tel)
  SEARCH_BY_BOOLEAN = %w(updated_profile)
  SEARCH_BY_DATE = %w(register_date birthday_format contract_start_date_format
    contract_end_date_format retirement_day_format hire_date_format expired_start_date_format
    expired_end_date_format absence_start_date_format absence_end_date_format residence_validity_format
    residence_expiration_date_format retirement_date_search payrolls_date debut_date_format)
  SEARCH_BY_DATE_NESTED = %w(nested_staff_messages)
  SEARCH_BY_SORT_STRING = %w(current_department_name evaluation total_inlate total_absence)
  SEARCH_BY_MATCH_PHRASE_PREFIX = %w(name name_kana)
  SEARCH_BY_INTEGER_RANGE = %w(evaluation_format)
  SEARCH_BY_ADDRESS = %w(prefecture_name city street_number building address_kana)
  SEARCH_BY_STRING_NOT_QUERY = %w(arrangement_type_str oc_public_staff_ids)
  SEARCH_BY_NESTED_CONTRACT_HISTORIES = %w(nested_contract_histories)
  SEARCH_BY_ARRANGED_ARRANGEMENTS = %w(arranged_arrangements)
  SEARCH_BY_NESTED_PAYROLLS = %w(nested_payrolls)
  SEARCH_BY_NESTED_STAFF_NOTIFICATIONS = %w(nested_staff_notifications)
  COMMON_FIELDS = SEARCH_BY_INTEGER + SEARCH_BY_FREE_WORDS
  ALL_SEARCH_FIELDS = COMMON_FIELDS + SEARCH_BY_EMAIL + SEARCH_BY_DATE + SEARCH_BY_TEL +
    SEARCH_BY_SORT_STRING + SEARCH_BY_ADDRESS +
    SEARCH_BY_STRING + SEARCH_BY_INTEGER_RANGE + SEARCH_BY_STRING_NOT_QUERY +
    SEARCH_BY_DATE_NESTED + SEARCH_BY_STRING_WILDCARD + SEARCH_BY_NESTED_CONTRACT_HISTORIES +
    SEARCH_BY_ARRANGED_ARRANGEMENTS + SEARCH_BY_NESTED_PAYROLLS + SEARCH_BY_BOOLEAN +
    SEARCH_BY_NESTED_STAFF_NOTIFICATIONS
end
