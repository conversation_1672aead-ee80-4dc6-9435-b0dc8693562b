class Admin::UsersService < Admin::AdminService
  include LoadDataForSelect2
  ASSOCIATION_MIN_PARAMS = 2

  def new
    selected_corporation = Corporation.first
    {
      user: User.new(started_at: User::USER_DEFAULT_START_DATE),
      account: Account.new,
      corporations: [[selected_corporation.full_name, selected_corporation.id]]
    }
  end

  def edit
    user = find_user
    AdminInformationHidingService.new(user).hide
    selected_corporation = user.corporation
    {
      user: user,
      account: user.account,
      corporations: [[selected_corporation.full_name, selected_corporation.id]]
    }
  end

  def create
    account = Account.find_by(email: account_params[:email]) || Account.new
    account.assign_attributes account_params.merge validate_email_exists: true
    user = User.new user_params
    begin
      ActiveRecord::Base.transaction do
        account.save!
        user.account_id = account.id
        user.save!
      end
      add_user_corporation_group_belong_group user
      AdminMailer.reset_password_mail_user(user, user.set_reset_password_token).deliver_now
      flash[:success] = get_success_message_for user
      response_action account, user, true
    rescue ActiveRecord::RecordInvalid
      response_action account, user, false
    end
  end

  def update
    user = find_user
    account = user.account
    begin
      account.assign_attributes account_params
      ActiveRecord::Base.transaction do
        user.assign_attributes user_params
        account.save!
        user.save!
      end
      add_user_corporation_group_belong_group user
      flash[:success] = get_success_message_for user
      response_action account, user, true
    rescue ActiveRecord::RecordNotFound
      flash[:alert] = t "admin.users.messages.update_failure"
      response_action account, user, true
    rescue ActiveRecord::RecordInvalid
      user.valid?
      response_action account, user, false
    end
  end

  def index
    case request_format
    when :html
      corporations = load_data_for_select2(:user_search_conditions, :corporation_id, Corporation)
      locations = load_data_for_select2(:user_search_conditions, :location_id, Location)
      {corporations: corporations, locations: locations}
    when :json
      users = UserSearch.new(params).search
      {
        page: users.current_page,
        users: users.as_json(
          only: [:id, :corporation_id, :role_id],
          methods: [:corporation_full_name, :role_name, :name]
        ),
        total_items: users.total_count
      }
    end
  end

  def destroy
    user = find_user
    if user.destroy
      flash[:success] = t "admin.users.modal.delete_success"
    elsif user.errors.include? :orders
      flash[:danger] = user.errors.messages[:orders].first
    else
      flash[:danger] = t "admin.users.modal.delete_failure"
    end
  end

  def load_selected_corporation_groups_and_locations
    user = User.find_by id: params[:user_id]
    return {data: {}} if user.blank?

    user_groups_json = user.user_groups.as_json(only: [:id, :corporation_group_tag_id])
    usr_corporation_groups = user.user_corporation_groups
    usr_copr_grps_json = usr_corporation_groups.as_json only: [:id, :corporation_group_id]
    usr_locations = user.user_locations
    usr_locations_json = usr_locations.as_json only: [:id, :location_id]
    {
      data: {
        user_groups: user_groups_json,
        user_corporation_groups: usr_copr_grps_json,
        user_locations: usr_locations_json
      }
    }
  end

  def reset_password_user
    user = find_user
    account = user.account
    if account.update(encrypted_password: account.original_encrypted_password,
      skip_password_validation: true)
      flash[:success] = t("admin.users.reset_password_success")
    else
      flash[:danger] = t("admin.users.reset_password_failed")
    end
    {
      status: true,
      errors: [],
      redirect_path: edit_admin_user_path(user)
    }
  end

  def resend_reset_password_mail
    user = find_user
    if AdminMailer.reset_password_mail_user(user, user.set_reset_password_token).deliver_now
      flash[:success] = t "admin.users.resend_reset_password_mail_successfully"
    else
      flash[:danger] = t "admin.users.resend_reset_password_mail_failed"
    end
    user
  end

  def view_owner_infos
    return {status: false} unless current_admin.can_view_user_details

    if current_admin.account.valid_password?(params[:password])
      user = User.find_by(id: params[:user_id])
      AdminAccessUserHistory.create(access_type: :view, target_type: :owner,
        user_id: params[:user_id], admin_id: current_admin.id,
        accessed_at: ServerTime.now)
      {
        status: true,
        email: user&.account&.email
      }
    else
      {status: false}
    end
  end

  def send_remind_login_mail
    user_id = params[:user_id]
    if user_id.blank?
      flash[:error] = t("activerecord.errors.messages.could_not_send_mail")
      return
    end
    SendMailRemindLoginWorker.perform_async([user_id], "user")
    flash[:success] = t("admin.staff.sent_mail_successfully")
  end

  private
  def user_params
    target_role = params.require(:user).fetch(:role_id)
    permit_params = target_role == "owner" ? User::OWNER_ATTRIBUTES : User::USER_ATTRIBUTES
    params.require(:user).permit(permit_params)
  end

  def account_params
    params.require(:user).require(:account).permit(Account::ACCOUNT_ATTRIBUTES)
      .merge(skip_password_validation: true, type: :user, execute_account_name_validation: true,
        is_email_present: true)
  end

  def response_action account, user, status
    errors = account.errors.messages.merge(user.errors.messages)

    errors[:email] = [I18n.t("admin.errors.login_id_exist")] if errors[:email].blank? && user.errors.messages[:account_id].present?

    {
      status: status,
      errors: errors.to_json,
      redirect_path: admin_users_path
    }
  end

  def add_user_corporation_group_belong_group user
    return false if user.owner? || user_groups_changed

    user_groups = user.user_groups.pluck(:user_id, :corporation_group_id)
    user_corporation_groups = user.user_corporation_groups.pluck(:user_id, :corporation_group_id)
    user_corporation_groups_params = user_groups - user_corporation_groups
    UserCorporationGroup.import([:user_id, :corporation_group_id], user_corporation_groups_params)
  end

  def user_groups_changed
    return unless params[:user][:user_groups_attributes]

    user_groups = params[:user][:user_groups_attributes].as_json
      .select{|_, value| value.keys.length >= ASSOCIATION_MIN_PARAMS}
    user_groups.all?{|_, value| value["id"].present?}
  end

  def find_user
    User.find(params[:id])
  end
end
