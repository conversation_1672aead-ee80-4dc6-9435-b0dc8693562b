class Admin::BillingPaymentTemplatesService < Admin::AdminService
  def index
    return unless request_format == :json

    templates = filter_templates
    {
      templates: templates.includes(:location, admin: :account)
        .as_json(
          only: BillingPaymentTemplate::TEMPLATE_ATTRS,
          methods: BillingPaymentTemplate::TEMPLATE_METHODS
        ),
      page: templates.current_page,
      total_items: templates.total_count
    }
  end

  def show
    template = BillingPaymentTemplate.find_by(id: params[:id])
    return {} if template.blank?

    data = load_data(template)
    {
      template: template,
      template_json: template.as_json(only: BillingPaymentTemplate::TEMPLATE_ATTRS,
        methods: BillingPaymentTemplate::TEMPLATE_METHODS),
      corporations: data[:corporations],
      locations: data[:locations]
    }
  end

  def new
    template = generate_new_template
    data = load_data(template)
    {
      template: template,
      corporations: data[:corporations],
      locations: data[:locations]
    }
  end

  def create
    template = BillingPaymentTemplate.new(template_params)
    template.save!
    flash[:success] = get_success_message_for(template)
    json_response(true, {}, admin_billing_payment_templates_path)
  rescue ActiveRecord::RecordInvalid
    json_response(false, template.errors.messages)
  end

  def update
    template = BillingPaymentTemplate.find(params[:id])
    # * Filter selected parameters temporarily
    filtered_params = template_params
      .select{|key, _val| BillingPaymentTemplate::UPDATE_TEMPLATE_ATTRS.include?(key.to_sym)}
    template.update!(filtered_params)
    flash[:success] = get_success_message_for(template)
    json_response(true, {}, admin_billing_payment_template_path(template))
  rescue ActiveRecord::RecordInvalid
    json_response(false, template.errors.messages)
  end

  def destroy
    template = BillingPaymentTemplate.find_by(id: params[:id])
    return json_response(true, {}, admin_billing_payment_templates_path) if template.blank?

    template.destroy
    flash[:success] = get_success_message_for(template)
    json_response(true, {}, admin_billing_payment_templates_path)
  rescue StandardError
    flash[:danger] = I18n.t("admin.billing_payment_template.cannot_delete")
    json_response(false, {}, admin_billing_payment_templates_path)
  end

  def load_location_templates
    return {templates: []} if params[:location_id].blank?

    templates = BillingPaymentTemplate.by_location_id(params[:location_id])
    {templates: templates.as_json(only: BillingPaymentTemplate::TEMPLATE_ATTRS)}
  end

  private

  def generate_new_template
    return generate_template_from_template params[:billing_payment_template_id] if
      params[:billing_payment_template_id].present?
    return generate_template_from_arrangement params[:arrangement_id] if
      params[:arrangement_id].present?

    BillingPaymentTemplate.new
  end

  def generate_template_from_template template_id
    original_template = BillingPaymentTemplate.find_by(id: template_id)
    return BillingPaymentTemplate.new if original_template.blank?

    template = original_template.dup
    template.name = nil
    template
  end

  def generate_template_from_arrangement arrangement_id
    arrangement = Arrangement.find_by(id: arrangement_id)
    return BillingPaymentTemplate.new if arrangement.blank?

    billing_field_2 = nil
    billing_field_2 = 0 if arrangement.billing_field_2.present? && arrangement.billing_field_2.zero?
    attrs = {
      location_id: arrangement.location_id,
      billing_basic_unit_price: arrangement.billing_basic_unit_price,
      billing_night_unit_price: arrangement.billing_night_unit_price,
      area_allowance: arrangement.billing_field_1,
      short_allowance: billing_field_2,
      absence_discount: arrangement.billing_field_4,
      tax_exemption: arrangement.billing_tax_exemption,
      payment_basic_unit_price: arrangement.payment_basic_unit_price,
      payment_night_unit_price: arrangement.payment_night_unit_price,
      transportation_fee: arrangement.payment_field_1.to_i
    }
    BillingPaymentTemplate.new(attrs)
  end

  def load_data template = nil
    location = template&.location || Location.first
    locations = [[location.name, location.id]]
    corporation = location.corporation_group.corporation
    corporations = [[corporation.full_name, corporation.id]]
    {
      locations: locations,
      corporations: corporations
    }
  end

  def filter_templates
    page = search_params[:page] || Settings.page.default
    per_page = search_params[:per_page] || Settings.per_page.default
    BillingPaymentTemplate.by_name(search_params[:name])
      .by_location_id(search_params[:location_id])
      .by_corporation_id(search_params[:corporation_id])
      .page(page).per(per_page)
  end

  def template_params
    params.require(:billing_payment_template)
      .permit(BillingPaymentTemplate::CREATE_TEMPLATE_ATTRS)
      .merge(creator_id: current_admin.id)
  end

  def search_params
    params.permit(:name, :location_id, :corporation_id, :page, :per)
  end

  def json_response status, errors = {}, redirect_path = nil
    {
      status: status,
      errors: errors,
      redirect_path: redirect_path
    }.to_json
  end
end
