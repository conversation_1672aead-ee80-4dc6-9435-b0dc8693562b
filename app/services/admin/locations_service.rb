class Admin::LocationsService < Admin::AdminService
  include Admin::LocationHelper
  include LoadDataForSelect2

  def index
    case request_format
    when :html
      load_data_for_select2(:admin_location_search_conditions, :corporation_group_id, CorporationGroup)
    when :json
      locations = LocationSearch.new(params).search
      {
        locations: locations.as_json(
          only: [:id, :name, :code, :city, :postal_code],
          methods: [:corporation_group_violation_day_str, :organization_full_name,
            :prefecture_name, :address, :location_survey_id]
        ),
        page: locations.current_page,
        total_items: locations.total_count
      }
    end
  end

  def new
    data = load_data
    data[:location] = Location.new
    data
  end

  def edit
    load_data(load_location)
  end

  def create
    location = Location.new location_params.merge(creator_params)
    status = location.save
    if status
      location.assign_attributes(formated_crop_params)
      location.update(thumbnail_params)
      flash[:success] = get_success_message_for(location)
    end
    {
      status: status,
      errors: location.errors.to_json,
      nested_errors: {
        location_pics: location.location_pics.map(&:errors),
        priority_staffs: location.priority_staffs.map(&:errors)
      }.to_json,
      redirect_path: admin_locations_path
    }
  end

  def update
    location = load_location
    location.disable_create_survey = location_params[:disable_create_survey]
    location.assign_attributes(formated_crop_params)
    payment_rate = load_payment_rate location.id
    status = false
    ActiveRecord::Base.transaction do
      location.update! location_params.merge(updater_params)
      update_payment_rate!(location.id, payment_rate) if save_payment_rate?(payment_rate)
      status = true
    end
    if status
      location.remove_thumbnail
      flash[:success] = get_success_message_for(location)
    end
    json_response status, location, payment_rate
  rescue ActiveRecord::RecordInvalid
    json_response false, location, payment_rate
  end

  def destroy
    location = load_location
    status = location.destroy
    if status
      flash[:success] = get_success_message_for(location)
    else
      flash[:danger] = t "admin.location.delete.has_order_error"
    end
    {
      status: status,
      errors: location.errors.to_json,
      redirect_path: admin_locations_path
    }
  end

  def location_tab
    group_id = params[:corporation_group_id].presence
    target = group_id ? CorporationGroup.find(group_id) : Corporation.find(params[:corporation_id])
    locations = target.locations.includes(:corporation_group, :prefecture).order(name: :asc, id: :asc)
      .page(params[:page]).per Settings.per_page.corporation_group_tab
    {
      pagination_limit: Settings.per_page.corporation_group_tab,
      page: locations.current_page,
      locations: locations.as_json(
        only: [:id, :name, :code, :tel, :fax], methods: location_method
      ),
      total_items: locations.total_count
    }
  end

  def find_station
    if search_content = params[:search_content].presence
      stations = Station.includes(:railway_line).with_name_like(search_content)
        .limit Location::LIMIT_STATIONS
    elsif selected_station_id = params[:selected_station_id].presence
      selected_station = Station.includes(:railway_line).find_by id: selected_station_id
      stations = get_records_around selected_station
    else
      stations = Station.includes(:railway_line).limit Location::LIMIT_STATIONS
    end
    blank_option = {full_name: I18n.t("common.please_select"), id: ""}
    stations_json = stations.as_json(only: [:name, :id], methods: :full_name)
      .unshift blank_option
    {results: stations_json}
  end

  def load_options_for_select
    if params[:location_name].present?
      locations = Location.by_name(params[:location_name]).limit(Settings.select2.limit_option)
    else
      locations = Location.limit(Settings.select2.limit_option)
    end
    {locations: locations.as_json(only: [:id, :name])}
  end

  def export
    locations = LocationSearch.new(params[:search].merge(is_not_paginate: true)).search
    return {jid: nil, status: false} if locations.blank?

    location_ids = locations.pluck(:id)
    job_id = ExportStatusLocationSurveyWorker.perform_async(location_ids)
    {jid: job_id}
  end

  def locations_by_corporation
    corporation = Corporation.find_by(id: params[:corporation_id])
    locations = corporation&.locations
    {locations: locations&.as_json(only: [:name, :id]) || []}
  end

  def import_branches
    ImportLocationBranchesService.import_csv(params[:file].tempfile)
  end

  private

  def formated_crop_params
    {
      thumbnail_parameters: crop_thumbnail_params,
      thumbnail_background_parameters: crop_thumbnail_background_params
    }
  end

  def location_params
    params.require(:location).permit(Location::LOCATION_ATTRIBUTE)
  end

  def crop_thumbnail_params
    params.require(:thumbnail).permit(Location::IMAGE_ATTRIBUTES)
  end

  def crop_thumbnail_background_params
    params.require(:thumbnail_background).permit(Location::IMAGE_ATTRIBUTES)
  end

  def thumbnail_params
    params.require(:location).permit([:thumbnail, :thumbnail_background])
  end

  def location_method
    params[:corporation_group_id].present? ? :full_address : :corporation_group_full_name
  end

  def load_data location = nil
    corporation = location ? location.corporation_group.corporation : Corporation.first
    corporations = [[corporation.full_name, corporation.id]]
    corporation_id = location&.corporation_group&.corporation&.id || corporations.first[1]
    corporation_groups = CorporationGroup.options_for_select_with_corporation corporation_id
    corporation_group_id = location&.corporation_group_id || corporation_groups.first[1]
    organizations = Organization.options_for_select corporation_group_id
    departments = Department.pluck(:name, :id)
    job_categories = JobCategory.pluck(:name, :id)
    location_pic_types = LocationPicType.pluck(:name, :id)
    location_survey = location&.new_location_survey
    user_location = location ? User.by_role_can_survey(location.id).pluck(:name, :id) : []
    priority_staffs = get_priority_staff_options(location)
    now_base_price = location ? LocationPaymentRate.by_location(location.id)
      .current_price.order(start_date: :desc).first : {}
    new_base_price = location ? LocationPaymentRate.by_location(location.id)
      .new_price.order(start_date: :desc).first : {}
    location_job_categories = location ? location.location_job_categories : []
    alow_create_category = location ? LocationJobCategory.allow_select_categories(location).present? : false
    stocon_valid_dates = get_stocon_valid_dates(location)
    {
      location: location,
      corporations: corporations,
      corporation_groups: corporation_groups,
      organizations: organizations,
      departments: departments,
      job_categories: job_categories,
      location_pic_types: location_pic_types,
      location_survey: location_survey,
      user_location: user_location,
      priority_staffs: priority_staffs,
      now_base_price: now_base_price,
      new_base_price: new_base_price,
      location_job_categories: location_job_categories,
      alow_create_category: alow_create_category,
      stocon_valid_dates: stocon_valid_dates
    }
  end

  def load_location
    Location.find(params[:id])
  end

  def load_payment_rate location_id
    pr_params = payment_rate_params
    rate_id = pr_params[:id]
    is_empty = LocationPaymentRate::UPDATEABLE_ATTRS.all?{|attribute| pr_params[attribute].blank?}
    return if rate_id.blank? && is_empty
    return LocationPaymentRate.new(payment_rate_params.merge(location_id: location_id)) if rate_id.blank?

    payment_rate = LocationPaymentRate.find_by(id: rate_id)
    payment_rate.assign_attributes(payment_rate_params)
    payment_rate
  end

  def payment_rate_params
    params.require(:location).require(:new_base_price)
      .permit(LocationPaymentRate::UPDATEABLE_ATTRS + [:id])
  end

  def save_payment_rate? payment_rate
    return false if payment_rate.blank?
    return payment_rate.changed? unless payment_rate.new_record?

    LocationPaymentRate::UPDATEABLE_ATTRS.any?{|attribute| payment_rate_params[attribute].present?}
  end

  def json_response status, location, payment_rate
    {
      status: status,
      errors: location.errors.to_json,
      nested_errors: {
        location_pics: location.location_pics.map(&:errors),
        priority_staffs: location.priority_staffs.map(&:errors)
      }.to_json,
      nested_errors_object: {
        location_survey: location.location_survey&.errors
      }.to_json,
      payment_rate_errors: payment_rate&.errors.to_json,
      redirect_path: admin_locations_path
    }
  end

  def update_payment_rate! location_id, future_rate
    future_rate.save!
    last_rate = LocationPaymentRate.by_location(location_id).current_price
      .order(start_date: :desc).first
    last_rate&.update!(end_date: future_rate.start_date&.yesterday)
  end

  def get_priority_staff_options location
    return [] if location.blank?

    staff_ids = location.priority_staffs.pluck(:staff_id)
    Staff.includes(:account).where(id: staff_ids).type_staff
      .map{|s| [[s.staff_number, s.account_name].join(" "), s.id]}
  end

  def get_stocon_valid_dates location
    return [] if location.blank?

    account_ids = location.users.pluck(:account_id)
    return [] if account_ids.blank?

    Account.where(id: account_ids, location_code: location.code)
      .pluck(:valid_start_date, :valid_end_date)
  end
end
