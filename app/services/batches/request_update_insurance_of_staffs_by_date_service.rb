class Batches::RequestUpdateInsuranceOfStaffsByDateService
  class << self
    def perform
      prev_day = ServerTime.now.to_date.prev_day
      can_send_mail = EmploymentInsuranceLog.exists?(by_date: prev_day) ||
        SubsectionInsuranceLog.exists?(by_date: prev_day)
      return unless can_send_mail

      admins = Admin.includes(:account).is_insurance_mail_receiver
      admins.each do |admin|
        AdminMailer.notify_join_and_reject_insurances_by_date(admin.name, admin.email, prev_day).deliver_now
      end
    end
  end
end
