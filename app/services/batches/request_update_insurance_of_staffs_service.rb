class Batches::RequestUpdateInsuranceOfStaffsService
  class << self
    def perform
      staffs = Staff.confirmed_staff.includes(:staff_salary, :recent_departments)
        .select(:id, :social_attribute)
        .where("created_at <= ? AND registration_type IN (?)",
          calculation_start_date - 2.months,
          [Staff.registration_types[:haken], Staff.registration_types[:single_haken]])
        .reject{|staff| in_department_training?(staff.current_department_id)}

      social_attrs = Staff.social_attributes
      staffs_approved = Staff.confirmed_staff.haken.includes(:recent_departments)
        .select(:id, :social_attribute).staff_approved_in_range(calculation_start_date.prev_month.beginning_of_day,
          calculation_start_date.prev_day.end_of_day).where("social_attribute != ? AND social_attribute != ?
            AND social_attribute != ?", social_attrs[:day_student], social_attrs[:wh],
            social_attrs[:international_student])
        .reject{|staff| in_department_training?(staff.current_department_id)}

      staffs_approved = reject_staff_no_exists_actual_wk_time(staffs_approved)
      staffs_join_subsection = []
      staffs_reject_subsection = []
      staffs_join_employment = staffs_approved.to_a
      staffs_reject_employment = []

      staff_ids = staffs.pluck(:id)
      wk_range_1_data = working_time_data_query(staff_ids, start_date_range_1, end_date_range_1)
      wk_range_2_data = working_time_data_query(staff_ids, start_date_range_2, end_date_range_2)

      staffs.each do |staff|
        staff_salary = staff.staff_salary
        staff_data_range_1 = staff_working_data(staff.id, wk_range_1_data)
        staff_data_range_2 = staff_working_data(staff.id, wk_range_2_data)

        wk_range_1 = staff_data_range_1.average_working_time_per_week(start_date_range_1, end_date_range_1)
        payroll_range_1 = staff_data_range_1.payroll_amount

        wk_range_2 = staff_data_range_2.average_working_time_per_week(start_date_range_2, end_date_range_2)
        payroll_range_2 = staff_data_range_2.payroll_amount

        working_data = {
          avg_time_range_1: wk_range_1,
          payroll_range_1: payroll_range_1,
          avg_time_range_2: wk_range_2,
          payroll_range_2: payroll_range_2
        }

        subsection_status = eligible_to_join_and_reject_subsection_insurance(staff.social_attribute, working_data)
        employment_status = eligible_to_join_and_reject_employment_insurance(staff.social_attribute, working_data)

        staffs_join_subsection << staff if staff_salary&.not_joining? && subsection_status == :join
        staffs_reject_subsection << staff if staff_salary&.subscription? && subsection_status == :reject

        staffs_join_employment << staff if staff_salary&.e_i_not_target? && employment_status == :join
        staffs_reject_employment << staff if staff_salary&.e_i_target? && employment_status == :reject
      end
      export(staffs_join_subsection, staffs_reject_subsection, staffs_join_employment, staffs_reject_employment)
    end

    private
    def current_time
      ServerTime.now
    end

    def current_date
      current_time.to_date
    end

    def calculation_start_date
      current_time.change(day: 17).to_date
    end

    def start_date_range_1
      (current_time - 2.months).beginning_of_month + 15.days
    end

    def end_date_range_1
      current_time.prev_month.beginning_of_month + 14.days
    end

    def start_date_range_2
      end_date_range_1.next_day
    end

    def end_date_range_2
      current_time.beginning_of_month + 14.days
    end

    def beginning_date_next_month
      current_time.next_month.beginning_of_month
    end

    def age_from_export_date birthday
      ((current_time.next_month.beginning_of_month - birthday.beginning_of_day) / 1.year).floor
    end

    def mid_month_date
      current_time.beginning_of_month + 14.days
    end

    def payroll_start_date
      (current_time - 3.months).to_date.beginning_of_month + 15.days
    end

    def payroll_end_date
      (current_time.beginning_of_month + 14.days)
    end

    def status bool
      bool == true ? 1 : 0
    end

    def export staffs_join_subsection, staffs_reject_subsection, staffs_join_employment, staffs_reject_employment
      staffs_join_subsection = staffs_join_subsection.pluck(:id)
      staffs_reject_subsection = staffs_reject_subsection.pluck(:id)
      staffs_join_employment = staffs_join_employment.pluck(:id)
      staffs_reject_employment = staffs_reject_employment.pluck(:id)
      export_csv_join_subsection(staff_ids: staffs_join_subsection,
        file_name: "join_subsection_insurance_export_#{current_time.to_i}.csv")
      export_csv_reject_subsection(staff_ids: staffs_reject_subsection,
        file_name: "reject_subsection_insurance_export_#{current_time.to_i}.csv")
      export_csv_join_employment(staff_ids: staffs_join_employment,
        file_name: "join_employment_insurance_export_#{current_time.to_i}.csv")
      export_csv_reject_employment(staff_ids: staffs_reject_employment,
        file_name: "reject_employment_insurance_export_#{current_time.to_i}.csv")
      send_mail_to_admins
    end

    def export_csv_join_subsection staff_ids:, file_name:
      csv_header = Settings.insurance.export_csv.subsection.label
      arrange_payments = ArrangePayment.payment_calculate(payroll_start_date, payroll_end_date,
        staff_ids)
      export_csv_template(staff_ids, file_name, csv_header, :subsection, :join_insurance) do |staff, csv|
        join_digital = 1
        age_digital = status age_from_export_date(staff.birthday) >= 40
        reject_date = ""
        join_date = beginning_date_next_month.strftime(Settings.date.formats)
        cause_disqualification = ""
        arrange_payment = arrange_payments.find{|a| a.staff_id == staff.id}
        csv << [staff.account_name, staff.staff_number, join_digital, join_digital, age_digital,
                join_date, reject_date, cause_disqualification, health_insurance_reward(join_digital, arrange_payment),
                join_date, reject_date, cause_disqualification]
      end
    end

    def export_csv_reject_subsection staff_ids:, file_name:
      csv_header = Settings.insurance.export_csv.subsection.label
      export_csv_template(staff_ids, file_name, csv_header, :subsection, :reject_insurance) do |staff, csv|
        join_digital = 0
        age_digital = status age_from_export_date(staff.birthday) >= 40
        reject_date = mid_month_date.strftime(Settings.date.formats)
        join_date = ""
        cause_disqualification = 0
        csv << [staff.account_name, staff.staff_number, join_digital, join_digital, age_digital,
                join_date, reject_date, cause_disqualification, "",
                join_date, reject_date, cause_disqualification]
      end
    end

    def export_csv_join_employment staff_ids:, file_name:
      csv_header = Settings.insurance.export_csv.employment.label
      export_csv_template(staff_ids, file_name, csv_header, :employment, :join_insurance) do |staff, csv|
        reject_digital = 0
        join_digital = 1
        cause_disqualification = ""
        acquisition_date = mid_month_date.next_day.strftime(Settings.date.formats)
        employment_insurance_number = staff.staff_salary&.employment_insurance_number
        disqualification_date = ""
        csv << [staff.account_name, staff.staff_number, reject_digital, join_digital,
                employment_insurance_number, acquisition_date, disqualification_date, cause_disqualification]
      end
    end

    def export_csv_reject_employment staff_ids:, file_name:
      csv_header = Settings.insurance.export_csv.employment.label
      export_csv_template(staff_ids, file_name, csv_header, :employment, :reject_insurance) do |staff, csv|
        reject_digital = 1
        join_digital = ""
        cause_disqualification = 2
        acquisition_date = ""
        employment_insurance_number = staff.staff_salary&.employment_insurance_number
        disqualification_date = mid_month_date.strftime(Settings.date.formats)
        csv << [staff.account_name, staff.staff_number, reject_digital, join_digital,
                employment_insurance_number, acquisition_date, disqualification_date, cause_disqualification]
      end
    end

    def export_csv_template staff_ids, file_name, csv_header, insurance_type, file_type
      staffs = Staff.where(id: staff_ids).includes(:account, :staff_salary)
      file_path = Rails.root.join("tmp", file_name)
      File.open(file_path, "w:UTF-8", invalid: :replace, undef: :replace, replace: "?") do |file|
        file_csv = CSV.generate do |csv|
          csv << csv_header
          staffs.each do |staff|
            yield(staff, csv)
          end
        end
        file.write(file_csv)
      end
      InsuranceLog.create(file_name: file_name, batch_start_date: current_date,
        insurance_type: insurance_type, file_type: file_type)
      return unless Settings.environment_can_use_aws.include? Rails.env

      s3_folder = Settings.aws.s3.folders.admin_insurance_exports
      S3_BUCKET.object("#{s3_folder}/#{file_name}").upload_file(file_path)
    end

    def send_mail_to_admins
      admins = Admin.includes(:account).is_insurance_mail_receiver
      admins.each do |admin|
        AdminMailer.notify_join_and_reject_insurances(admin.name, admin.email).deliver_now
      end
    end

    def health_insurance_reward join_digital, arrange_payment
      return unless !join_digital.zero? && arrange_payment

      average = (arrange_payment.total_payment_total_amount.to_i / 3).floor
      reward_dest = 0
      rewards.each_with_index do |_, index|
        reward_value = rewards[index]["reward"]
        range_salary_1 = rewards[index]["range_salary_1"].to_i
        range_salary_2 = rewards[index]["range_salary_2"].to_i
        range = [range_salary_1, range_salary_2]
        next unless average.between?(*range)

        reward_dest = reward_value
        break
      end
      reward_dest
    end

    def rewards
      insurance_salaries_path = Rails.root.join("db", "insurance", "range_salaries.csv")
      CSV.parse(File.read(insurance_salaries_path), headers: true)
    end

    def in_department_training? current_department_id
      Settings.department_training_center_ids.include?(current_department_id)
    end

    def exists_acutal_wk_time? staff, started_at, ended_at
      wa = staff.work_achievements.sum_actual_working_time(started_at, ended_at.end_of_day, :payroll)
      total_minutes = wa.try(:[], 0).try(:actual_time_count).to_i
      return false if total_minutes.zero?

      true
    end

    def working_time_data_query staff_ids, working_started_at, working_ended_et
      params = {
        staff_ids: staff_ids,
        start_time: working_started_at.beginning_of_day,
        end_time: working_ended_et.end_of_day,
        segment_calc: :payroll,
        group_by_staff: true
      }

      WorkAchievements::WorkingTimeQuery.execute(
        :sum_actual_working_time,
        params
      )
    end

    def reject_staff_no_exists_actual_wk_time staffs_approved
      return staffs_approved if staffs_approved.blank?

      staff_ids = staffs_approved.pluck(:id)

      wk_time_data = working_time_data_query(staff_ids, calculation_start_date.prev_month.beginning_of_day,
        calculation_start_date.prev_day)

      staff_exists_actual_wk_time = wk_time_data
        .reject{|wk_time| wk_time.actual_time_count.to_i.zero?}
        .map(&:staff_id)

      staffs_approved.select{|staff| staff_exists_actual_wk_time.include?(staff.id)}
    end

    def staff_working_data staff_id, working_data
      staff_working_data = working_data.find{|wk_data| wk_data.staff_id == staff_id}
      staff_working_data.extend(Insurances::CalculateWorkingData)
    end

    def eligible_to_join_and_reject_subsection_insurance staff_social_attribute, working_data
      cmd = Insurances::EligibleJoinSubsectionInsuranceCommand.new(staff_social_attribute)
      status_range_1 = cmd.perform(working_data[:avg_time_range_1], working_data[:payroll_range_1])
      status_range_2 = cmd.perform(working_data[:avg_time_range_2], working_data[:payroll_range_2])

      return :join if status_range_1 && status_range_2
      return :reject if !status_range_1 && !status_range_2

      :do_nothing
    end

    def eligible_to_join_and_reject_employment_insurance staff_social_attribute, working_data
      cmd = Insurances::EligibleJoinEmploymentInsuranceCommand.new(staff_social_attribute)
      status_range_1 = cmd.perform(working_data[:avg_time_range_1])
      status_range_2 = cmd.perform(working_data[:avg_time_range_2])

      return :join if status_range_1 && status_range_2
      return :reject if !status_range_1 && !status_range_2

      :do_nothing
    end
  end
end
