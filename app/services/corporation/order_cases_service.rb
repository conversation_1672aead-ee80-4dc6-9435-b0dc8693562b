class Corporation::OrderCasesService < Corporation::CorporationService
  def index
    locations = current_user.picked_locations
    case request.format
    when "html"
      {
        locations: locations,
        status_ids: OrderCase.get_status_id_options(OrderCase.status_ids, :status_by_portion),
        working_status_id: OrderCase.get_status_id_options(OrderCase.working_status_ids, :arragement_status),
        last_condition: current_user.user_order_case_search_conditions.order_cases.last&.conditions
      }
    when "json"
      parsed_params = JSON.parse(params[:search])
      search_params = OrderCases::FormatOwnerFilterParamsCommand.new(parsed_params, **user_default_params(locations))
        .execute

      scope = OrderCases::OwnerFilterQuery.new(**search_params).execute
      order_cases = scope.page(search_params[:page]).per(search_params[:per_page])
        .includes(OrderCase::INCLUDE_TABLES)
      {
        order_cases: order_cases.as_json(
          only: OrderCase::ORDER_CASE_BASIC_ATTRIBUTES,
          methods: [:started_at] + OrderCase::ORDER_CASE_METHODS
        ),
        not_avalable_cancel_ids: check_not_avalable_to_cancel(order_cases),
        metadata: {total_items: order_cases.total_count}
      }
    end
  end

  def show
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    arrangements = order_case.arrangements.is_arranged
    work_achievements = order_case.work_achievements
      .by_arrangement_id(arrangements.arrived.pluck(:id)).not_approved.arrangement_started
    location = order_case.order.location
    order_branch = order_case.order_branch
    order_portions = order_case.order_portions.valid_status
    other_order_cases = order_branch.order_cases - [order_case]
    order_case_price = order_case.calculate_price || []
    staff_evaluations = StaffEvaluation.by_arrangement_id(work_achievements.map(&:arrangement_id))
    order_branch_details = order_branch.as_json(only: OrderBranch::WORKING_TIME_FULL_ATTRS)
    job_page_view = order_case.job_page_view
    {
      order_case: order_case,
      arrangements: arrangements,
      work_achievements: work_achievements,
      location: location,
      order_branch: order_branch,
      order_portions: order_portions,
      other_order_cases: other_order_cases,
      order_case_price: order_case_price,
      staff_evaluations: staff_evaluations,
      order_branch_details: order_branch_details,
      job_page_view: job_page_view
    }
  end

  def cancel_order_case
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    message = I18n.t("corporation.order_case.order_case_list_page.update_failed")
    valid_order_case = false
    if order_case&.valid_cancel
      valid_order_case = true
      status = update_cancel_status_and_notify_to_staff(order_case)
      message = I18n.t("corporation.order_case.order_case_list_page.update_success") if status
    else
      flash[:danger] = message
    end
    {
      valid_order_case: valid_order_case,
      status: status,
      order_case: order_case.as_json(only: :id, methods: [:valid_cancel, :status_id_i18n]),
      message: message
    }
  end

  def cancel_order_cases
    order_cases = OrderCase.where(id: params[:order_case_ids])
    message = I18n.t("corporation.order_case.order_case_list_page.update_failed")
    status = check_valid_order_cases(order_cases) && cancel_order_cases_and_notify_to_staff(order_cases)
    if status
      message = I18n.t("corporation.order_case.order_case_list_page.update_success")
    else
      flash[:danger] = message
    end
    {
      status: status,
      message: message
    }
  end

  def cancel_order_case_detail
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    redirect_url = root.corporation_order_case_url(order_case)
    if order_case&.valid_cancel
      status = update_cancel_status_and_notify_to_staff order_case
      if status
        flash[:success] = I18n.t("corporation.order_case.order_case_list_page.success_message")
        redirect_url = root.corporation_order_cases_url
      else
        flash[:danger] = I18n.t("corporation.order_case.order_case_list_page.update_failed")
      end
    else
      flash[:danger] = I18n.t("corporation.order_case.order_case_list_page.update_failed")
    end
    {redirect_url: redirect_url}
  end

  def calculate_summary_price
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    new_summary = 0
    if params[:total_portion].to_i != 0
      order_case.total_portion = params[:total_portion]
      new_summary = order_case.calculate_price[:summary]
    end
    {new_summary: new_summary}
  end

  def change_order_portion_number
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    if ServerTime.now > order_case.case_started_at
      flash[:danger] = I18n.t("corporation.order_case.order_case_list_page.expired")
      return true
    end
    updated_total_portion = order_case.order.total_order_portions + params[:total_portion].to_i
    if updated_total_portion > Settings.order_case.max_portions
      flash[:danger] = I18n.t("corporation.order_case.order_case_list_page.over_limit_portion")
      return true
    end
    ActiveRecord::Base.transaction do
      create_order_portion_data(order_case)
      order_case.update_status_follow_order_portion
    end

    cancel_staff_apply_ocs(order_case)
    flash[:success] = get_success_message_for order_case, :update
    true
  rescue ActiveRecord::RecordInvalid
    flash[:danger] = I18n.t("corporation.order_case.order_case_list_page.update_failed")
    true
  end

  def delete_order_portion
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    order_portions = order_case.order_portions.valid_status
    order_portion = order_portions.find_by(id: params[:portion_id])
    status = false
    temporary_arranged_ids = order_case.arrangements.temporary_arrange.pluck(:staff_id)
    applied_ids = order_case.staff_apply_order_cases.not_rejected.pluck(:staff_id)
    staff_ids = (temporary_arranged_ids + applied_ids).uniq
    if order_portion&.can_cancel?
      ActiveRecord::Base.transaction do
        if order_portion.update(status_id: "cancel")
          order_case.update_status_follow_order_portion
          ArrangeLog.create_by_owner(ArrangeLog.action_types[:op_cancel_from_owner],
            order_portion.arrangement, order_case.id, current_user.id)
          if order_case.cancel?
            portion_ids = order_case.order_portions.cancel.pluck(:id)
            cancel_and_notify_staff(order_case, staff_ids, portion_ids)
          end
          status = true
        end
      end

      cancel_staff_apply_ocs(order_case)
    end
    flash[:success] = I18n.t("corporation.order_case.order_case_detail.delete_success") if status
    flash[:danger] = I18n.t("corporation.order_case.order_case_detail.delete_failure") unless status
    {}
  end

  def update_special_offer_fee
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    if order_case.order_portions.arranged.any?
      flash[:danger] = I18n.t("corporation.order_case.request_special.update_failure")
      return true
    end
    current_offer_fee = order_case.special_offer_fee.to_i
    if order_case.update(special_offer_params)
      update_special_offer(current_offer_fee, order_case)
      flash[:success] = I18n.t("corporation.order_case.request_special.update_success")
    else
      flash[:danger] = I18n.t("corporation.order_case.request_special.update_failure")
    end
    {}
  end

  def view_staff_info
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    staff = Staff.find params[:staff_id]
    arrangement = staff.arrangements.find_by order_case_id: order_case.id
    return unless arrangement

    render_options = {
      layout: "/layouts/print/base.html.erb",
      template: "/corporation/order_cases/view_staff_info.html.erb",
      assigns: {
        for_export_pdf: true,
        staff: staff,
        arrangement: arrangement,
        order_case: order_case
      }
    }
    filename_prefix = I18n.t("corporation.order_case.order_case_detail.notification_form")
    root.send_data ExportPdfService.new.create(render_options),
      filename: staff_contract_info_file_name(filename_prefix, staff),
      type: "application/pdf", disposition: "inline"
  end

  def staff_contract
    order_case = OrderCase.find params[:id]
    return unless order_case.order.able_update_by?(current_user.locations.ids)

    staff = Staff.find params[:staff_id]
    arrangement = staff.arrangements.find_by order_case_id: order_case.id
    return unless arrangement

    render_options = {
      layout: "/layouts/print/base.html.erb",
      template: "/corporation/order_cases/staff_contract.html.erb",
      assigns: {
        for_export_pdf: true,
        staff: staff,
        arrangement: arrangement,
        pic_department: arrangement.order.corporation_group.pic_department,
        order_case: order_case,
        location_survey: order_case.order&.location&.location_survey
      }
    }
    filename_prefix = I18n.t("corporation.order_case.order_case_detail.individual_contract")
    root.send_data ExportPdfService.new.create(render_options),
      filename: staff_contract_info_file_name(filename_prefix, staff),
      type: "application/pdf", disposition: "inline"
  end

  def update_changeable
    order_portion = OrderPortion.find(params[:portion_id])
    branch_options = generate_new_order_branch_options order_portion
    action_types = branch_options[:action_types]
    arrangement = order_portion.arrangement
    messages = [I18n.t("corporation.order_case.update_changeable.already_assigned")]
    return {status: false, messages: messages} if
      arrangement.staff_id.present? || order_portion.order_case.exist_staff_applying?

    status = true
    new_oc = nil
    messages = [I18n.t("corporation.order_case.update_changeable.success_message")]
    if action_types.present?
      arrangement_copy = arrangement.dup
      arrangement_copy.id = arrangement.id
      ActiveRecord::Base.transaction do
        service = CreateOrderBranchService.new(arrangement, nil, branch_options[:options])
        clone_data_order = service.create_branch_data(false)
        errors = clone_data_order[2]
        status = errors.empty?
        messages = errors unless status
        raise ActiveRecord::Rollback unless status

        new_oc = clone_data_order[0].reload.order_case
        action_types.each do |action_type|
          ArrangeLog.create_by_owner(action_type, clone_data_order[0], new_oc.id, current_user.id)
        end
      end
    end
    response = {status: true, messages: messages}
    if new_oc
      popup_content = generate_popup_message(branch_options, new_oc)
      response[:order_case] = {
        id: new_oc.id,
        redirect_url: root.corporation_order_cases_url,
        body: popup_content
      }
    end
    response
  end

  private

  def generate_popup_message branch_options, new_oc
    is_required_time_changed = branch_options[:is_required_time_changed]
    is_time_changable_changed = branch_options[:is_time_changable_changed]
    oc_changable = new_oc.is_time_changable
    return unless is_required_time_changed || is_time_changable_changed
    return I18n.t("corporation.order_case.move_order_case_popup.changeable_to_unchangeable") unless oc_changable

    return I18n.t("corporation.order_case.move_order_case_popup.unchangeable_to_changeable",
      time_range: new_oc.required_time) if is_time_changable_changed && oc_changable
    I18n.t("corporation.order_case.move_order_case_popup.required_time_changed", time_range: new_oc.required_time)
  end

  # TODO(Phuc): Create generic version
  def generate_new_order_branch_options order_portion
    order_branch = order_portion.order_branch
    order_branch.assign_attributes order_branch_params
    order_branch.valid?
    action_types = []
    changable_changed = order_branch.is_time_changable_changed?
    if changable_changed
      change_type = order_branch.is_time_changable? ? :owner_changed_to_changeable : :owner_changed_to_unchangeable
      action_types << ArrangeLog.action_types[change_type]
    end
    required_time_changed = order_branch.required_start_time_changed? || order_branch.required_end_time_changed?
    action_types << ArrangeLog.action_types[:owner_changed_required_time] if
      required_time_changed && order_branch.is_time_changable?
    data = {
      is_time_changable_changed: changable_changed,
      is_required_time_changed: required_time_changed,
      action_types: action_types
    }
    data[:options] = {}
    data[:options] = order_branch.as_json(only: [:is_time_changable, :required_start_time, :required_end_time])
      .symbolize_keys if data[:is_time_changable_changed] || data[:is_required_time_changed]
    data
  end

  def order_branch_params
    params.require(:order_branch).permit(:is_time_changable, :required_start_time, :required_end_time)
  end

  def special_offer_params
    params.require(:order_case).permit(:special_offer_note, :special_offer_fee)
      .merge(is_special_offer: true)
  end

  def user_default_params locations
    {
      user_location_ids: locations.pluck(:id),
      user_corporation_id: current_user.corporation_id
    }
  end

  def check_valid_order_cases order_cases
    ids = current_user.locations.ids
    order_cases.all? do |order_case|
      order_case.order.able_update_by?(ids) && order_case&.valid_cancel
    end
  end

  def cancel_order_cases_and_notify_to_staff order_cases
    canceled_order_potion_ids = OrderPortion.cancel.where(order_case_id: order_cases.ids).ids
    staff_ids_by_oc = get_staff_ids_for_notification(order_cases)
    status = OrderCase.update_cancel_status(order_cases)
    if status
      order_cases.includes(arrangements: [:order_portion]).each do |order_case|
        staff_ids = staff_ids_by_oc[order_case.id]
        cancel_and_notify_staff(order_case, staff_ids, canceled_order_potion_ids)
      end
    end
    status
  end

  def update_cancel_status_and_notify_to_staff order_case
    applied_ids = order_case.staff_apply_order_cases.not_rejected.pluck(:staff_id)
    temporary_arranged_ids = order_case.arrangements.temporary_arrange.pluck(:staff_id)
    staff_ids = (applied_ids + temporary_arranged_ids).uniq
    canceled_order_potion_ids = order_case.order_portions.cancel.ids
    status = order_case.update_cancel_status
    cancel_and_notify_staff(order_case, staff_ids, canceled_order_potion_ids) if status
    status
  end

  def cancel_and_notify_staff order_case, staff_ids, canceled_order_potion_ids
    return if order_case.is_registration_training_job?

    order_case.arrangements.includes(:order_portion).each do |arrangement|
      next if canceled_order_potion_ids.include?(arrangement.order_portion.id)

      ArrangeLog.create_by_owner(ArrangeLog.action_types[:oc_cancel_from_owner],
        arrangement, order_case.id, current_user.id)
    end
    sorted_order_portions = order_case.order_portions.order(case_started_at: :desc)
    arrangement = order_case.arrangements.first
    app_notification_options = []
    Staff.where(id: staff_ids).includes(:app_staff_notification_setting).each do |staff|
      options = {sorted_order_portions: sorted_order_portions}
      Notification::SentEmailToStaffService.new(staff, :cancel_order_case_for_staff, options).execute
      if staff.account&.tel.present? && staff.account&.email.blank?
        location_name = order_case.order.location.name
        case_started_at = order_case.case_started_at
        MessageSenderService.send_canceled_order_case_message(staff.account&.tel, location_name, case_started_at)
      end
      next unless arrangement

      app_notification_options << {
        staff_id: staff.id,
        order_case_id: order_case.id,
        arrangement_id: arrangement.id,
        creator_type: :user,
        creator_id: current_user.id,
        notification_type: :canceled_job
      }
    end
    app_notification_options.in_groups_of(Settings.app_notification.batch_size)
      .each do |notification_options|
      AppSendNotificationWorker.perform_async(notification_options.reject(&:blank?))
    end
  end

  def create_order_portion_data order_case
    order = order_case.order
    order_branch = order_case.order_branch
    params[:total_portion].to_i.times do
      portion = OrderPortion.new(order_id: order_case.order_id,
        order_branch_id: order_case.order_branch.id, order_case_id: order_case.id,
        case_started_at: order_case.case_started_at, case_ended_at: order_case.case_ended_at)
      portion.save!
      arrangement = Arrangement.new(order_id: order.id, order_branch_id: order_branch.id,
        order_case_id: portion.order_case_id, order_portion_id: portion.id,
        working_started_at: portion.case_started_at, working_ended_at: portion.case_ended_at,
        break_time: order_branch.break_time, order_segment_id: order.order_segment_id,
        is_penalty_target: false)
      Arrangement::BREAK_TIME_ATTRS.each{|attr| arrangement[attr] = order_branch[attr]}
      arrangement.save!
      work_achievement = WorkAchievement.new(arrangement_id: arrangement.id,
        working_time_status_id: WorkAchievement.working_time_status_ids[:not_inputted])
      arr_paym = ArrangePayment.new(arrangement_id: arrangement.id)
      arr_bill = ArrangeBilling.new(arrangement_id: arrangement.id)
      cal_service = Calculation::TriggerArrangementDataService.new(portion, work_achievement,
        arr_paym, arr_bill, not_trigger: true)
      cal_service.billing_payment_template = arrangement.billing_payment_template if
        arrangement.billing_payment_template_id.present?
      cal_service.execute
      work_achievement.save
      arr_paym.save
      arr_bill.save
    end
    order_branch.update_total_portion
    order.update_total_order_portions
  end

  def staff_contract_info_file_name prefix, staff
    filename = [prefix, staff.id, ServerTime.now.strftime(Settings.datetime.file_formats)].join "-"
    "#{filename}.pdf"
  end

  def update_special_offer current_offer_fee, order_case
    difference_offer_fee = special_offer_params[:special_offer_fee].to_i - current_offer_fee
    order_branch = order_case.order_branch
    order_case.update_column(:estimation, order_case.estimation.to_i + difference_offer_fee)
    order_branch.update_column(:estimation, order_branch.estimation.to_i + difference_offer_fee)
    order_case.update_billing_field_5
  end

  def check_not_avalable_to_cancel order_cases
    ids = current_user.locations.ids
    order_cases.map do |order_case|
      next if order_case.order.able_update_by?(ids) && order_case&.valid_cancel

      order_case.id
    end.compact
  end

  def get_staff_ids_for_notification order_cases
    order_cases.each_with_object({}) do |oc, staff_ids|
      applied_ids = oc.staff_apply_order_cases.not_rejected.pluck(:staff_id)
      temporary_arranged_ids = oc.arrangements.temporary_arrange.pluck(:staff_id)
      staff_ids[oc.id] = (applied_ids + temporary_arranged_ids).uniq
    end
  end

  def cancel_staff_apply_ocs order_case
    options = {
      action_type: "owner_cancelled",
      user_id: current_user.id
    }
    OrderCases::ArrangeFullPortionCommand.new([order_case], options).perform
  end
end
