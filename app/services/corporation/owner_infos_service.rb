class Corporation::OwnerInfosService < Corporation::CorporationService
  def index
    current_user.corporation
  end

  def corporation_groups
    corporation = current_user.corporation
    corporation_groups = corporation.corporation_groups.includes(:organizations)
      .search_by(params[:keyword])
      .extending(Locations::OwnerScopes)
      .order_in_owner_info(params[:sort_key].to_s, params[:desc].to_s.true?)
      .page(params[:page]).per(params[:per_page])
    {
      corporation_groups: corporation_groups
        .as_json(only: [:violation_day],
          methods: [
            :id_with_leading_zeros, :full_name, :formatted_violation_day, :organizations_name,
            :group_name, :corporation_group_tag_id, :id
          ]),
      metadata: {
        total_items: corporation_groups.total_count
      },
      corporation_group_tags: corporation.corporation_group_tags.as_json(only: [:id, :group_name])
    }
  end

  def update
    corporation = current_user.corporation
    corporation_group = CorporationGroup.find params[:id]
    return {status: false} unless
      current_user.owner? && corporation_group.in?(current_user.corporation.corporation_groups)

    group_tag_id = params[:owner_info][:corporation_group_tag_id]
    group_tags = corporation.corporation_group_tags
    ActiveRecord::Base.transaction do
      if params[:owner_info][:is_new_group_tag]
        group_tag = group_tags.create! group_name: params[:owner_info][:group_name]
        group_tag_id = group_tag.id
      end
      corporation_group.update_attribute(:corporation_group_tag_id, group_tag_id)
    end
    {
      status: true,
      corporation_group: corporation_group.as_json(only: :corporation_group_tag_id,
        methods: :group_name),
      corporation_group_tags: group_tags.as_json(only: [:id, :group_name])
    }
  rescue ActiveRecord::RecordInvalid
    {status: false}
  end

  def locations
    locations = current_user.corporation.locations.is_active.includes(:location_pics)
      .search_by(params[:keyword])
      .extending(Locations::OwnerScopes)
      .select_in_owner_info
      .order_in_owner_info(params[:sort_key].to_s, params[:desc].to_s.true?)
      .page(params[:page]).per(params[:per_page])
    {
      locations: locations
        .as_json(only: [:id, :name, :short_name, :haken_destination_name, :claim_name,
          :mandator_name],
          methods: [
            :id_with_leading_zeros
          ]),
      metadata: {
        total_items: locations.total_count
      }
    }
  end
end
