class Corporation::LocationSurveysService < Corporation::CorporationService
  PDF_TYPE = %w(location_survey_instruction)

  def index
    return unless current_user.is_role_can_survey_location?

    {
      locations_surveyed: get_locations_surveyed
    }
  end

  def new
    return if is_access_fail_with_location? ||
      !get_locations_not_survey.exists?(params[:location_id])

    location = Location.find(params[:location_id])
    {
      location_survey: location.new_location_survey,
      location: location
    }
  end

  def show
    location_survey = LocationSurvey.find(params[:id])
    location = current_user.locations.find_by(id: location_survey.location_id)
    return if location.nil? || !current_user.is_role_can_survey_location?

    {
      location_survey: location_survey,
      location: location
    }
  end

  def create
    location_id = params[:location_id]
    location_survey_params = location_survey_params.merge(location_id: location_id, user_id: current_user.id)
    location_survey = LocationSurvey.new(location_survey_params)
    return if location_survey.is_can_not_create?

    redirect_path = ""
    redirect_path = finish_corporation_location_surveys_path(location_id: location_id, locale: params[:locale]) if location_survey.save
    {status: redirect_path.present?, redirect_path: redirect_path}
  end

  def locations_not_survey
    return {status: false, locations_not_survey: []} if is_can_not_access_location_survey?

    {
      status: true,
      locations_not_survey: get_locations_not_survey.select(:id, :name)
    }
  end

  def finish
    return unless current_user.is_role_can_survey_location?

    {location_survey_next: get_locations_not_survey.first}
  end

  def instruction_pdf
    return if PDF_TYPE.exclude?(params[:file]) || !current_user.is_role_can_survey_location?

    root.send_file(
      Rails.root.join("public", "pdfs", "#{params[:file]}.pdf"),
      filename: I18n.t("corporation.location_survey.instruction_pdf_name"),
      disposition: "inline",
      type: "application/pdf"
    )
  end

  def load_salary_by_prefecture
    return unless current_user.is_role_can_survey_location?

    location = Location.find params[:location_id]
    salary_range = PrefectureSalaryRange.find_by(prefecture_id: location.prefecture_id)
    salary_range_i18n = I18n.t("corporation.location_survey.form_survey.question_group_3.range_salary",
      min_salary: salary_range&.min.to_i, max_salary: salary_range&.max.to_i)
    {status: true, salary_range: salary_range_i18n}
  end

  private
  def location_survey_params
    params.require(:location_survey).permit!
  end

  def is_access_fail_with_location?
    LocationSurvey.exists?(location_id: params[:location_id]) || is_can_not_access_location_survey?
  end

  def get_locations_surveyed
    current_user.locations.joins(:location_survey)
  end

  def get_locations_not_survey
    current_user.locations.where(closed_at: nil, is_show_survey: true)
      .where.not(id: get_locations_surveyed.select(:id))
  end

  def is_can_not_access_location_survey?
    is_full_location_surveys? || !current_user.is_role_can_survey_location? || current_user.locations.blank?
  end

  def is_full_location_surveys?
    location_ids = current_user.locations.where(closed_at: nil, is_show_survey: true).pluck(:id)
    total_locations = location_ids.uniq.count
    total_location_surveys = LocationSurvey.where(location_id: location_ids).count

    total_locations == total_location_surveys
  end
end
