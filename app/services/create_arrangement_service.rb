class CreateArrangementService
  WORK_ACHIEVEMENT_INIT_METHODS = %i(set_working_started_at set_working_ended_at set_rest_time_date)
  SECOND_IN_HOUR = 3600
  SECOND_PER_MINUTE = 60
  attr_accessor :location_job_category_id, :invoice_target

  def initialize params, admin
    @params = params
    @admin = admin
    @bill_paym = bill_paym_params
    @warning_messages = []
    @saved_order_cases = []
    @saved_arrangements = []
    @arrangement_errors = []
    @work_achievement_errors = []
    @saved_arrange_billings = []
  end

  def execute
    format_params_nested
    @order = Order.new build_order_params
    order.is_violation_day_unchanged = true
    order.is_location_survey_unchanged = true
    add_sequence_no_order_branch(order)
    order.set_pic_department_id
    order_branches = order.order_branches.reject(&:marked_for_destruction?)
    order_branches.each(&:calculate_relate_field_after_assign_attr)
    order.calculate_overall_attributes
    @min_wage_status = true
    if bill_paym_params.present?
      working_dates = order_branches.map(&:started_at).uniq.compact.reject(&:blank?)
      @min_wage_status = CheckMinimumWageService.new(
        working_dates, bill_paym_params[:payment_basic_unit_price], params[:prefecture_id]
      ).valid?
    end
    @status = order.valid? && @min_wage_status
    @status &&= order.location.valid_order_step_1_info? unless order.is_migrated?
    ActiveRecord::Base.transaction do
      @status = order.save if @status
      order_branches.each_with_index do |order_branch, idx|
        @order_branch = order_branch
        @arrangement_index = idx
        @submit_arrangement = params[:arrangement][idx.to_s]
        create_order_case
        create_order_portion
        create_arrange_data
        @arrangement.update_payment_ot1_time_in_working_day_of @arrangement.staff_id if @status
        update_staff_worked_history
      end
      check_status
    end
    create_result
  rescue ActiveRecord::RecordInvalid
    @status = false
    create_result
  end

  private
  attr_reader :params, :admin, :order, :submit_arrangement, :order_branch, :bill_paym,
    :order_case, :order_portion, :arrangement, :work_achievement, :warning_messages

  def format_params_nested
    type_id = params[:order][:type_id]
    params[:order][:order_branches_attributes].each do |idx, branch|
      branch[:_destroy] = true if branch[:order_branch_type].present? &&
        branch[:order_branch_type] != "#{type_id}_order"
      OrderBranch::REST_TIMES.each do |rest_time|
        if branch["rest#{rest_time}_started_at".to_sym].blank?
          branch["rest#{rest_time}_started_at".to_sym] = ""
          branch["rest#{rest_time}_started_at".to_sym] = ""
        end
      end
      branch[:staff_count] = 1
      branch[:create_by_arrangement] = true
      branch[:arrangement_staff_id] = params[:arrangement][idx][:staff_id]
    end
  end

  def build_order_params
    extend_params = {
      created_admin_id: admin.id,
      status_id: :confirmed
    }
    order_params.merge(extend_params)
  end

  def add_sequence_no_order_branch order
    sequence_no = order.order_branches&.with_deleted&.max_by(&:sequence_no)&.sequence_no
    if sequence_no.nil?
      sequence_no = 0
    else
      sequence_no += 1
    end
    order.order_branches.each do |ob|
      if ob.sequence_no.nil?
        ob.sequence_no = sequence_no
        sequence_no += 1
      end
    end
  end

  def order_params
    return params[:order] if params[:order] && params[:order][:is_migrated]

    params.require(:order).permit(Order::ORDER_ATTRS)
  end

  def bill_paym_params
    return {} unless params[:order] && params[:order][:bill_paym_temp]

    params[:order][:bill_paym_temp]
  end

  def create_order_case
    @order_case = OrderCase.new(order_case_params)
    @order_case.location_job_category_id = location_job_category_id
    order_case.invoice_target = invoice_target ||
      order&.location&.default_invoice_target
    order_case_status = @order_case.save
    @status &&= order_case_status
    return unless order_case_status

    @saved_order_cases << @order_case
    order_case.update_columns(segment_id: order.order_segment_id)
    OrderCases::UpdatePeakPeriodCommand.new(order_case).perform
  end

  def create_order_portion
    @order_portion = OrderPortion.new(order_portion_params)
    @status = @order_portion.save && @status
  end

  def create_arrange_data
    @arrangement = Arrangement.new(arrangement_params)
    if arrangement.save
      @arrangement_errors << []
      @saved_arrangements << arrangement
    else
      @arrangement_errors << [arrangement.errors.to_json]
      @status = false
    end

    @work_achievement = WorkAchievement.new(work_achievement_params)
    return if submit_arrangement[:staff_id].blank?

    if work_achievement.valid?
      @work_achievement_errors << []
      work_achievement.assign_break_time WorkAchievement::TYPES_WITH_UNDERSCORE[2]
      @status = work_achievement.save && @status
      arr_paym = ArrangePayment.new(arrangement_id: arrangement.id)
      arr_bill = ArrangeBilling.new(arrangement_id: arrangement.id)
      arr_paym.payment_basic_unit_price = bill_paym[:payment_basic_unit_price]
      arr_paym.payment_night_unit_price = bill_paym[:payment_night_unit_price]
      arr_paym.payment_field_1 = bill_paym[:transportation_fee]
      arr_bill.billing_basic_unit_price = bill_paym[:billing_basic_unit_price]
      arr_bill.billing_night_unit_price = bill_paym[:billing_night_unit_price]
      arr_bill.billing_field_1 = bill_paym[:area_allowance]
      arr_bill.billing_field_2 = bill_paym[:short_allowance]
      arr_bill.billing_field_4 = bill_paym[:absence_discount]
      arr_bill.billing_tax_exemption = bill_paym[:tax_exemption]
      update_billing_payment_logs arrangement.id
      trigger_service = Calculation::TriggerArrangementDataService.new(order_portion, work_achievement,
        arr_paym, arr_bill, create_arrangement: true)
      trigger_service.billing_payment_template = arrangement.billing_payment_template if
        arrangement.billing_payment_template_id.present?
      trigger_service.execute
      arr_paym.save
      @saved_arrange_billings << arr_bill if arr_bill.save
      staff_apply_oc = StaffApplyOrderCase.new(staff_apply_oc_params)
      staff_apply_oc.save
      arrange_to_staff arrangement.reload
    else
      @status = false
      @work_achievement_errors << [work_achievement.errors.to_json]
    end
  end

  def update_staff_worked_history
    current_date = ServerTime.now
    staff = @arrangement.staff
    history_work_time = Calculation::StaffHistoryWorkedTimeService.new(staff, current_date)
    history_work_time_end_date = history_work_time.last_month_end_date
    if @arrangement.staff_id.present? &&
      @arrangement.working_started_at < history_work_time_end_date
      staff.update_working_time_history(current_date)
    end
  end

  def update_billing_payment_logs arrangement_id
    return if bill_paym.blank?

    ArrangePaymentLog.create(
      arrangement_id: arrangement_id,
      admin_id: order.created_admin_id,
      payment_basic_unit_price: bill_paym[:payment_basic_unit_price],
      payment_night_unit_price: bill_paym[:payment_night_unit_price],
      payment_field_1: bill_paym[:transportation_fee]
    )
    ArrangeBillingLog.create(
      arrangement_id: arrangement_id,
      admin_id: order.created_admin_id,
      billing_basic_unit_price: bill_paym[:billing_basic_unit_price],
      billing_night_unit_price: bill_paym[:billing_night_unit_price],
      billing_field_1: bill_paym[:area_allowance],
      billing_field_2: bill_paym[:short_allowance],
      billing_field_4: bill_paym[:absence_discount],
      billing_tax_exemption: bill_paym[:tax_exemption]
    )
  end

  def order_case_params
    working_start_time = order_branch&.working_start_time&.strftime("%H:%M")
    working_end_time = order_branch&.working_end_time&.strftime("%H:%M")
    start_date = order_branch&.started_at&.strftime(Settings.date.formats)
    started_at = "#{start_date} #{working_start_time}"&.in_time_zone
    ended_at = "#{start_date} #{working_end_time}"&.in_time_zone
    ended_at += 1.day if ended_at && started_at && ended_at < started_at
    {
      order_id: order_branch.order_id,
      order_branch_id: order_branch.id,
      case_started_at: started_at,
      case_ended_at: ended_at,
      total_portion: 1,
      estimation: order_branch.estimation,
      immediate_fee: 0,
      is_special_offer: order_branch.is_special_offer,
      special_offer_fee: order_branch.special_offer_fee,
      special_offer_note: order_branch.special_offer_note,
      training_session_code: order.training_session_code,
      training_schedule_code: order.training_schedule_code
    }
  end

  def order_portion_params
    {
      order_id: order_case.order_id,
      order_branch_id: order_case.order_branch_id,
      order_case_id: order_case.id,
      case_started_at: order_case.case_started_at,
      case_ended_at: order_case.case_ended_at
    }
  end

  def arrangement_params
    arr_params = Arrangement::BREAK_TIME_ATTRS.map{|attr| [attr, order_branch[attr]]}.to_h
    {
      order_id: order.id,
      order_branch_id: order_branch.id,
      order_case_id: order_portion.order_case_id,
      order_portion_id: order_portion.id,
      working_started_at: order_portion.case_started_at,
      working_ended_at: order_portion.case_ended_at,
      order_segment_id: order.order_segment_id.to_i,
      is_penalty_target: false,
      is_prepared: true,
      is_arrived: true,
      arrange_comment: submit_arrangement[:arrange_note],
      staff_id: submit_arrangement[:staff_id],
      migrated_at: submit_arrangement[:migrated_at],
      staff_present: true,
      billing_payment_template_id: bill_paym[:id]
    }.merge(arr_params)
  end

  def work_achievement_params
    attrs = WorkAchievement::ARRANGE_UPDATE_ATTRS + %w(rest1_editable rest2_editable rest3_editable)
    w_params = attrs.map{|attr| [attr, submit_arrangement[attr]]}.to_h
    {
      arrangement_id: arrangement.id,
      working_time_status_id: WorkAchievement.working_time_status_ids[:op_center_approved],
      create_by_arrangement: true,
      approved_admin_id: admin.id,
      updater_type_id: WorkAchievement.updater_type_ids[:admin],
      updater_id: admin.id,
      order_branch_start_date: order_branch.started_at&.to_date
    }.merge(w_params)
  end

  def arrange_to_staff arrangement
    staff = Staff.find submit_arrangement[:staff_id]
    condition = ArrangeCondition.new(order_case, staff, arrangement.id, false, false)
    condition.addition_working_hours = maping_staff_working_hours[staff.id.to_s]
    arrange_condition = condition.valid_arrange?
    if valid_condition? arrange_condition
      ArrangementStaffService.new(arrangement, staff, admin).arrange_when_create
    else
      if arrangement.valid?
        message = arrange_condition[:arrange_condition_message].first
        if message.present?
          arrangement.errors.add(:staff_id, message)
          @arrangement_errors[@arrangement_errors.size - 1] = [arrangement.errors.to_json]
        else
          warning_message = arrange_condition[:arrange_warning_message].first
          @warning_messages << {warning_message: warning_message, index: @arrangement_index}
        end
      end
      @status = false
    end
  end

  def staff_apply_oc_params
    {
      staff_id: submit_arrangement[:staff_id],
      order_id: order.id,
      order_branch_id: order_case.order_branch_id,
      order_case_id: order_case.id,
      location_id: order_case.location_id,
      requested_transportation_fee: submit_arrangement[:transportation_fee].to_i,
      is_used_original_condition: true,
      status_id: StaffApplyOrderCase.status_ids[:not_processed]
    }
  end

  def check_status
    return if @status

    raise ActiveRecord::Rollback
  end

  def create_result
    result = {
      status: @status,
      warning_messages: @warning_messages,
      order: order.as_json(methods: Order::ORDER_DEFINE_METHODS),
      nested_errors: order.order_branches.map(&:errors),
      work_achievement_errors: @work_achievement_errors,
      arrangement_errors: @arrangement_errors,
      status_except_warning: @status_except_warning,
      under_minimum_wage: !@min_wage_status
    }
    order.is_migrated? ? result.merge(extra_migrate_result) : result
  end

  def extra_migrate_result
    {
      order_branch: order_branch.id,
      order_case: order_case.id,
      order_portion: order_portion.id,
      arrangement: arrangement.id,
      work_achievement: work_achievement.id
    }
  end

  def valid_condition? arrange_condition
    return true if order.is_migrated?

    arrange_condition[:valid_arrange] &&
      ((!arrange_condition[:warning_arrange] && !arrange_condition[:notify_arrange]) || params[:confirm_warning])
  end

  def maping_staff_working_hours
    formatted_params = params.as_json
    order_branchs_data = formatted_params["order"]["order_branches_attributes"]
    arrangements_data = formatted_params["arrangement"]
    data_arrange = {}
    arrangements_data.each do |key, arrangement_value|
      staff_id = arrangement_value["staff_id"]
      next if staff_id.blank?

      data_arrange[staff_id] = {} if data_arrange[staff_id].blank?
      old_data_time = data_arrange[staff_id]
      new_data_time = {}
      working_date = order_branchs_data[key]["started_at"]
      if old_data_time[working_date].present?
        old_data_time[working_date] += caculated_working_hours(arrangement_value)
      else
        new_data_time[working_date] = caculated_working_hours(arrangement_value)
      end
      data_arrange[staff_id] = old_data_time.merge(new_data_time)
    end
    data_arrange
  end

  def caculated_working_hours data_arrange
    working_time = from_format_to_second(data_arrange["working_ended_at"]) -
      from_format_to_second(data_arrange["working_started_at"])
    rest_1 = 0
    rest_2 = 0
    rest_3 = 0
    if data_arrange["rest1_ended_at"].present? && data_arrange["rest1_started_at"].present?
      rest_1 = from_format_to_second(data_arrange["rest1_ended_at"]) -
      from_format_to_second(data_arrange["rest1_started_at"])
      rest_1 = 0 if rest_1 < 0
    end
    if data_arrange["rest2_ended_at"].present? && data_arrange["rest2_started_at"].present?
      rest_2 = from_format_to_second(data_arrange["rest2_ended_at"]) -
      from_format_to_second(data_arrange["rest2_started_at"])
      rest_2 = 0 if rest_2 < 0
    end
    if data_arrange["rest3_ended_at"].present? && data_arrange["rest3_started_at"].present?
      rest_3 = from_format_to_second(data_arrange["rest3_ended_at"]) -
      from_format_to_second(data_arrange["rest3_started_at"])
      rest_3 = 0 if rest_3 < 0
    end
    ((working_time - rest_1 - rest_2 - rest_3).to_f / SECOND_IN_HOUR).round(1)
  end

  def from_format_to_second strtime
    data = strtime.split(":")
    number_sec = convert_to_second(data[0], "HH") + convert_to_second(data[1], "MM")
    number_sec += convert_to_second(data[2], "SS") if data[2].present?
    number_sec
  end

  def convert_to_second strtime, format_type
    strtime = strtime.to_i
    case format_type
    when "HH"
      strtime * SECOND_IN_HOUR
    when "MM"
      strtime * SECOND_PER_MINUTE
    else
      strtime
    end
  end
end
