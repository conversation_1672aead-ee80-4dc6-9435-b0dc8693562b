class Staff::TrainingSchedulesService < Staff::StaffService
  UNCHANGED_SCHEDULE_ID = -1

  def new
    {
      account: current_staff.account,
      require_email: !current_staff.is_email_verified?,
      direct_booking: current_staff.staff_recruitment_process&.not_in_progress?,
      training_centers: Location.training_center_locs_json_arr
    }
  end

  def create
    training_schedule_id = schedule_params[:single_session_id]
    condition   = TrainingScheduleCondition.new(training_schedule_id).valid_schedule?
    valid       = condition[:valid]
    error_msg   = condition[:error_messages][0]
    return JsonResponse.redirect_path(valid, error_msg, "") unless valid

    cmd = TrainingSchedules::StaffApplyTrainingScheduleCommand.new(
      training_schedule_id,
      current_staff.id
    )

    applicant = cmd.perform

    save_staff_action_log(
      log_data(
        "staff_applies_training_schedule",
        applicant.training_schedule_id,
        training_applicant_id: applicant.id
      )
    )

    notify_to_staff

    JsonResponse.redirect_path(valid, "", details_staff_training_schedules_path)
  rescue ActiveRecord::RecordInvalid
    JsonResponse.redirect_path(false, I18n.t("errors.messages.generic_error"), "")
  end

  def details
    applied_schedules = get_applied_schedules
    return if applied_schedules.blank?

    TrainingScheduleApplicantDecorator.applicant_details(applied_schedules)
  end

  def reschedule
    applied_schedules = get_applied_schedules
    return unless applied_schedules

    staff_recruitment_process = current_staff.staff_recruitment_process
    current_session = TrainingScheduleApplicantDecorator.get_current_session(applied_schedules)
    raise(CustomExceptions::CannotRescheduleTrainingError) if
      !current_session.training_schedule.able_to_absent? && !staff_recruitment_process.absent_from_training?

    data = {
      training_center_id: current_session.location_id,
      training_centers: Location.training_center_locs_json_arr,
      current_schedule_id: current_session.is_unavailable? ? nil : current_session.training_schedule_id
    }

    data = data.merge(TrainingScheduleApplicantDecorator.get_reschedule_data(applied_schedules))
      .merge(
        TrainingSchedule.available_schedules_by_location(
          current_session.location_id,
          current_session.training_schedule_id
        )
      )

    survey_question = SurveyQuestion.active.find_by(survey_question_type: :staff_reschedule_training)
    data[:surveys] = SurveyQuestionBlueprint.render_as_json(survey_question, view: :training_survey_for_staff) unless
      current_staff.staff_recruitment_process.absent_from_training?

    data
  end

  def reschedule_schedules
    diff, status, message = diff_reschedule
    return JsonResponse.redirect_path(true, "", details_staff_training_schedules_path) if diff.blank?
    return JsonResponse.redirect_path(status, message, "") unless status

    cmd = TrainingSchedules::RescheduleTrainingScheduleCommand.new(
      staff_id: current_staff.id,
      removed_ids: diff[:removed_ids],
      added_ids: diff[:added_ids],
      survey_answer_id: params[:survey_answer_id],
      survey_answer_response: params[:survey_answer_response]
    )
    cmd.perform

    current_staff.update_recruitment_process!(
      training_process_code: TrainingScheduleApplicants::GetTrainingProcessCodeQuery.execute(current_staff.id)
    )
    notify_to_staff

    JsonResponse.redirect_path(status, "", details_staff_training_schedules_path)
  rescue ActiveRecord::RecordInvalid
    JsonResponse.redirect_path(false, I18n.t("errors.messages.generic_error"), "")
  end

  def list_by_training_center
    TrainingSchedule.available_schedules_by_location(
      params[:training_center_id].to_i,
      params[:current_training_id].to_i
    )
  end

  def cancel
    cmd = TrainingSchedules::StaffAbsentTrainingScheduleCommand.new(
      params[:applicant_ids],
      current_staff.id,
      params["consider_dropping_out"]
    )

    absent_applicants = cmd.perform
    return if absent_applicants.empty?

    TrainingSchedules::CreateSurveyResponsesWorker.perform_async(
      params[:survey_answer_id],
      params[:survey_answer_response],
      absent_applicants.pluck(:id),
      current_staff.id
    )

    absent_applicants.each do |applicant|
      save_staff_action_log(
        log_data(
          "staff_cancel_training_schedule",
          applicant.training_schedule_id,
          training_applicant_id: applicant.id
        )
      )

      TrainingSchedules::HandleAbsentActionWorker.perform_async(
        applicant.training_schedule_id,
        current_staff.name,
        true
      )
    end

    JsonResponse.success
  end

  def submit_email
    cmd = Staffs::SubmitEmailCommand.new(current_staff.id, params[:email])
    cmd.perform
  end

  def check_able_to_absent
    applicant_ids = cancellation_info_params.values.flatten.reject(&:blank?)
    applicants = current_staff.training_schedule_applicants.includes(:training_schedule)
      .where(id: applicant_ids)
    training_schedules = applicants.map(&:training_schedule)
    able_to_absent = training_schedules.all?(&:able_to_absent?)
    {
      able_to_absent: able_to_absent,
      redirect_path: reschedule_staff_training_schedules_path
    }
  end

  def cancellation_info
    applicant_ids = cancellation_info_params.values.flatten.reject(&:blank?)
    applicants = current_staff.training_schedule_applicants.includes(:training_schedule)
      .where(id: applicant_ids)

    training_schedules = applicants.map(&:training_schedule)
    return {able_to_absent: false} unless training_schedules.all?(&:able_to_absent?)

    data = {
      able_to_absent: true,
      cancel_applicant_ids: applicant_ids
    }

    if cancellation_info_params[:single_session_id].present?
      data[:message_content] = I18n.t("staff.training_schedule.modal.cancel_training.content_single_session")
    else
      data[:message_content] = I18n.t("staff.training_schedule.modal.cancel_training.content")
    end

    survey_question = SurveyQuestion.active.find_by(survey_question_type: :staff_cancel_training)

    data.merge(
      surveys: SurveyQuestionBlueprint.render_as_json(
        survey_question,
        view: :training_survey_for_staff
      )
    )
  end

  private

  def get_applicants
    current_staff.training_schedule_applicants.where(id: params[:applicant_ids])
  end

  def get_applied_schedules
    TrainingScheduleApplicants::GetAppliedTrainingQuery.execute(current_staff.id)
  end

  def schedule_params
    params.permit(:training_center_id, :first_session_id, :second_session_id, :single_session_id, :training_schedule)
  end

  def cancellation_info_params
    params.permit(:first_session_id, :second_session_id, :single_session_id)
  end

  def notify_to_staff
    TrainingScheduleReservationWorker.perform_async(current_staff.id)
  end

  def diff_reschedule
    # Get booked session id
    applied_id = schedule_params[:single_session_id]
    return [{}, false, I18n.t("staff.training_schedule.errors.must_select_training_session")] if applied_id.blank?

    booked_ids = [get_applied_schedules].flatten.compact.map(&:training_schedule_id)

    return [{}, true, ""] if applied_id.in? booked_ids

    diff = {added_ids: [applied_id], removed_ids: booked_ids}
    condition = TrainingScheduleCondition.new(
      [applied_id]
    ).valid_schedule?
    valid     = condition[:valid]
    error_msg = condition[:error_messages][0]

    [diff, valid, error_msg]
  end

  def include_absent? schedule_status_codes
    schedule_status_codes.any? do |status|
      TrainingScheduleApplicant::ABSENT_STATUSES.include?(
        TrainingScheduleApplicant.schedule_status_codes[status]
      )
    end
  end

  def submit_email_prams
    params.permit(:email).merge(skip_password_validation: true, is_email_present: true)
  end

  def send_otp_to_email email
    otp_service = OtpVerificationService.new(email)
    otp_code = otp_service.generate_otp
    return if otp_code.blank?

    StaffMailer.send_verification_otp(current_staff, otp_code, nil, email).deliver_now
  end

  def booked_applicants session
    return if session.blank?

    absent_statuses = TrainingScheduleApplicant::ABSENT_STATUSES
    return if absent_statuses.include?(TrainingScheduleApplicant.schedule_status_codes[session.schedule_status_code])

    session.training_schedule&.id
  end
end
