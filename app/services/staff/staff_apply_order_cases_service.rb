# TODO(PHUC): Deprecate this after PTS has been released
class Staff::StaffApplyOrderCasesService < Staff::StaffService
  def create
    apply_individual_order_case(OrderCase.find_by(id: apply_params[:order_case_id]))
  end

  def check_apply_condition
    order_case = OrderCase.find_by id: params[:order_case_id]
    return unless order_case

    apply_condition = ApplyOrderCaseCondition.new(order_case, current_staff,
      skip_reject_auto_matching_if_time_changed: true)
    status = apply_condition.valid?
    {
      status: status,
      errors: apply_condition.errors.messages
    }
  end

  def check_insurance_of_staff
    return false unless params[:order_case_id]

    cmd = Insurances::RequestStaffJoinInsurancesCommand.new(current_staff, params[:order_case_id])
    status = cmd.perform
    return status if status.present?

    {status: true}
  end

  private

  def apply_params
    params.require(:apply_params).permit(StaffApplyOrderCase::APPLY_PARAMS)
      .merge(status_id: StaffApplyOrderCase.status_ids[:not_processed])
  end

  def default_apply_params order_case
    {
      staff_id: current_staff.id,
      order_id: order_case.order_id,
      order_branch_id: order_case.order_branch_id,
      order_case_id: order_case.id,
      location_id: order_case.order.location_id,
      is_used_original_condition: true,
      status_id: StaffApplyOrderCase.status_ids[:not_processed]
    }
  end

  def re_apply_params
    apply_params.merge(deleted_at: nil)
  end

  def re_apply_training_params order_case
    default_apply_params(order_case).merge(deleted_at: nil)
  end

  def training_oc_ids
    [
      params.dig(:apply_params, :first_oc_id),
      params.dig(:apply_params, :second_oc_id)
    ].compact
  end

  def apply_individual_order_case order_case, is_training_schedule = false
    apply_condition = ApplyOrderCaseCondition.new(
      order_case,
      current_staff,
      skip_staff_status_1: is_training_schedule,
      skip_oc_status_3: is_training_schedule,
      skip_reject_auto_matching_if_time_changed: apply_params["is_used_original_condition"]
    )
    cmd = Insurances::RequestStaffJoinInsurancesCommand.new(current_staff, order_case,
      params[:apply_params][:accepted_apply])
    insurance_condition = cmd.perform
    status = false
    error_messages = {}
    return render_json_result(order_case.id, apply_condition, status, error_messages) unless
      apply_condition.valid? && insurance_condition.nil?

    status, apply_order_case, apply_w, overtime_w = apply_old_order_case(order_case, is_training_schedule) ||
      apply_new_order_case(status, order_case)
    error_messages = apply_order_case.errors.messages
    send_join_insurance_mail(order_case) if status && !is_training_schedule
    render_json_result(order_case.id, apply_condition, status, error_messages,
      apply_warnings: apply_w, overtime_warning: overtime_w)
  end

  def apply_old_order_case order_case, is_training_schedule
    apply_order_case = current_staff.staff_apply_order_cases.with_deleted
      .by_order_case_ids(order_case.id).first
    return if apply_order_case.blank?

    saoc_params = is_training_schedule ? re_apply_training_params(order_case) : re_apply_params
    status = apply_order_case.update(saoc_params)
    apply_order_case.update_after_re_applying if status
    [status, apply_order_case, nil, nil]
  end

  def apply_new_order_case status, order_case
    saoc_params = is_training_applicant?(order_case) ? default_apply_params(order_case) : apply_params
    apply_order_case = current_staff.staff_apply_order_cases.new(saoc_params)
    warning_ok, apply_warnings = check_apply_warnings(order_case)
    overtime_ok, overtime_warning = check_overtime_warning(order_case, apply_order_case)
    status = apply_order_case.save if apply_order_case.valid? && warning_ok && overtime_ok
    [status, apply_order_case, apply_warnings, overtime_warning]
  end

  # TODO(Phuong): check logic here
  def send_join_insurance_mail order_case
    return unless params[:apply_params][:accepted_apply] && !is_training_applicant?(order_case)

    MailJoinInsuranceWorker.perform_async(current_staff.id, order_case.id)
  end

  def check_apply_warnings order_case
    return [true, {}] if params[:apply_params][:accepted_out_of_contract].present? ||
      is_training_applicant?(order_case)

    apply_oc_warning = ApplyOrderCaseWarning.new(current_staff, order_case)
    apply_warnings = apply_oc_warning.errors.messages
    [apply_oc_warning.valid?, apply_warnings]
  end

  def check_overtime_warning order_case, apply_order_case = nil
    return [true, {}] if (params[:apply_params][:accepted_ot].present? &&
      params[:apply_params][:accepted_ot_in_week].present?) ||
      is_training_applicant?(order_case)

    request_data = {
      start_at: params[:apply_params][:requested_started_at],
      end_at: params[:apply_params][:requested_ended_at]
    }

    cmd = ApplyOrderCaseConditions::BaseCommand.new(
      order_case,
      current_staff,
      apply_order_case,
      request_data: request_data
    )

    warnings = cmd.perform

    [warnings[:status], warnings.merge(mobile_device: mobile_device?.present?)]
  end

  def render_json_result oc_id, apply_condition, status, error_messages, data = {}
    {
      status: status,
      order_case_id: oc_id,
      errors: error_messages,
      apply_condition_errors: apply_condition.errors.messages,
      warning_over_time: data[:overtime_warning],
      apply_warnings: data[:apply_warnings].as_json
    }
  end

  def is_training_applicant? order_case
    return if order_case.blank?

    order_case.training? || current_staff.current_level&.before_debut?
  end
end
