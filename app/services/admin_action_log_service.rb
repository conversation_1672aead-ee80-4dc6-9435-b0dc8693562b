class AdminActionLogService
  BYPASS_ADMIN_ID = 0
  BYPASS_ACTIONS = %i(auto_delete auto_restore)

  NORMAL_ACTIONS = %i(create update delete restore)
  SPECIAL_ACTIONS = {
    training_schedule: [
      :admin_books_staff,
      :admin_set_staff_to_absent_with_notice,
      :admin_set_staff_to_absent_without_notice,
      :admin_set_staff_to_joined,
      :admin_set_staff_reason_cancel
    ]
  }

  def initialize admin_id, target_type, target_id, action_type, params = {}
    @admin_id = admin_id
    @target_type = target_type.classify.constantize rescue nil
    @target_id = target_id
    @action_type = action_type.downcase.to_sym
    @params = params
    @special_info = params[:special_info]
    @objects_data = params[:objects_data]
  end

  attr_reader :logger, :admin_id, :target_type, :target_id, :target, :action_type,
    :special_info, :objects_data, :params

  def save_log
    return if target_type.blank?

    if objects_data.blank? && target_id
      @target = target_type.find_by(id: target_id)
      return if target.blank? && !delete_action_type?
    end

    save_bypass_action_type if action_type.in?(BYPASS_ACTIONS)
    return unless admin_id.present? && is_valid_action_type?

    AdminActionLog.create(log_params)
  end

  private
  def log_params
    result_params = {
      admin_id: admin_id,
      target_object_type: target_type_name,
      target_object_id: target_id,
      action_type: action_type.upcase
    }

    if objects_data.present?
      content = {}
      objects_data.each_key do |object_type|
        content[object_type.to_sym] = generate_content_params objects_data[object_type]
      end
      content = content.values.flatten
    else
      content = generate_content_params [target_id]
    end
    result_params.merge(content: content)
  end

  def generate_content_params object_ids
    if is_save_only_id?
      content = object_ids.map{|id| {"id" => id}}
    elsif special_info.present?
      content = [JSON.parse(special_info.to_json)]
    else
      content = [JSON.parse(object_ids.to_json)]
    end
    content = content.first if content.size == 1
    content
  end

  def is_save_only_id?
    [:delete, :create].include? action_type
  end

  def is_valid_action_type?
    special_action = SPECIAL_ACTIONS[target_type_name.underscore.to_sym] || []
    action_type.in? NORMAL_ACTIONS.concat(special_action)
  end

  def target_type_name
    target_type.name
  end

  def save_bypass_action_type
    AdminActionLog.create(
      log_params.merge(admin_id: BYPASS_ADMIN_ID)
    )
  end

  def delete_action_type?
    [:delete, :auto_delete].include?(action_type)
  end
end
