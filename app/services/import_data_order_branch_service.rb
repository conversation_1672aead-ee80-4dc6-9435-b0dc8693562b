class ImportDataOrderBranchService
  attr_reader :order_branch, :order, :data_info, :bill_paym
  attr_accessor :location_job_category_id, :invoice_target

  def initialize order_branch, order, bill_paym
    @order_branch = order_branch.reload
    @order = order
    @bill_paym = bill_paym || {}
  end

  def import_data
    data = []
    if order.individual?
      data = [EstimateOrderPriceService.individual_order(order_branch.estimate_price_params, true)]
    elsif order.batch?
      estimate_price_params = order_branch.estimate_price_params
      estimate_price_params = estimate_price_params.merge(order_branch.data_info) unless order_branch.data_info.nil?
      data = EstimateOrderPriceService.collective_order(estimate_price_params, true)
    elsif order.template?
      estimate_price_params = order_branch.estimate_price_params
      estimate_price_params = estimate_price_params.merge(order_branch.data_info) unless order_branch.data_info.nil?
      data = EstimateOrderPriceService.template_order(estimate_price_params, true)
    elsif order.non_recurring?
      data = EstimateOrderPriceService.non_recurring_order(order_branch.estimate_price_params, true)
    elsif order.recurring?
      data = EstimateOrderPriceService.recurring_order(order_branch.estimate_price_params, true)
    end

    order_cases = []
    data.each do |day_data|
      working_start_time = order_branch.working_start_time.strftime("%H:%M")
      working_end_time = order_branch.working_end_time.strftime("%H:%M")

      started_at = "#{day_data[:original_date]} #{working_start_time}".in_time_zone
      ended_at = "#{day_data[:original_date]} #{working_end_time}".in_time_zone
      ended_at += 1.day if ended_at < started_at
      order_cases << [order_branch.order_id, order_branch.id, started_at,
        ended_at, order_branch.staff_count, day_data[:summary],
        day_data[:immediate_fee], order_branch.is_special_offer, order_branch.special_offer_fee,
        order_branch.special_offer_note, location_job_category_id, invoice_target]
    end

    OrderCase.import OrderCase::IMPORT_ATTRIBUTES, order_cases, validate: false
    created_order_cases = order.order_cases
    created_order_cases.update_all(
      segment_id: order.order_segment_id,
      training_session_code: order.training_session_code,
      training_schedule_code: order.training_schedule_code
    )
    update_order_overall_dates
    create_order_portions
    order.reload
    created_order_cases.each do |oc|
      OrderCases::UpdatePeakPeriodCommand.new(oc, data_info).perform
    end
  end

  private
  def create_order_portions
    order_portions = []
    order_branch.order_cases.each do |order_case|
      order_branch.staff_count.times do
        order_portions << [order_branch.order_id, order_branch.id, order_case.id,
          order_case.case_started_at, order_case.case_ended_at]
      end
    end
    OrderPortion.import OrderPortion::UPDATABLE_ATTRS, order_portions, validate: false
    create_arrange_data unless order_branch.is_migrated
  end

  def create_arrange_data
    order_portions = order_branch.order_portions
    arrangements = order_portions.map do |portion|
      arrangement = Arrangement.new(
        order_id: order.id, order_branch_id: order_branch.id,
        order_case_id: portion.order_case_id, order_portion_id: portion.id,
        working_started_at: portion.case_started_at, working_ended_at: portion.case_ended_at,
        order_segment_id: order.order_segment_id.to_i,
        is_penalty_target: false,
        current_location_type: order.current_location_type.to_sym,
        billing_payment_template_id: bill_paym[:id]
      )
      Arrangement::BREAK_TIME_ATTRS.each{|attr| arrangement[attr] = order_branch[attr]}
      %i(set_working_time set_rest_time_date set_break_time).each{|method| arrangement.send method}
      arrangement.is_penalty_target = nil
      arrangement
    end
    Arrangement.import arrangements, validate: false

    created_arrangements = Arrangement.includes(
      :staff,
      order_portion: [:order_branch, :order_case, {arrangement: [
        :order_case, {order: [:corporation]}
      ]}]
    ).by_order_portions_ids(order_portions.pluck(:id))
    arrange_payments = []
    arrange_billings = []
    work_achievements = []
    @data_info = OrderCase.data_info(order.order_cases, order_branch.data_info)
    created_arrangements.each do |arrangement|
      work_achievement = WorkAchievement.new(arrangement_id: arrangement.id,
        working_time_status_id: WorkAchievement.working_time_status_ids[:not_inputted])
      arr_paym = ArrangePayment.new(arrangement_id: arrangement.id)
      arr_bill = ArrangeBilling.new(arrangement_id: arrangement.id)
      if order.regular_order?
        arr_paym.payment_basic_unit_price = order.payment_basic_unit_price
        arr_paym.payment_night_unit_price = order.payment_night_unit_price
        arr_bill.billing_basic_unit_price = order.billing_basic_unit_price
        arr_bill.billing_night_unit_price = order.billing_night_unit_price
      else
        arr_paym.payment_basic_unit_price = bill_paym[:payment_basic_unit_price]
        arr_paym.payment_night_unit_price = bill_paym[:payment_night_unit_price]
        arr_paym.payment_field_1 = bill_paym[:transportation_fee]
        arr_bill.billing_basic_unit_price = bill_paym[:billing_basic_unit_price]
        arr_bill.billing_night_unit_price = bill_paym[:billing_night_unit_price]
        arr_bill.billing_field_1 = bill_paym[:area_allowance]
        arr_bill.billing_field_2 = bill_paym[:short_allowance]
        arr_bill.billing_tax_exemption = bill_paym[:tax_exemption]
      end
      update_billing_payment_logs arrangement.id
      cal_service = Calculation::TriggerArrangementDataService.new(arrangement.order_portion, work_achievement,
        arr_paym, arr_bill, not_trigger: true, data: data_info)
      cal_service.billing_payment_template = arrangement.billing_payment_template if
        arrangement.billing_payment_template_id.present?
      cal_service.execute
      work_achievements << work_achievement
      arrange_payments << arr_paym
      arrange_billings << arr_bill
    end

    WorkAchievement.import work_achievements, validate: false
    ArrangePayment.import arrange_payments, validate: false
    ArrangeBilling.import arrange_billings, validate: false
  end

  def update_order_overall_dates
    return unless order.regular_order?

    UpdateRegularOrderOverallDatesService.new(order).update_overall_dates
  end

  def update_billing_payment_logs arrangement_id
    return if bill_paym.blank?

    ArrangePaymentLog.create(
      arrangement_id: arrangement_id,
      admin_id: order.created_admin_id,
      payment_basic_unit_price: bill_paym[:payment_basic_unit_price],
      payment_night_unit_price: bill_paym[:payment_night_unit_price],
      payment_field_1: bill_paym[:transportation_fee]
    )
    ArrangeBillingLog.create(
      arrangement_id: arrangement_id,
      admin_id: order.created_admin_id,
      billing_basic_unit_price: bill_paym[:billing_basic_unit_price],
      billing_night_unit_price: bill_paym[:billing_night_unit_price],
      billing_field_1: bill_paym[:area_allowance],
      billing_field_2: bill_paym[:short_allowance],
      billing_field_4: bill_paym[:absence_discount],
      billing_tax_exemption: bill_paym[:tax_exemption]
    )
  end
end
