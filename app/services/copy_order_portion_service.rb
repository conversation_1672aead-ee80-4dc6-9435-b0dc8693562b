class CopyOrderPortionService
  REST_TIME_RANGES = 1..3

  attr_reader :params, :arrangement, :order, :arrange_billing, :order_case, :errors

  def initialize params, arrangement, admin
    @params = format_param params[:arrangement]
    @admin = admin
    @arrangement = arrangement
    @order = arrangement.order
    @order_case = arrangement.order_case
    @arrange_billing = arrangement.arrange_billing
    @errors = []
  end

  def copy new_arrangement
    @new_portion = build_order_portion
    ActiveRecord::Base.transaction do
      @new_portion.save!
      new_arrangement.order_portion_id = @new_portion.id
      %i(set_working_time set_rest_time_date set_break_time).each{|attr| arrangement.send attr}
      clone_data = CreateOrderBranchService.new(new_arrangement).create_branch_data
      @new_portion.reload
      new_arrangement = clone_data[0]
      if clone_data[2].present?
        @errors = clone_data[2]
        raise ActiveRecord::Rollback
      end
      ArrangeLog.create_by(ArrangeLog.action_types[:oc_created], @admin.id, new_arrangement)
      save_arrangement_data(@new_portion, new_arrangement)
      new_arrangement.reload
      order_case.update_status_follow_order_portion
    end
  rescue ActiveRecord::RecordInvalid
    @errors << @new_portion&.errors&.messages&.values&.first
    @errors << new_arrangement&.errors&.messages&.values&.first
    @errors << order_case&.errors&.messages&.values&.first
    @errors = @errors.reject(&:blank?)
  rescue StandardError => e
    @errors << e
  end

  def build_arrangement
    Arrangement.new(order_id: arrangement.order_id, order_branch_id: arrangement.order_branch_id,
      order_case_id: arrangement.order_case_id, order_portion_id: arrangement.order_portion_id,
      working_started_at: params[:working_started_at], working_ended_at: params[:working_ended_at],
      rest1_started_at: params[:rest1_started_at], rest1_ended_at: params[:rest1_ended_at],
      rest2_started_at: params[:rest2_started_at], rest2_ended_at: params[:rest2_ended_at],
      rest3_started_at: params[:rest3_started_at], rest3_ended_at: params[:rest3_ended_at],
      order_segment_id: order.order_segment_id.to_i, parent_id: arrangement.id,
      is_penalty_target: false,
      is_copy_portion: true, billing_payment_template_id: arrangement.billing_payment_template_id)
  end

  private
  def format_param params
    started_date = params[:working_started_at]
    started_time = params[:working_time_started_at]
    ended_time = params[:working_time_ended_at]
    started_date_time = ServerTime.parse("#{started_date} #{started_time}")
    ended_date_time = ServerTime.parse("#{started_date} #{ended_time}")
    params[:working_started_at] = started_time.blank? ? "" : started_date_time
    params[:working_ended_at] = ended_time.blank? ? "" : ended_date_time
    return params if started_time.blank? || ended_time.blank?

    if Time.parse(ended_time) < Time.parse(started_time)
      params[:working_ended_at] = ended_date_time + 1.day
    else
      params[:working_ended_at] = ended_date_time
    end
    params
  end

  def save_arrangement_data portion, new_arrangement
    work_achievement = WorkAchievement.new(arrangement_id: new_arrangement.id,
      working_time_status_id: WorkAchievement.working_time_status_ids[:not_inputted])
    arr_paym = ArrangePayment.new(arrangement_id: new_arrangement.id)
    arr_bill = ArrangeBilling.new(arrangement_id: new_arrangement.id)
    trigger_service = Calculation::TriggerArrangementDataService.new(portion, work_achievement,
      arr_paym, arr_bill, not_trigger: true)
    trigger_service.billing_payment_template = new_arrangement.billing_payment_template if
        new_arrangement.billing_payment_template_id.present?
    trigger_service.execute
    work_achievement.save!
    arr_paym.save!
    arr_bill.save!
    ArrangeBilling::COPY_FIELD_ATTRS.each do |field|
      arr_bill[field] = params[field] == "true" ? arrange_billing.send(field) : 0
    end
    ArrangeBilling::UPDATABLE_BILLING_FIELDS.each do |field|
      arr_bill[field] = arrange_billing.send(field)
    end
    arr_bill.calculate_trigger :arrange_billing,
      (ArrangeBilling::UPDATABLE_BILLING_FIELDS + ArrangeBilling::COPY_FIELD_ATTRS).uniq, true
  end

  def build_order_portion
    OrderPortion.new(order_id: arrangement.order_id, order_branch_id: arrangement.order_branch_id,
      order_case_id: arrangement.order_case_id, case_started_at: params[:working_started_at],
      case_ended_at: params[:working_ended_at], is_manual_copy: true,
      original_portion_id: arrangement.order_portion_id)
  end
end
