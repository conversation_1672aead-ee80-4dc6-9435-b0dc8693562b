class MessageSenderService
  class << self
    include Rails.application.routes.url_helpers

    def send_verify_otp_message tel, otp_code
      content = I18n.t("staff.sms.verify_otp", otp_code: otp_code)
      send_sms(tel, content)
    end

    def send_recruited_message tel, prefix_option, url
      content = I18n.t("staff.sms.recruited_staff#{prefix_option}", registered_profile_url: url)
      send_sms(tel, content)
    end

    def send_rejected_staff_message tel
      content = I18n.t("staff.sms.rejected_staff")
      send_sms(tel, content)
    end

    def send_canceled_order_case_message tel, location_name, case_started_at
      content = I18n.t("staff.sms.canceled_order_case", location_name: location_name,
        working_datetime: format_full_datetime(case_started_at))
      send_sms(tel, content)
    end

    def send_rejected_staff_apply_message tel, location_name, is_registration_training_job = false
      if is_registration_training_job
        content = I18n.t("staff.sms.rejected_staff_apply_training", location_name: location_name)
      else
        content = I18n.t("staff.sms.rejected_staff_apply", location_name: location_name)
      end
      send_sms(tel, content)
    end

    def send_reschedule_reminder_interview_message tel, user_name, date_format
      content = I18n.t("staff.sms.reschedule_reminder_interview",
        user_name: user_name, date_format: date_format,
        url: details_staff_interview_schedules_url)
      send_sms(tel, content)
    end

    def notify_interview_deleted tel, user_name, date_format
      content = I18n.t("staff.sms.notify_interview_deleted",
        user_name: user_name, date_format: date_format,
        url: details_staff_interview_schedules_url)
      send_sms(tel, content)
    end

    def notify_training_schedule_deleted tel, user_name, date_format
      content = I18n.t("staff.sms.notify_training_schedule_deleted",
        user_name: user_name, date_format: date_format,
        url: details_staff_training_schedules_url)
      send_sms(tel, content)
    end

    def notify_interview_applied tel, room_url, date_format
      apply_data = {
        room_url: room_url,
        date_format: date_format,
        interview_url: details_staff_interview_schedules_url,
        skill_check_url: Settings.skill_check_url
      }
      content = I18n.t("staff.sms.notify_interview_applied", **apply_data)
      send_sms(tel, content)
    end

    def notify_training_schedule_booked tel, apply_data
      content = I18n.t("staff.sms.notify_training_schedule_applied",
        **apply_data.merge(training_details_url: details_staff_training_schedules_url))
      send_sms(tel, content)
    end

    def remind_staff_interview tel, full_date_time, room_url
      content = I18n.t("staff.sms.remind_staff_interview",
        full_date_time: full_date_time,
        room_url: room_url,
        interview_url: details_staff_interview_schedules_url,
        skill_check_url: Settings.skill_check_url)
      send_sms(tel, content)
    end

    def remind_staff_training tel, user_name
      content = I18n.t("staff.sms.remind_staff_training",
        user_name: user_name,
        training_details_url: details_staff_training_schedules_url)
      send_sms(tel, content)
    end

    def notify_training_schedule_absent tel, apply_data
      content = I18n.t("staff.sms.absence_staff_training",
        **apply_data.merge(reschedule_url: reschedule_staff_training_schedules_url))
      send_sms(tel, content)
    end

    def notify_staff_new_training_schedule tel, apply_data
      content = I18n.t("staff.sms.assign_staff_training",
        **apply_data.merge(training_details_url: details_staff_training_schedules_url))
      send_sms(tel, content)
    end

    private

    def send_sms tel, content, callback = false
      return false if tel.blank?

      message_key = get_message_key(tel, content)
      write_log "START SEND MESSAGE #{message_key} - #{tel}\n#{content}"
      formatted_tel = tel.gsub("-", "")

      notify_sent_message(tel, content)

      unless can_send_sms?(formatted_tel)
        write_log "FINISHED (NOT SENT) #{message_key} - Callback result #{callback ? 'ON' : 'OFF'}"
        return true
      end

      req_params = {
        phone_number: formatted_tel,
        content: content,
        sms_id: message_key
      }
      result = {}
      begin
        result = MediaSmsService.new(req_params, callback).send_sms
        raise unless result[:status]

        write_log "FINISHED SENDING MESSAGE #{message_key} - Callback result #{callback ? 'ON' : 'OFF'}"
        save_message_key(message_key) if callback
        true
      rescue StandardError => e
        write_log(result.present? ? result[:result_message] : e)
        false
      end
    end

    def can_send_sms? tel
      Rails.env.production? ||
        (Rails.env.staging? && tel.in?(Settings.message_sender.whitelist_phone_numbers))
    end

    def write_log message
      logger = Logger.new Rails.root.join("log", "media_sms.log")
      logger.info message
    end

    def get_message_key tel, message
      message_md5_format = Digest::MD5.hexdigest "#{tel}-#{message}"
      "#{message_md5_format}#{ServerTime.now.to_i}"
    end

    def notify_sent_message tel, content
      return unless Rails.env.staging?

      CustomNotifier::SmsNotifier.new.call(content, tel: tel)
    end

    def format_full_datetime datetime
      return "" if datetime.blank?

      I18n.l(datetime, format: Settings.datetime.full_weekday_format)
    end

    def save_message_key message_key
      # TODO: save message key when implement callback
    end

    def default_url_options
      Rails.application.config.action_mailer.default_url_options
    end

    def staff_host
      default_url_options.merge(protocol: "https", host: Settings.config.mailer.host)
    end
  end
end
