class AdminAvatarUploader < CarrierWave::Uploader::Base
  include CarrierWave::MiniMagick
  version :avatar_custom do
    process resize_to_fill: [300, 300]
  end

  def extension_allowlist
    %w(jpg jpeg gif png)
  end

  def size_range
    1..(10.megabytes)
  end

  def filename
    @name ||= "#{timestamp}-#{md5}#{File.extname(super)}" if super
  end

  def md5
    @md5 ||= Digest::MD5.hexdigest model.send(mounted_as).read.to_s
  end

  def store_dir
    "uploads/#{model.class.to_s.underscore}/#{model.id}"
  end

  def timestamp
    var = :"@#{mounted_as}_timestamp"
    model.instance_variable_get(var) || model.instance_variable_set(var, Time.now.to_i)
  end
end
