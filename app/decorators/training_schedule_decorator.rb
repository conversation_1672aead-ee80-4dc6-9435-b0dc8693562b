module TrainingScheduleDecorator
  extend ActiveSupport::Concern

  TRAINING_SESSION_MAPPING = {
    training_first_round: I18n.t("admin.training_schedule.training_session_codes.training_first_round"),
    training_second_round: I18n.t("admin.training_schedule.training_session_codes.training_second_round"),
    single_session: I18n.t("admin.training_schedule.training_session_codes.single_session")
  }

  def applicant_staff_ids
    training_schedule_applicants.pluck(:staff_id)
  end

  def booked_staff_names booked_applicants
    return [] if booked_applicants.blank?

    booked_applicants.map{|staff_apply| staff_apply&.staff&.account_name}.compact
  end

  def list_format booked_applicants
    filled_slots = booked_applicants.presence&.size || 0
    {
      schedule_id: id,
      location_name: location_name,
      training_date: format_date(start_time, Settings.date.dash_separator),
      training_datetime: formatted_datetime,
      start_time: start_time_text,
      end_time: end_time_text,
      training_session: training_session_text,
      slots: {
        filled: filled_slots,
        total: total_portion
      },
      person_in_charge: person_in_charge_name.presence,
      deleted_by: deleted_by,
      deleted_at: {
        date: format_date(deleted_at, Settings.date.dash_separator),
        time: deleted_at&.strftime(Settings.time.formats)
      }
    }
  end

  def deleted_format
    {
      id: id,
      location_id: location_id,
      location_name: location_name,
      start_time: start_time_text,
      end_time: end_time_text,
      training_session: training_session_text,
      training_session_code: TrainingSchedule.training_session_codes[training_session_code],
      total_portion: total_portion,
      person_in_charge: person_in_charge_name.presence,
      person_in_charge_id: person_in_charge_id,
      training_datetime: training_time_with_day
    }
  end

  def training_session_text
    TRAINING_SESSION_MAPPING[training_session_code.to_sym]
  end

  def formatted_calendar_data booked_applicants
    {
      id: id,
      height: (end_time_text.to_time - start_time_text.to_time).to_i / view_time,
      training_date: training_date,
      start_time: start_time_text,
      end_time: end_time_text,
      total_apply: booked_applicants.presence&.size || 0,
      total_portion: total_portion,
      location_name: location_name,
      applieds_names: booked_staff_names(booked_applicants),
      person_in_charge: person_in_charge_name || "",
      person_in_charge_id: person_in_charge_id,
      training_session_code: training_session_text
    }
  end

  def view_time
    Settings.training_schedules.view.time * 60
  end

  def location_json
    return nil unless location_id

    {
      id: location_id,
      name: location_name
    }
  end

  def person_in_charge_json current_admin
    {
      id: person_in_charge_id.presence || current_admin.id,
      name: person_in_charge_name || current_admin.name
    }
  end

  def show_format current_admin
    {
      id: id,
      training_date: training_date_text,
      start_time: start_time_text,
      end_time: end_time_text,
      training_session_code: training_session_text,
      location: location_json,
      has_person_in_charge: person_in_charge_id.present?,
      person_in_charge: person_in_charge_json(current_admin),
      can_change_person_in_charge: can_change_person_in_charge?(current_admin.id),
      applicants: training_schedule_applicants.not_absent.as_json(
        only: [:staff_id], methods: [:staff_name, :staff_number]
      ),
      total_applicants: training_schedule_applicants.size
    }
  end

  # Overrides the default as_json method to allow renaming of keys in the resulting JSON object.
  def as_json options = {}
    json = super(options)
    if options.key?(:rename)
      options[:rename].each do |old_key, new_key|
        json[new_key.to_s] = json.delete(old_key.to_s)
      end
    end
    json
  end

  # * --- Date/time methods ---
  def format_date date, format
    return "" unless date

    I18n.l date, format: format
  end

  def training_date format = Settings.date.day_and_month
    format_date(start_time, format)
  end

  def training_date_weekday format = Settings.date.month_date
    format_date(start_time, format)
  end

  def training_date_text format = Settings.date.formats
    format_date(start_time, format)
  end

  def working_date format = Settings.date.day_and_date
    format_date(start_time, format)
  end

  def format_time time, format = Settings.time.formats
    return "" unless time

    time.strftime(format)
  end

  def formatted_datetime
    "#{format_date(start_time, Settings.date.year_month_and_date)} #{training_time}"
  end

  def start_time_text
    format_time(start_time)
  end

  def end_time_text
    format_time(end_time)
  end

  def training_time
    "#{start_time_text} ~ #{end_time_text}"
  end

  def training_time_with_day
    "#{working_date} | #{training_time}"
  end

  def formatted_absent_deadline
    "#{format_date(absent_deadline, Settings.date.year_month_and_date)} #{format_time(absent_deadline)}"
  end
  # * ------

  def default_mailer_data
    {
      trainer_name: person_in_charge_name,
      location_name: location_name,
      location_prefecture: location_prefecture_name,
      training_date_weekday: training_date_weekday,
      training_datetime: training_time_with_day
    }
  end

  def booked_mailer_data deleted_schedules = []
    default_mailer_data.merge(
      schedule_id: id,
      deleted_schedules: deleted_schedules
    )
  end

  def absent_mailer_data deleted_schedules = []
    default_mailer_data.merge(
      deleted_schedules: deleted_schedules
    )
  end

  def delete_mailer_data
    default_mailer_data
  end

  def auto_restore_mail_data restored_schedules = []
    {
      schedule_id: id,
      training_date_weekday: training_date_weekday,
      person_in_charge_id: person_in_charge_id,
      location_name: location_name,
      person_in_charge: person_in_charge_name.presence,
      training_datetime: training_time_with_day,
      restored_schedules: restored_schedules
    }
  end

  private

  class_methods do
    def label_i18n
      I18n.t("admin.training_schedule.export.list.columns").values
    end
  end
end
