module StaffMessages
  class MessagesByTypeQuery
    def initialize staff_id, message_type, options = {}
      @staff_id = staff_id
      @message_type = message_type
      @options = options
    end

    def perform
      staff_messages = StaffMessage.where(staff_id: @staff_id)
      return staff_messages if staff_messages.blank?

      staff_messages = filter_by_working_date(staff_messages)

      case message_type
      when :from_location
        messages_from_location(staff_messages)
      when :from_corporation_group
        messages_from_corporation_group(staff_messages)
      when :from_corporation
        messages_from_corporation(staff_messages)
      else
        staff_messages
      end
    end

    private

    def filter_by_working_date staff_messages
      return staff_messages if @options[:working_date].blank?

      staff_messages.by_working_date(@options[:working_date])
    end

    def messages_from_location staff_messages
      return staff_messages.from_location if @options[:location_ids].blank?

      staff_messages.from_location.by_location(@options[:location_ids])
    end

    def messages_from_corporation_group staff_messages
      return staff_messages.from_corporation_group if @options[:corporation_group_ids].blank?

      staff_messages.from_corporation_group.by_location(@options[:corporation_group_ids])
    end

    def messages_from_corporation staff_messages
      return staff_messages.from_corporation if @options[:corporation_ids].blank?

      staff_messages.from_corporation.by_corporation(@options[:corporation_ids])
    end
  end
end
