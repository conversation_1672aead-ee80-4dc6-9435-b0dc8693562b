module TrainingScheduleApplicants
  class GetStaffAppliedHistoriesQuery
    class << self
      def execute staff_id
        applicants = TrainingScheduleApplicant.with_deleted
          .select("training_schedule_applicants.id, training_schedule_applicants.schedule_status_code,
            training_schedule_applicants.training_schedule_id,
            training_schedule_applicants.deleted_at,
            training_schedules.training_session_code AS session,
            training_schedules.start_time AS training_start_time")
          .joins(:training_schedule)
          .where(staff_id: staff_id)
          .order(order_string)

        applicants.map{|applicant| staff_histories_info(applicant)}
      end

      private

      def order_string
        ActiveRecord::Base.send(
          :sanitize_sql_for_order,
          [Arel.sql("FIELD(session, ?), schedule_status_code"), order_session_code]
        )
      end

      def order_session_code
        [
          TrainingSchedule.training_session_codes[:single_session],
          TrainingSchedule.training_session_codes[:training_first_round],
          TrainingSchedule.training_session_codes[:training_second_round]
        ]
      end

      def staff_histories_info applicant
        {
          id: applicant.id,
          schedule_id: applicant.training_schedule_id,
          session: training_session_text(applicant.session),
          schedule_start_time: schedule_start_time_format(applicant.training_start_time),
          status: applicant.schedule_status_text,
          status_code: applicant.schedule_status_code,
          is_deleted: applicant.deleted_at?
        }
      end

      def training_session_text session
        return if session.blank?

        I18n.t("admin.training_schedule.training_session_codes.#{TrainingSchedule.training_session_codes.key(session)}")
      end

      def schedule_start_time_format time
        return "" unless time

        I18n.l(ServerTime.parse(time.to_s), format: Settings.date.day_and_date)
      end
    end
  end
end
