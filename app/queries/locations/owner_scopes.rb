module Locations
  module OwnerScopes
    LOCATION_PIC_TYPES = {haken_destination: 1, claim: 2, mandator: 3}

    def select_in_owner_info
      query = "locations.id, locations.name, locations.short_name,"
      query << LOCATION_PIC_TYPES.map do |type, value|
        build_location_pics_name_query(type, value)
      end.join(",")

      self.select(query.squish)
    end

    def order_in_owner_info sort_key, is_descending
      order_type = is_descending ? :desc : :asc
      case sort_key.to_sym
      when :id, :name, :short_name
        order(sort_key.to_sym => order_type)
      when :haken_destination_name
        order_type = order_type.to_s
        order("haken_destination_name #{order_type}")
      when :claim_name
        order_type = order_type.to_s
        order("claim_name #{order_type}")
      when :mandator_name
        order_type = order_type.to_s
        order("mandator_name #{order_type}")
      else
        order(id: :desc)
      end
    end

    private

    def build_location_pics_name_query type, pic_type_id
      "(SELECT name
        FROM location_pics
        WHERE location_pics.location_id = locations.id
        AND location_pics.pic_type_id = #{pic_type_id}
        ORDER BY updated_at DESC LIMIT 1) AS #{type}_name"
    end
  end
end
