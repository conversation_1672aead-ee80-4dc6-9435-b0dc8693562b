class StaffMailer < ApplicationMailer
  include Rails.application.routes.url_helpers

  helper :application
  default from: Settings.config.mailer.default_from

  def reset_password_mail account, token
    mail(to: account.email, subject: t("mailer.staff.reset_pw.subject")) do |format|
      format.text do
        render locals: {
          name: account.name,
          token: token,
          support_email: support_email(account.staff)
        }
      end
    end
  end

  def cancel_order_less_than_minimum_wage staff, order_portions
    subject = t("mailer.staff.cancel_order_less_than_minimum_wage.subject")

    mail(to: staff.account_email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff.account_name,
          order_portions: order_portions,
          support_email: support_email(staff)
        }
      end
    end
  end

  def cancel_job_an_hour_ago staff_id, staff_ocs
    staff = Staff.find_by(id: staff_id)
    return unless staff

    subject = t("mailer.staff.cancel_job_an_hour_ago.subject")
    mail(to: staff.account_email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff.account_name,
          staff_ocs: staff_ocs,
          url: staff_my_page_url
        }
      end
    end
  end

  def join_insurance_subsection staff_name, staff_number, admin_name, admin_email
    subject = t("mailer.staff.join_insurance_subsection.subject")
    mail(to: admin_email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff_name,
          staff_code: staff_number,
          admin_name: admin_name,
          admin_email: admin_email
        }
      end
    end
  end

  def join_employment_insurance staff_name, staff_number, admin_name, admin_email
    subject = t("mailer.staff.join_employment_insurance.subject")
    mail(to: admin_email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff_name,
          staff_code: staff_number,
          admin_name: admin_name,
          admin_email: admin_email
        }
      end
    end
  end

  def reject_employment_insurance_to_admin staff, admin
    subject = t("mailer.staff.reject_employment_insurance.subject")
    mail(to: admin.email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff.account_name,
          staff_code: staff.staff_number,
          admin_name: admin.name,
          admin_email: admin.email
        }
      end
    end
  end

  def reject_subsection_insurance_to_admin staff, admin
    subject = t("mailer.staff.reject_subsection_insurance.subject")
    mail(to: admin.email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff.account_name,
          staff_code: staff.staff_number,
          admin_name: admin.name,
          admin_email: admin.email
        }
      end
    end
  end

  def reject_subsection_and_employment_insurance_to_admin staff, admin
    subject = t("mailer.staff.reject_subsection_and_employment_insurance.subject")
    mail(to: admin.email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff.account_name,
          staff_code: staff.staff_number,
          admin_name: admin.name,
          admin_email: admin.email
        }
      end
    end
  end

  def notify_acquired_insurance_to_admin staff, admin
    subject = t("mailer.staff.notify_acquired_insurance_to_admin.subject")
    mail(to: admin.email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff.account_name,
          staff_code: staff.staff_number,
          admin_name: admin.name,
          admin_email: admin.email
        }
      end
    end
  end

  def send_verification_otp staff, otp_code, otp_url = nil, custom_email = nil
    subject = t("mailer.staff.send_verification_otp.subject", otp_code: otp_code)
    to_email = custom_email || staff.account.email
    mail(to: to_email, subject: subject) do |format|
      format.html do
        render locals: {
          staff_name: staff.account.name,
          otp_code: otp_code,
          otp_url: otp_url,
          support_email: support_email(staff)
        }
      end
    end
  end

  def send_reschedule_interview_reminder staff, date_format
    subject = t("mailer.staff.send_reschedule_interview_reminder.subject",
      date_format: date_format)
    mail(to: staff.account.email, subject: subject) do |format|
      format.text do
        render locals: {
          staff_name: staff.account.name,
          date_format: date_format,
          url: details_staff_interview_schedules_url,
          support_email: support_email(staff)
        }
      end
    end
  end

  def send_mail_entry staff
    mail(to: staff.email, subject: t("mailer.staff.send_mail_entry.subject")) do |format|
      format.text do
        render locals: {
          name: staff.name,
          is_staff_mobile: staff.app?,
          support_email: support_email(staff)
        }
      end
    end
  end

  def send_mail_entry_has_exp staff
    mail(to: staff.email, subject: t("mailer.staff.send_mail_entry_has_exp.subject")) do |format|
      format.text do
        render locals: {
          name: staff.name,
          support_email: support_email(staff)
        }
      end
    end
  end

  def send_mail_entry_no_exp staff
    mail(to: staff.email, subject: t("mailer.staff.send_mail_entry_no_exp.subject")) do |format|
      format.text do
        render locals: {
          name: staff.name,
          support_email: support_email(staff)
        }
      end
    end
  end

  def reject_training_job_mail staff_apply_order_case
    staff = staff_apply_order_case.staff
    order_case = staff_apply_order_case.order_case
    subject = t("mailer.staff.reject_training_job_mail.subject")
    order_case_info = {
      location_name: order_case.site_name,
      station: order_case.location_stations_1_station_name,
      started_date: order_case.started_at_format_month_day,
      start_time: order_case.case_started_at&.strftime(Settings.time.formats),
      end_time: order_case.case_ended_at&.strftime(Settings.time.formats),
      break_time: order_case.order_branch.break_time_format_hour
    }
    mail(to: staff.account_email, subject: subject) do |format|
      format.html do
        render locals: {
          name: staff.account_name,
          order_case_info: order_case_info,
          url: details_staff_training_schedules_url,
          support_email: support_email(staff)
        }
      end
    end
  end

  def notify_interview_deleted staff, date_format
    subject = t("mailer.staff.notify_interview_deleted.subject",
      date_format: date_format)
    mail(to: staff.account.email, subject: subject) do |format|
      format.text do
        render locals: {
          staff_name: staff.account.name,
          date_format: date_format,
          url: details_staff_interview_schedules_url,
          support_email: support_email(staff)
        }
      end
    end
  end

  def notify_interview_applied staff, room_url, date_format
    subject = t("mailer.staff.notify_interview_applied.subject")
    mail(to: staff.account.email, subject: subject) do |format|
      format.text do
        render locals: {
          staff_name: staff.account.name,
          room_url: room_url,
          date_format: date_format,
          interview_url: details_staff_interview_schedules_url,
          skill_check_url: Settings.skill_check_url
        }
      end
    end
  end

  def remind_staff_interview staff, room_url, interview_start_time
    data = build_data_for_remind_interview_mail(interview_start_time, room_url)
    subject = t("mailer.staff.remind_staff_interview.subject", date_format: data[:interview_date])
    account = staff.account
    locals_partial = data.merge(staff_name: account.name)

    status = mail(to: account.email, subject: subject) do |format|
      format.html do
        render locals: locals_partial
      end
    end
    create_staff_mail_history locals_partial, subject, staff if status
    status
  end

  def notify_staff_new_training_schedule staff, datetime, user_name,
    location_name, location_full_address
    subject = t("mailer.staff.notify_staff_new_training_schedule.subject")
    mail(to: staff.account.email, subject: subject) do |format|
      format.html do
        render locals: {
          user_name: user_name,
          datetime: datetime,
          location_name: location_name,
          location_full_address: location_full_address
        }
      end
    end
  end

  def notify_training_schedule_booked staff, apply_data
    subject = t("mailer.staff.notify_training_schedule_booked.subject")
    mail(to: staff.account.email, subject: subject) do |format|
      format.html do
        render locals: {
          user_name: staff&.account_name,
          first_session_datetime: apply_data[:first_session_datetime],
          second_session_datetime: apply_data[:second_session_datetime],
          single_session_datetime: apply_data[:single_session_datetime],
          location_name: apply_data[:location_name],
          location_full_address: apply_data[:location_full_address]
        }
      end
    end
  end

  def notify_training_schedule_absent account, location_name, formatted_datetime, training_date_weekday
    subject = t("mailer.staff.notify_training_schedule_absent.subject",
      training_date_weekday: training_date_weekday)
    mail(to: account.email, subject: subject) do |format|
      format.html do
        render locals: {
          user_name: account.name,
          reschedule_staff_training_schedules_url: reschedule_staff_training_schedules_url,
          location_name: location_name,
          formatted_datetime: formatted_datetime
        }
      end
    end
  end

  def notify_training_schedule_deleted account, location_name, formatted_datetime, training_date_weekday
    subject = t("mailer.staff.notify_training_schedule_deleted.subject",
      training_date_weekday: training_date_weekday)
    mail(to: account.email, subject: subject) do |format|
      format.html do
        render locals: {
          user_name: account.name,
          training_details_url: details_staff_training_schedules_url,
          location_name: location_name,
          formatted_datetime: formatted_datetime
        }
      end
    end
  end

  def remind_staff_training staff, location_name, location_full_address,
    location_stations, start_time_text, end_time_text
    subject = t("mailer.staff.remind_staff_training.subject")
    mail(to: staff.account.email, subject: subject) do |format|
      format.html do
        render locals: {
          user_name: staff&.account_name,
          location_name: location_name,
          location_full_address: location_full_address,
          location_stations: location_stations,
          start_time_text: start_time_text,
          end_time_text: end_time_text
        }
      end
    end
  end

  def remind_two_hours_before_training staff, location_name, location_full_address,
    location_stations, start_time_text, end_time_text, training_date_weekday
    subject = t("mailer.staff.remind_two_hours_before_training.subject",
      training_date_weekday: training_date_weekday)
    mail(to: staff.account.email, subject: subject) do |format|
      format.html do
        render locals: {
          user_name: staff&.account_name,
          location_name: location_name,
          location_full_address: location_full_address,
          location_stations: location_stations,
          start_time_text: start_time_text,
          end_time_text: end_time_text
        }
      end
    end
  end

  private

  def staff_host
    default_url_options.merge(protocol: "https", host: Settings.config.mailer.host)
  end

  def create_staff_mail_history locals_partial, subject, staff
    content = render locals: locals_partial.merge(is_render_footer: false), layout: false
    StaffMailHistory.create staff_id: staff.id, subject_mail: subject,
      destination_email: staff.account_email, content: content,
      send_time: ServerTime.now, send_date: ServerTime.now
  end

  def build_data_for_remind_interview_mail interview_start_time, room_url
    interview_full_datetime = DateTimeFormatting.jp_full_date_time(interview_start_time)
    interview_date = DateTimeFormatting.month_and_date(interview_start_time)
    interview_month_datetime = DateTimeFormatting.jp_month_date_time(interview_start_time)

    {
      interview_date: interview_date,
      interview_month_datetime: interview_month_datetime,
      interview_full_datetime: interview_full_datetime,
      interview_room_url: room_url,
      interview_url: details_staff_interview_schedules_url,
      skill_check_url: Settings.skill_check_url
    }
  end
end
